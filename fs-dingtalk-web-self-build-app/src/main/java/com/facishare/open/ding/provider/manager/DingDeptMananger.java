package com.facishare.open.ding.provider.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDepartmentBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDeptDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaDeptDataMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/7/26 12:34
 * @Version 1.0
 */
@Slf4j
@Component
public class DingDeptMananger {
    @Autowired
    private OuterOaDepartmentBindManager departmentBindManager;
    
    @Autowired
    private OuterOaDeptDataManager deptDataManager;
    
    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    
    @Resource
    private OuterOaDeptDataMapper outerOaDeptDataMapper;

    /**
     * 将部门绑定实体转换为DeptVo
     * 不需要部门数据和企业账号，只使用绑定关系数据
     *
     * @param bindEntity 部门绑定实体
     * @return DeptVo实例
     */
    public DeptVo convertToDeptVo(OuterOaDepartmentBindEntity bindEntity) {
        if (bindEntity == null) {
            return null;
        }
        
        String fsEa = bindEntity.getFsEa();
        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);

        OuterOaDeptDataEntity dataEntity = deptDataManager.getEntity(ChannelEnum.dingding, bindEntity.getOutEa(), bindEntity.getAppId(), bindEntity.getOutDepId());

        return DeptVo.fromEntities(bindEntity, dataEntity, ei);
    }
    
    /**
     * 批量将部门绑定实体转换为DeptVo列表
     *
     * @param bindEntities 部门绑定实体列表
     * @return DeptVo实例列表
     */
    public List<DeptVo> batchConvertToDeptVo(List<OuterOaDepartmentBindEntity> bindEntities) {
        if (CollectionUtils.isEmpty(bindEntities)) {
            return new ArrayList<>();
        }
        
        return bindEntities.stream()
                .map(this::convertToDeptVo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 初始化部门映射
     *
     * @param list 部门列表
     * @param ei 企业ID
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 添加的记录数
     */
    public Integer initDeptManager(List<DeptVo> list, Integer ei, Integer userId, String appId) {
        if (CollectionUtils.isEmpty(list) || ei == null || appId == null) {
            log.warn("initDeptManager invalid params: list={}, ei={}, userId={}, appId={}", list, ei, userId, appId);
            return 0;
        }
        
        log.info("initDeptManager: list.size={}, ei={}, userId={}, appId={}", list.size(), ei, userId, appId);
        
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        
        // 通过fsEa和appId查询企业绑定关系的outEa
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (outEa == null) {
            log.warn("No enterprise bind entity found for ei={}, appId={}", ei, appId);
            return 0;
        }
        
        // 创建部门绑定关系实体列表和部门数据实体列表
        List<OuterOaDepartmentBindEntity> bindEntities = new ArrayList<>();
        List<OuterOaDeptDataEntity> dataEntities = new ArrayList<>();
        
        // 转换列表
        for (DeptVo dept : list) {
            // 转换部门绑定关系实体
            OuterOaDepartmentBindEntity bindEntity = dept.toBindEntity(dcId, appId, outEa, eieaConverter);
            bindEntities.add(bindEntity);
            
            // 转换部门数据实体
            OuterOaDeptDataEntity dataEntity = dept.toDataEntity(appId, outEa);
            dataEntities.add(dataEntity);
        }
        
        // 批量插入部门绑定关系
        int bindCount = departmentBindManager.batchInsert(bindEntities);
        
        // 批量插入部门数据
        int dataCount = deptDataManager.batchInsert(dataEntities);
        
        log.info("initDeptManager done: bindCount={}, dataCount={}", bindCount, dataCount);
        
        // 返回绑定关系的记录数
        return bindCount;
    }

    /**
     * 根据ei查询部门映射
     */
    public List<DeptVo> getDeptByEI(Integer ei, String appId) {
        if (ei == null || appId == null) {
            log.warn("getDeptByEI invalid params: ei={}, appId={}", ei, appId);
            return Collections.emptyList();
        }
        
        log.info("getDeptByEI: ei={}, appId={}", ei, appId);
        
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        
        // 查询部门绑定关系
        List<OuterOaDepartmentBindEntity> bindEntities = departmentBindManager.queryByAppId(ChannelEnum.dingding, fsEa, appId);
        if (CollectionUtils.isEmpty(bindEntities)) {
            log.warn("No bind entities found for ei={}, appId={}", ei, appId);
            return Collections.emptyList();
        }
        
        return batchConvertToDeptVo(bindEntities);
    }
    
//    /**
//     * 查询没有创建成功的部门
//     */
//    public List<DeptVo> findByNoCrm(Integer ei) {
//        return dingDeptDao.findByNoCrm(ei);
//    }

    /**
     * 查询部门总数
     * 
     * @param ei 企业ID
     * @param appId 应用ID
     * @return 部门总数
     */
    public Integer countByEi(Integer ei, String appId) {
        if (ei == null || appId == null) {
            log.warn("countByEi invalid params: ei={}, appId={}", ei, appId);
            return 0;
        }
        
        log.info("countByEi: ei={}, appId={}", ei, appId);
        
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        
        // 查询所有部门绑定关系并返回数量
        List<OuterOaDepartmentBindEntity> bindEntities = departmentBindManager.queryByAppId(
                ChannelEnum.dingding, 
                fsEa, 
                appId);
        
        return bindEntities.size();
    }

    /**
     * 添加部门
     *
     * @param vo    部门信息
     * @param appId
     * @return 添加的记录数
     */
    public Integer addDeptByEi(DeptVo vo, String appId) {
        if (vo == null || vo.getEi() == null || vo.getDingDeptId() == null || vo.getCrmDeptId() == null || appId == null) {
            log.warn("addDeptByEi invalid params: vo={}, appId={}", vo, appId);
            return 0;
        }
        
        log.info("addDeptByEi: vo={}, appId={}", vo, appId);
        
        String fsEa = eieaConverter.enterpriseIdToAccount(vo.getEi());
        
        // 通过fsEa和appId查询企业绑定关系的outEa
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (outEa == null) {
            log.warn("No enterprise bind entity found for ei={}, appId={}", vo.getEi(), appId);
            return 0;
        }
        // 创建部门数据实体
        OuterOaDeptDataEntity dataEntity = vo.toDataEntity(appId, outEa);

        // 插入部门绑定关系和部门数据
        int dataCount = deptDataManager.upsert(dataEntity);
        
        // 创建部门绑定关系实体
        if (Objects.nonNull(vo.getCrmDeptId()) && !Objects.equals(vo.getCrmDeptId(), 0)) {
            OuterOaDepartmentBindEntity bindEntity = vo.toBindEntity(dcId, appId, outEa, eieaConverter);
            int bindCount = departmentBindManager.upsert(bindEntity);
        }

        log.info("addDeptByEi done: dataCount={}", dataCount);
        
        return dataCount;
    }

    /**
     * 根据dingDeptID查询
     *
     * @param ei     企业ID
     * @param deptId 钉钉部门ID
     * @param appId
     * @return 部门VO
     */
    public DeptVo queryByDingId(Integer ei, Long deptId, String appId) {
        if (ei == null || deptId == null || appId == null) {
            log.warn("queryByDingId invalid params: ei={}, deptId={}, appId={}", ei, deptId, appId);
            return null;
        }
        
        log.info("queryByDingId: ei={}, deptId={}, appId={}", ei, deptId, appId);
        
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String outDeptIdStr = String.valueOf(deptId);
        
        // 使用辅助方法进行查询
        return queryDeptDetail(fsEa, outDeptIdStr, appId);
    }

    /**
     * 修改部门信息
     */
    public Result<Integer> updateDept(DeptVo vo, String appId) {
        if (vo == null || vo.getEi() == null || vo.getDingDeptId() == null || appId == null) {
            log.info("updateDept invalid params: vo={}, appId={}", vo, appId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        
        log.info("updateDept: vo={}, appId={}", vo, appId);
        
        Integer ei = vo.getEi();
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        
        // 获取outEa用于更新部门数据
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (outEa == null) {
            log.warn("No enterprise bind entity found for ei={}, appId={}", ei, appId);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }

        // 更新部门数据
        OuterOaDeptDataEntity dataEntity = vo.toDataEntity(appId, outEa);
        int dataCount = deptDataManager.upsert(dataEntity);

        log.info("updateDept done: dataCount={}", dataCount);

        if (!Objects.nonNull(vo.getCrmDeptId()) || Objects.equals(vo.getCrmDeptId(), 0)) {
            return Result.newSuccess(0);
        }

        // 更新绑定关系
        OuterOaDepartmentBindEntity bindEntity = vo.toBindEntity(dcId, appId, outEa, eieaConverter);
        final Integer upsert = departmentBindManager.upsert(bindEntity);
        return Result.newSuccess(upsert);

    }

    /**
     * 删除部门
     */
    public Result<Integer> deleteDept(Integer ei, Long dingDeptId, String appId) {
        if (dingDeptId == null || ei == null || appId == null) {
            log.info("deleteDept invalid params: ei={}, dingDeptId={}, appId={}", ei, dingDeptId, appId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        
        log.info("deleteDept: ei={}, dingDeptId={}, appId={}", ei, dingDeptId, appId);
        
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String outDeptIdStr = String.valueOf(dingDeptId);
        
        // 获取outEa用于删除部门数据
        String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (outEa == null) {
            log.warn("deleteDept: could not find outEa for ei={}, appId={}", ei, appId);
            return Result.newError(ResultCode.SYSTEM_ERROR);
        }
        
        // 删除部门绑定关系 - 修复参数不匹配问题
        int bindCount = departmentBindManager.deleteDepartmentBinds(
                ChannelEnum.dingding, 
                fsEa, 
                outEa, 
                appId, 
                Collections.singletonList(outDeptIdStr),
                null); // 不按fsDepId删除

//        // 删除部门数据 - 使用LambdaQueryWrapper构建删除条件
        LambdaQueryWrapper<OuterOaDeptDataEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaDeptDataEntity::getChannel, ChannelEnum.dingding)
               .eq(OuterOaDeptDataEntity::getOutEa, outEa)
               .eq(OuterOaDeptDataEntity::getAppId, appId)
               .in(OuterOaDeptDataEntity::getOutDeptId, Collections.singletonList(outDeptIdStr));

        int dataCount = outerOaDeptDataMapper.delete(wrapper);
        log.info("deleteDept done: bindCount={} dataCount={}", bindCount, dataCount);

        return Result.newSuccess(bindCount);
    }

    /**
     * 按照名字查询部门信息
     * 
     * @param ei 企业ID
     * @param deptName 部门名称
     * @param appId 应用ID
     * @return 匹配的部门列表
     */
    public List<DeptVo> getByEIAndName(Integer ei, String deptName, String appId) {
        if (ei == null || StringUtils.isEmpty(deptName) || appId == null) {
            log.warn("getByEIAndName invalid params: ei={}, deptName={}, appId={}", ei, deptName, appId);
            return Collections.emptyList();
        }
        
        log.info("getByEIAndName: ei={}, deptName={}, appId={}", ei, deptName, appId);
        
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        
        // 获取outEa
        String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (outEa == null) {
            log.warn("No enterprise bind entity found for ei={}, appId={}", ei, appId);
            return Collections.emptyList();
        }
        
        // 按部门名称查询部门数据
        List<OuterOaDeptDataEntity> dataEntities = outerOaDeptDataMapper.getDeptByName(
                ChannelEnum.dingding,
                outEa,
                appId,
                deptName);
                
        if (CollectionUtils.isEmpty(dataEntities)) {
            return Collections.emptyList();
        }
        
        // 获取部门ID列表
        List<String> outDeptIds = dataEntities.stream()
                .map(OuterOaDeptDataEntity::getOutDeptId)
                .collect(Collectors.toList());
                
        // 查询部门绑定关系
        List<OuterOaDepartmentBindEntity> bindEntities = departmentBindManager.batchGetByOutDepIds(
                ChannelEnum.dingding,
                fsEa,
                appId,
                outDeptIds);
                
        if (CollectionUtils.isEmpty(bindEntities)) {
            return Collections.emptyList();
        }
        
        // 构建数据映射关系
        Map<String, OuterOaDeptDataEntity> dataMap = dataEntities.stream()
                .collect(Collectors.toMap(OuterOaDeptDataEntity::getOutDeptId, e -> e, (e1, e2) -> e1));
                
        // 转换为DeptVo列表
        return bindEntities.stream()
                .map(bindEntity -> {
                    String outDeptId = bindEntity.getOutDepId();
                    OuterOaDeptDataEntity dataEntity = dataMap.get(outDeptId);
                    return DeptVo.fromEntities(bindEntity, dataEntity, ei);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

//    /**
//     * 更新部门ei(处理脏数据）
//     *
//     * @param ei 企业ID
//     * @param ids 部门ID
//     * @param appId 应用ID
//     * @return 更新的记录数
//     */
//    public Integer fixDept(Integer ei, Integer ids, String appId) {
//        if (ei == null || ids == null || appId == null) {
//            log.warn("fixDept invalid params: ei={}, ids={}, appId={}", ei, ids, appId);
//            return 0;
//        }
//
//        log.info("fixDept: ei={}, ids={}, appId={}", ei, ids, appId);
//
//        // 具体实现需要根据原来fixDept的逻辑适配到新的数据模型
//        // 这里保留原来的实现，但需要在调用方传入appId参数
//        return dingDeptDao.fixDept(ei, ids);
//    }

    /**
     * 联合查询部门信息
     */
    public DeptVo queryDeptDetail(String fsEa, String outDeptIdStr, String appId) {
        // 查询部门绑定关系
        OuterOaDepartmentBindEntity bindEntity = departmentBindManager.queryByOutDepId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                outDeptIdStr);
        
        // 使用DeptVo的静态方法合并数据
        return convertToDeptVo(bindEntity);
    }

    /**
     * 获取部门名称
     * @param ei 企业ID
     * @param deptId 钉钉部门ID
     * @param appId 应用ID
     * @return 部门名称
     */
    public String getDeptById(Integer ei, Long deptId, String appId) {
        if (deptId == null || ei == null || appId == null) {
            log.warn("getDeptById invalid params: ei={}, deptId={}, appId={}", ei, deptId, appId);
            return null;
        }
        
        log.info("getDeptById: ei={}, deptId={}, appId={}", ei, deptId, appId);
        
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String outDeptIdStr = String.valueOf(deptId);
        
        // 使用辅助方法进行查询
        DeptVo deptVo = queryDeptDetail(fsEa, outDeptIdStr, appId);
        return deptVo != null ? deptVo.getName() : null;
    }
}
