package com.facishare.open.ding.provider.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.provider.enums.DingTodoTypeEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeDataMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaMessageBindMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.object.BaseOuterEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DingTodoManager {

    @Autowired
    private OuterOaEmployeeBindMapper outerOaEmployeeBindMapper;

    @Autowired
    private OuterOaMessageBindMapper outerOaMessageBindMapper;

    @Autowired
    private OuterOaEmployeeDataMapper outerOaEmployeeDataMapper;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaEnterpriseBindMapper outerOaEnterpriseBindMapper;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    public List<String> queryUserIds(int ei, List<Integer> receiverIds, String appId) {
        if (CollectionUtils.isEmpty(receiverIds)) {
            return new LinkedList<>();
        }

        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        // 查询PG数据
        List<String> userIds = new LinkedList<>();
        for (Integer receiverId : receiverIds) {
            LambdaQueryWrapper<OuterOaEmployeeBindEntity> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(OuterOaEmployeeBindEntity::getChannel, ChannelEnum.dingding);
            queryWrapper.eq(OuterOaEmployeeBindEntity::getFsEa, ea);
            queryWrapper.eq(OuterOaEmployeeBindEntity::getAppId, appId);
            queryWrapper.eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal);
            queryWrapper.eq(OuterOaEmployeeBindEntity::getFsEmpId, String.valueOf(receiverId));
            OuterOaEmployeeBindEntity bindEntity = outerOaEmployeeBindMapper.selectOne(queryWrapper);
            if (Objects.nonNull(bindEntity)) {
                // 查询员工信息,只会返回一个
                userIds.add(bindEntity.getOutEmpId());
            }
        }

        return userIds;
    }

    public String queryUserId(int ei, int receiverId, String appId) {
        List<Integer> receiverIds = new LinkedList<>();
        receiverIds.add(receiverId);
        List<String> userIds = queryUserIds(ei, receiverIds, appId);
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        return userIds.get(0);
    }

    public int getSourceCount(int ei, String sourceId, String appId) {
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        LambdaQueryWrapper<OuterOaMessageBindEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaMessageBindEntity::getFsEa, fsEa)
                .eq(OuterOaMessageBindEntity::getSourceId, sourceId)
                .eq(OuterOaMessageBindEntity::getChannel, ChannelEnum.dingding)
                .eq(StringUtils.isNotEmpty(appId), OuterOaMessageBindEntity::getAppId, appId);
        return outerOaMessageBindMapper.selectCount(queryWrapper).intValue();
    }

    public List<DingTaskVo> getDingTaskVo(int ei, String sourceId, String appId) {
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        LambdaQueryWrapper<OuterOaMessageBindEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaMessageBindEntity::getFsEa, fsEa)
                .eq(OuterOaMessageBindEntity::getSourceId, sourceId)
                .eq(OuterOaMessageBindEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaMessageBindEntity::getStatus, OuterOaMessageBindStatusEnum.pending)
                .eq(OuterOaMessageBindEntity::getMessageType, OuterOaMessageBindMsgTypeEnum.todo)
                .eq(StringUtils.isNotEmpty(appId), OuterOaMessageBindEntity::getAppId, appId);
        List<OuterOaMessageBindEntity> entities = outerOaMessageBindMapper.selectList(queryWrapper);
        return entities.stream()
                .map(this::convertToTaskVo)
                .collect(Collectors.toList());
    }

    public int insertSource(DingTaskVo dingTaskVo, String appId) {
        OuterOaMessageBindEntity entity = convertToEntity(dingTaskVo, appId);
        return outerOaMessageBindMapper.insert(entity);
    }

    public int updateStatus(int status, int ei, String sourceId, int employeeId, String appId) {
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String dingEmployeeId = queryUserId(ei, employeeId, appId);
        if (StringUtils.isEmpty(dingEmployeeId)) {
            return 0;
        }

        LambdaUpdateWrapper<OuterOaMessageBindEntity> update = new LambdaUpdateWrapper<>();
        update.eq(OuterOaMessageBindEntity::getFsEa, fsEa)
                .eq(OuterOaMessageBindEntity::getSourceId, sourceId)
                .eq(OuterOaMessageBindEntity::getOutUserId, dingEmployeeId)
                .eq(OuterOaMessageBindEntity::getMessageType, OuterOaMessageBindMsgTypeEnum.todo)
                .eq(OuterOaMessageBindEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaMessageBindEntity::getStatus, OuterOaMessageBindStatusEnum.pending)
                .eq(StringUtils.isNotEmpty(appId), OuterOaMessageBindEntity::getAppId, appId)
                .set(OuterOaMessageBindEntity::getStatus, status == 1 ? OuterOaMessageBindStatusEnum.pending : OuterOaMessageBindStatusEnum.approved)
                .set(OuterOaMessageBindEntity::getUpdateTime, System.currentTimeMillis());

        return outerOaMessageBindMapper.update(null, update);
    }

    public int updateExecutor(List<Integer> deleteEmployeeIds, int ei, String sourceId, String appId) {
        if (CollectionUtils.isEmpty(deleteEmployeeIds)) {
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        List<String> dingEmployeeIds = new ArrayList<>();
        for (Integer employeeId : deleteEmployeeIds) {
            String dingEmployeeId = queryUserId(ei, employeeId, appId);
            if (StringUtils.isNotEmpty(dingEmployeeId)) {
                dingEmployeeIds.add(dingEmployeeId);
            }
        }

        if (CollectionUtils.isEmpty(dingEmployeeIds)) {
            return 0;
        }

        return outerOaMessageBindMapper.updateExecutorStatus(
                fsEa,
                sourceId,
                dingEmployeeIds,
                OuterOaMessageBindMsgTypeEnum.todo.name(),
                ChannelEnum.dingding.name(),
                OuterOaMessageBindStatusEnum.pending.name(),
                OuterOaMessageBindStatusEnum.deleted.name(),
                appId,
                System.currentTimeMillis()
        );
    }

    /**
     * 根据钉钉员工ID获取任务ID列表
     */
    public List<String> getTaskIdByDingEmpId(int ei, String sourceId, String dingEmployeeId, String appId,OuterOaMessageBindMsgTypeEnum msgTypeEnum) {
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        LambdaQueryWrapper<OuterOaMessageBindEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaMessageBindEntity::getFsEa, fsEa)
                .eq(OuterOaMessageBindEntity::getSourceId, sourceId)
                .eq(OuterOaMessageBindEntity::getOutUserId, dingEmployeeId)
                .eq(OuterOaMessageBindEntity::getMessageType, msgTypeEnum)
                .eq(OuterOaMessageBindEntity::getChannel, ChannelEnum.dingding)
                .eq(StringUtils.isNotEmpty(appId), OuterOaMessageBindEntity::getAppId, appId);
        List<OuterOaMessageBindEntity> entities = outerOaMessageBindMapper.selectList(queryWrapper);
        return entities.stream()
                .map(OuterOaMessageBindEntity::getTaskId)
                .collect(Collectors.toList());
    }

    /**
     * 根据任务ID更新状态
     */
    public int updateByTaskId(int status, int ei, String taskId, String dingEmployeeId, String appId) {
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        LambdaQueryWrapper<OuterOaMessageBindEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OuterOaMessageBindEntity::getFsEa, fsEa)
                .eq(OuterOaMessageBindEntity::getTaskId, taskId)
                .eq(OuterOaMessageBindEntity::getOutUserId, dingEmployeeId)
                .eq(OuterOaMessageBindEntity::getMessageType, OuterOaMessageBindMsgTypeEnum.todo)
                .eq(OuterOaMessageBindEntity::getChannel, ChannelEnum.dingding)
                .eq(StringUtils.isNotEmpty(appId), OuterOaMessageBindEntity::getAppId, appId);
        OuterOaMessageBindEntity entity = outerOaMessageBindMapper.selectOne(queryWrapper);
        if (entity == null) {
            return 0;
        }

        entity.setStatus(status == 1 ? OuterOaMessageBindStatusEnum.pending : OuterOaMessageBindStatusEnum.approved);
        entity.setUpdateTime(System.currentTimeMillis());
        return outerOaMessageBindMapper.updateById(entity);
    }

    /**
     * 将OuterOaMessageBindEntity转换为DingTaskVo
     */
    private DingTaskVo convertToTaskVo(OuterOaMessageBindEntity entity) {
        if (entity == null) {
            return null;
        }

        final OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.queryNormalByFsEaAndOutEmpId(ChannelEnum.dingding, entity.getFsEa(), entity.getAppId(), entity.getOutUserId());

        DingTaskVo vo = new DingTaskVo();
        vo.setEmployeeId(Objects.isNull(employeeBindEntity) || Objects.isNull(employeeBindEntity.getFsEmpId()) ? null : Integer.valueOf(employeeBindEntity.getFsEmpId()));
        vo.setEi(eieaConverter.enterpriseAccountToId(entity.getFsEa()));
        vo.setSourceId(entity.getSourceId());
        vo.setTaskId(entity.getTaskId());
        vo.setDingEmployeeId(entity.getOutUserId());
        vo.setMessageType(Objects.equals(entity.getMessageType(), OuterOaMessageBindMsgTypeEnum.app) ?
                DingTodoTypeEnum.DING_WORK.name() : DingTodoTypeEnum.DING_TODO.name());
        vo.setStatus(Objects.equals(entity.getStatus(), OuterOaMessageBindStatusEnum.pending) ? 1 : 0);
        vo.setDataCenterId(entity.getDcId());
        return vo;
    }

    /**
     * 将DingTaskVo转换为OuterOaMessageBindEntity
     */
    private OuterOaMessageBindEntity convertToEntity(DingTaskVo vo, String appId) {
        if (vo == null) {
            return null;
        }

        final String fsEa = eieaConverter.enterpriseIdToAccount(vo.getEi());
        final OuterOaEnterpriseBindEntity entitiesByFsEaAndAppId = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        OuterOaMessageBindEntity entity = new OuterOaMessageBindEntity();
        entity.setOutEa(entitiesByFsEaAndAppId.getOutEa());
        entity.setChannel(ChannelEnum.dingding);
        entity.setDcId(entitiesByFsEaAndAppId.getId());
        entity.setFsEa(fsEa);
        entity.setOutUserId(vo.getDingEmployeeId());
        entity.setSourceId(vo.getSourceId());
        entity.setTaskId(vo.getTaskId());
        entity.setAppId(appId);
        entity.setEventType(OuterOaMessageBindEventTypeEnum.commonMsg);

        // 根据消息类型设置messageType
        OuterOaMessageBindMsgTypeEnum msgTypeEnum = Objects.equals(DingTodoTypeEnum.DING_WORK.name(), vo.getMessageType()) ?
                OuterOaMessageBindMsgTypeEnum.app : OuterOaMessageBindMsgTypeEnum.todo;
        entity.setMessageType(msgTypeEnum);

        // 根据状态设置status
        entity.setStatus(Objects.equals(vo.getStatus(), 1) ?
                OuterOaMessageBindStatusEnum.pending : OuterOaMessageBindStatusEnum.approved);

        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());

        return entity;
    }
}
