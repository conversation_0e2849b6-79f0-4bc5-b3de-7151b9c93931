package com.facishare.open.ding.transfer.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.provider.dao.DingTaskDao;
import com.facishare.open.ding.provider.enums.DingTodoTypeEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaMessageBindMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindEventTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindStatusEnum;
import com.fxiaoke.api.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 钉钉任务数据迁移处理器
 * <AUTHOR>
 * @date 2024/1/22 15:46:25
 */
@Component
public class DingTalkSelfBuildTaskHandler extends DingTalkSelfBuildHandler<DingTaskVo, OuterOaMessageBindEntity> {

    @Autowired
    private DingTaskDao dingTaskDao;

    @Autowired
    private OuterOaMessageBindMapper outerOaMessageBindMapper;

    @Autowired
    private EIEAConverter eieaConverter;

    @Override
    protected void update(int enterpriseId, DingTaskVo sourceData, OuterOaMessageBindEntity targetData) {
        final List<OuterOaEnterpriseBindEntity> list = appAuthEntityCache.get(sourceData.getEi());
        list.forEach(OuterOaEnterpriseBindEntity -> {
            final OuterOaMessageBindEntity entity = convert2OuterOaMessageBindEntity(sourceData, targetData, OuterOaEnterpriseBindEntity);
            if (Objects.isNull(targetData)) {
                outerOaMessageBindMapper.insert(entity);
            } else {
                outerOaMessageBindMapper.updateById(entity);
            }
        });
    }

    private OuterOaMessageBindEntity convert2OuterOaMessageBindEntity(DingTaskVo sourceData, OuterOaMessageBindEntity targetData, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {
        final OuterOaMessageBindEntity entity = new OuterOaMessageBindEntity();
        entity.setFsEa(outerOaEnterpriseBindEntity.getFsEa());
        entity.setOutEa(outerOaEnterpriseBindEntity.getOutEa());
//        entity.setTemplateId();
        entity.setId(Objects.isNull(targetData) ? IdGenerator.get() : targetData.getId());
        entity.setChannel(ChannelEnum.dingding);
        entity.setDcId(outerOaEnterpriseBindEntity.getId());
        entity.setAppId(outerOaEnterpriseBindEntity.getAppId());
        final OuterOaMessageBindMsgTypeEnum msgTypeEnum = Objects.equals(DingTodoTypeEnum.DING_WORK.name(), sourceData.getMessageType()) ? OuterOaMessageBindMsgTypeEnum.app : OuterOaMessageBindMsgTypeEnum.todo;
        entity.setMessageType(msgTypeEnum);
        entity.setOutUserId(sourceData.getDingEmployeeId());
        entity.setEventType(OuterOaMessageBindEventTypeEnum.commonMsg);
        entity.setStatus(Objects.equals(sourceData.getStatus(), 1) ? OuterOaMessageBindStatusEnum.pending : OuterOaMessageBindStatusEnum.approved);
        entity.setSourceId(sourceData.getSourceId());
        entity.setTaskId(sourceData.getTaskId());
        entity.setFsEa(eieaConverter.enterpriseIdToAccount(sourceData.getEi()));
        entity.setCreateTime(Objects.isNull(targetData) ? System.currentTimeMillis() : targetData.getCreateTime());
        entity.setUpdateTime(Objects.isNull(targetData) ? System.currentTimeMillis() : targetData.getUpdateTime());
        return entity;
    }

    @Override
    protected boolean checkDataEquals(DingTaskVo sourceData, OuterOaMessageBindEntity targetData) {
        final OuterOaMessageBindMsgTypeEnum msgTypeEnum = Objects.equals(DingTodoTypeEnum.DING_WORK.name(), sourceData.getMessageType()) ? OuterOaMessageBindMsgTypeEnum.app : OuterOaMessageBindMsgTypeEnum.todo;
        return Objects.equals(targetData.getMessageType(), msgTypeEnum) &&
                Objects.equals(targetData.getEventType(), OuterOaMessageBindEventTypeEnum.commonMsg) &&
                Objects.equals(targetData.getStatus(), Objects.equals(sourceData.getStatus(), 1) ? OuterOaMessageBindStatusEnum.pending : OuterOaMessageBindStatusEnum.approved) &&
                Objects.equals(targetData.getTaskId(), sourceData.getTaskId()) &&
                Objects.equals(targetData.getFsEa(), eieaConverter.enterpriseIdToAccount(sourceData.getEi()));
    }

    @Override
    protected OuterOaMessageBindEntity getTargetData(int enterpriseId, DingTaskVo k) {
        final OuterOaMessageBindEntity entity = new OuterOaMessageBindEntity();
        entity.setChannel(ChannelEnum.dingding);
        entity.setDcId(k.getDataCenterId());
        entity.setFsEa(eieaConverter.enterpriseIdToAccount(k.getEi()));
        entity.setOutUserId(k.getDingEmployeeId());
        entity.setSourceId(k.getSourceId());
        LambdaQueryWrapper<OuterOaMessageBindEntity> lambdaQueryWrapper = Wrappers.lambdaQuery(entity);
        return outerOaMessageBindMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    protected List<DingTaskVo> getSourceDataPage(Integer enterpriseId, DingTaskVo maxId) {
        Long id = Objects.isNull(maxId) ? null : maxId.getId();
        return dingTaskDao.pageById(enterpriseId, id, 1000);
    }
}