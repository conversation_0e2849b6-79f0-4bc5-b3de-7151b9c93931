<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-3.1.xsd">

       <aop:aspectj-autoproxy/>

       <!-- 日志拦截器 -->
       <bean id="logInterceptor" class="com.facishare.open.ding.common.interceptor.LogInterceptor"/>
       <!-- 异常的统一处理 拦截器 -->
       <bean id="apiExceptionInterceptor" class="com.facishare.open.ding.common.interceptor.ApiExceptionInterceptor"/>

       <bean id="crmRateLimiterAspect" class="com.facishare.open.ding.web.aop.CrmRateLimiterAspect"/>

       <aop:config>
              <aop:pointcut id="exceptionMethods" expression="execution(* com.facishare.open.ding.web.controller.*.*(..))"/>
              <aop:pointcut id="logMethods" expression="execution(* com.facishare.open.ding.web.controller.*.*(..)) || @annotation(org.springframework.web.bind.annotation.RestController)"/>
              <aop:pointcut id="crmRateLimiterMethods"
                            expression="(execution(* com.facishare.userlogin.api.service.SSOLoginService.*(..)) or
                            execution(* com.facishare.uc.api.service.EnterpriseEditionService.*(..)))"/>
              <aop:advisor order="0" pointcut-ref="logMethods" advice-ref="logInterceptor"/>
              <aop:advisor order="1" pointcut-ref="exceptionMethods" advice-ref="apiExceptionInterceptor"/>
              <aop:advisor order="2" pointcut-ref="crmRateLimiterMethods" advice-ref="crmRateLimiterAspect"/>
       </aop:config>

       <!-- fs-dingtalk-provider spring-aop.xml-->
<!--       <aop:aspectj-autoproxy/>-->

       <!-- 日志拦截器 -->
<!--       <bean id="logInterceptor" class="com.facishare.open.ding.common.interceptor.LogInterceptor"/>-->

       <!-- 异常的统一处理 拦截器 -->
<!--       <bean id="apiExceptionInterceptor" class="com.facishare.open.ding.common.interceptor.ApiExceptionInterceptor"/>-->

<!--       <bean id="crmRateLimiterAspect" class="com.facishare.open.ding.provider.aop.CrmRateLimiterAspect"/>-->

       <aop:config>
              <aop:pointcut id="serviceMethods" expression="!execution(* com.facishare.open.ding.api.service.ObjectMappingService.batchCreateFxEmployee(..))
              and !execution(* com.facishare.open.ding.api.service.RedisDingService.*(..))
              and execution(* com.facishare.open.ding.api.service.*.*(..)))"/>
              <aop:pointcut id="serviceAndManagerMethods" expression="!execution(* com.facishare.open.ding.provider.manager.DingDeptMananger.*(..)) and execution(* com.facishare.open.ding.provider..*.*(..))"/>
              <aop:pointcut id="crmRateLimiterMethods"
                            expression="(execution(* com.facishare.open.app.center.api.service.QueryAppAdminService.*(..)) or
                            execution(* com.facishare.open.app.center.api.service.OpenAppAdminService.*(..)) or
                            execution(* com.facishare.organization.api.service.DepartmentProviderService.*(..)) or
                            execution(* com.facishare.organization.api.service.EmployeeProviderService.*(..)) or
                            execution(* com.facishare.organization.adapter.api.service.EmployeeService.*(..)) or
                            execution(* com.facishare.organization.adapter.api.config.service.EnterpriseConfigService.*(..)) or
                            execution(* com.facishare.open.ding.provider.crm.CrmRestManager.*(..)))"/>
              <aop:advisor order="0" pointcut-ref="serviceAndManagerMethods" advice-ref="logInterceptor"/>
              <aop:advisor order="1" pointcut-ref="serviceMethods" advice-ref="apiExceptionInterceptor"/>
              <aop:advisor order="2" pointcut-ref="crmRateLimiterMethods" advice-ref="crmRateLimiterAspect"/>
       </aop:config>
       <!-- fs-dingtalk-provider spring-aop.xml-->

</beans>