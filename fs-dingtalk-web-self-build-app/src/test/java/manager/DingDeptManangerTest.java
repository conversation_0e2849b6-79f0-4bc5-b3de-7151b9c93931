package manager;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.provider.manager.DingDeptMananger;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDepartmentBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDeptDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaDeptDataMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;

import base.BaseAbstractTest;
import lombok.extern.slf4j.Slf4j;

/**
 * DingDeptMananger单元测试类
 */
@Slf4j
public class DingDeptManangerTest extends BaseAbstractTest {

    @InjectMocks
    @Autowired
    private DingDeptMananger dingDeptMananger;

    @Mock
    private OuterOaDepartmentBindManager departmentBindManager;

    @Mock
    private OuterOaDeptDataManager deptDataManager;

    @Mock
    private EIEAConverter eieaConverter;

    @Mock
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Mock
    private OuterOaDeptDataMapper outerOaDeptDataMapper;

    private static final Integer TEST_EI = 123456;
    private static final String TEST_APP_ID = "test_app_id";
    private static final String TEST_FS_EA = "EA123456";
    private static final String TEST_OUT_EA = "test_out_ea";
    private static final Long TEST_DEPT_ID = 789L;
    private static final String TEST_DEPT_NAME = "测试部门";

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);

        // 设置通用Mock行为
        when(eieaConverter.enterpriseIdToAccount(TEST_EI)).thenReturn(TEST_FS_EA);
        when(eieaConverter.enterpriseAccountToId(TEST_FS_EA)).thenReturn(TEST_EI);
        when(outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, TEST_FS_EA, TEST_APP_ID)).thenReturn(TEST_OUT_EA);
    }

    /**
     * 测试getByEIAndName方法
     */
    @Test
    public void testGetByEIAndName() {
        // 准备测试数据
        List<OuterOaDeptDataEntity> dataEntities = new ArrayList<>();
        OuterOaDeptDataEntity dataEntity = new OuterOaDeptDataEntity();
        dataEntity.setChannel(ChannelEnum.dingding);
        dataEntity.setOutEa(TEST_OUT_EA);
        dataEntity.setAppId(TEST_APP_ID);
        dataEntity.setOutDeptId(String.valueOf(TEST_DEPT_ID));
        dataEntity.setDeptName(TEST_DEPT_NAME);
        dataEntity.setCreateTime(System.currentTimeMillis());
        dataEntity.setUpdateTime(System.currentTimeMillis());
        dataEntities.add(dataEntity);

        List<OuterOaDepartmentBindEntity> bindEntities = new ArrayList<>();
        OuterOaDepartmentBindEntity bindEntity = new OuterOaDepartmentBindEntity();
        bindEntity.setChannel(ChannelEnum.dingding);
        bindEntity.setFsEa(TEST_FS_EA);
        bindEntity.setOutEa(TEST_OUT_EA);
        bindEntity.setAppId(TEST_APP_ID);
        bindEntity.setOutDepId(String.valueOf(TEST_DEPT_ID));
        bindEntity.setFsDepId("1001");
        bindEntity.setBindStatus(BindStatusEnum.normal);
        bindEntity.setCreateTime(System.currentTimeMillis());
        bindEntity.setUpdateTime(System.currentTimeMillis());
        bindEntities.add(bindEntity);

        // 设置Mock行为
        when(outerOaDeptDataMapper.getDeptByName(ChannelEnum.dingding, TEST_OUT_EA, TEST_APP_ID, TEST_DEPT_NAME)).thenReturn(dataEntities);
        when(departmentBindManager.batchGetByOutDepIds(eq(ChannelEnum.dingding), eq(TEST_FS_EA), eq(TEST_APP_ID), anyList())).thenReturn(bindEntities);
        when(deptDataManager.getEntity(ChannelEnum.dingding, TEST_OUT_EA, TEST_APP_ID, String.valueOf(TEST_DEPT_ID))).thenReturn(dataEntity);

        // 调用测试方法
        List<DeptVo> result = dingDeptMananger.getByEIAndName(TEST_EI, TEST_DEPT_NAME, TEST_APP_ID);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertFalse("结果列表不应为空", result.isEmpty());
        assertEquals("结果数量应为1", 1, result.size());
        assertEquals("部门名称应匹配", TEST_DEPT_NAME, result.get(0).getName());
        assertEquals("部门ID应匹配", TEST_DEPT_ID, result.get(0).getDingDeptId());
        assertEquals("企业ID应匹配", TEST_EI, result.get(0).getEi());
    }

    /**
     * 测试参数验证：当企业ID为空时
     */
    @Test
    public void testGetByEIAndNameWithNullEi() {
        List<DeptVo> result = dingDeptMananger.getByEIAndName(null, TEST_DEPT_NAME, TEST_APP_ID);
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试参数验证：当部门名称为空时
     */
    @Test
    public void testGetByEIAndNameWithNullName() {
        List<DeptVo> result = dingDeptMananger.getByEIAndName(TEST_EI, null, TEST_APP_ID);
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试参数验证：当应用ID为空时
     */
    @Test
    public void testGetByEIAndNameWithNullAppId() {
        List<DeptVo> result = dingDeptMananger.getByEIAndName(TEST_EI, TEST_DEPT_NAME, null);
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试convertToDeptVo方法
     */
    @Test
    public void testConvertToDeptVo() {
        // 准备测试数据
        OuterOaDepartmentBindEntity bindEntity = new OuterOaDepartmentBindEntity();
        bindEntity.setChannel(ChannelEnum.dingding);
        bindEntity.setFsEa(TEST_FS_EA);
        bindEntity.setOutEa(TEST_OUT_EA);
        bindEntity.setAppId(TEST_APP_ID);
        bindEntity.setOutDepId(String.valueOf(TEST_DEPT_ID));
        bindEntity.setFsDepId("1001");
        bindEntity.setBindStatus(BindStatusEnum.normal);
        bindEntity.setCreateTime(System.currentTimeMillis());
        bindEntity.setUpdateTime(System.currentTimeMillis());

        OuterOaDeptDataEntity dataEntity = new OuterOaDeptDataEntity();
        dataEntity.setChannel(ChannelEnum.dingding);
        dataEntity.setOutEa(TEST_OUT_EA);
        dataEntity.setAppId(TEST_APP_ID);
        dataEntity.setOutDeptId(String.valueOf(TEST_DEPT_ID));
        dataEntity.setDeptName(TEST_DEPT_NAME);
        dataEntity.setCreateTime(System.currentTimeMillis());
        dataEntity.setUpdateTime(System.currentTimeMillis());

        // 设置Mock行为
        when(deptDataManager.getEntity(ChannelEnum.dingding, bindEntity.getOutEa(), bindEntity.getAppId(), bindEntity.getOutDepId())).thenReturn(dataEntity);

        // 调用测试方法
        DeptVo result = dingDeptMananger.convertToDeptVo(bindEntity);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertEquals("部门名称应匹配", TEST_DEPT_NAME, result.getName());
        assertEquals("部门ID应匹配", TEST_DEPT_ID, result.getDingDeptId());
        assertEquals("企业ID应匹配", TEST_EI, result.getEi());
    }

    /**
     * 测试batchConvertToDeptVo方法
     */
    @Test
    public void testBatchConvertToDeptVo() {
        // 准备测试数据
        List<OuterOaDepartmentBindEntity> bindEntities = new ArrayList<>();
        OuterOaDepartmentBindEntity bindEntity = new OuterOaDepartmentBindEntity();
        bindEntity.setChannel(ChannelEnum.dingding);
        bindEntity.setFsEa(TEST_FS_EA);
        bindEntity.setOutEa(TEST_OUT_EA);
        bindEntity.setAppId(TEST_APP_ID);
        bindEntity.setOutDepId(String.valueOf(TEST_DEPT_ID));
        bindEntity.setFsDepId("1001");
        bindEntity.setBindStatus(BindStatusEnum.normal);
        bindEntity.setCreateTime(System.currentTimeMillis());
        bindEntity.setUpdateTime(System.currentTimeMillis());
        bindEntities.add(bindEntity);

        OuterOaDeptDataEntity dataEntity = new OuterOaDeptDataEntity();
        dataEntity.setChannel(ChannelEnum.dingding);
        dataEntity.setOutEa(TEST_OUT_EA);
        dataEntity.setAppId(TEST_APP_ID);
        dataEntity.setOutDeptId(String.valueOf(TEST_DEPT_ID));
        dataEntity.setDeptName(TEST_DEPT_NAME);
        dataEntity.setCreateTime(System.currentTimeMillis());
        dataEntity.setUpdateTime(System.currentTimeMillis());

        // 设置Mock行为
        when(deptDataManager.getEntity(ChannelEnum.dingding, bindEntity.getOutEa(), bindEntity.getAppId(), bindEntity.getOutDepId())).thenReturn(dataEntity);

        // 调用测试方法
        List<DeptVo> result = dingDeptMananger.batchConvertToDeptVo(bindEntities);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertFalse("结果列表不应为空", result.isEmpty());
        assertEquals("结果数量应为1", 1, result.size());
        assertEquals("部门名称应匹配", TEST_DEPT_NAME, result.get(0).getName());
        assertEquals("部门ID应匹配", TEST_DEPT_ID, result.get(0).getDingDeptId());
        assertEquals("企业ID应匹配", TEST_EI, result.get(0).getEi());
    }

    /**
     * 测试batchConvertToDeptVo方法：当输入为空列表时
     */
    @Test
    public void testBatchConvertToDeptVoWithEmptyList() {
        List<DeptVo> result = dingDeptMananger.batchConvertToDeptVo(Collections.emptyList());
        assertNotNull("结果不应为空", result);
        assertTrue("结果列表应为空列表", result.isEmpty());
    }

    /**
     * 测试getDeptById方法
     */
    @Test
    public void testGetDeptById() {
        // 准备测试数据
        OuterOaDepartmentBindEntity bindEntity = new OuterOaDepartmentBindEntity();
        bindEntity.setChannel(ChannelEnum.dingding);
        bindEntity.setFsEa(TEST_FS_EA);
        bindEntity.setOutEa(TEST_OUT_EA);
        bindEntity.setAppId(TEST_APP_ID);
        bindEntity.setOutDepId(String.valueOf(TEST_DEPT_ID));
        bindEntity.setFsDepId("1001");
        bindEntity.setBindStatus(BindStatusEnum.normal);
        bindEntity.setCreateTime(System.currentTimeMillis());
        bindEntity.setUpdateTime(System.currentTimeMillis());

        OuterOaDeptDataEntity dataEntity = new OuterOaDeptDataEntity();
        dataEntity.setChannel(ChannelEnum.dingding);
        dataEntity.setOutEa(TEST_OUT_EA);
        dataEntity.setAppId(TEST_APP_ID);
        dataEntity.setOutDeptId(String.valueOf(TEST_DEPT_ID));
        dataEntity.setDeptName(TEST_DEPT_NAME);
        dataEntity.setCreateTime(System.currentTimeMillis());
        dataEntity.setUpdateTime(System.currentTimeMillis());

        // 设置Mock行为
        when(departmentBindManager.queryByOutDepId(eq(ChannelEnum.dingding), eq(TEST_FS_EA), eq(TEST_APP_ID), eq(String.valueOf(TEST_DEPT_ID)))).thenReturn(bindEntity);
        when(deptDataManager.getEntity(ChannelEnum.dingding, bindEntity.getOutEa(), bindEntity.getAppId(), bindEntity.getOutDepId())).thenReturn(dataEntity);

        // 调用测试方法
        String result = dingDeptMananger.getDeptById(TEST_EI, TEST_DEPT_ID, TEST_APP_ID);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertEquals("部门名称应匹配", TEST_DEPT_NAME, result);
    }

    /**
     * 测试getDeptById方法：当参数为空时
     */
    @Test
    public void testGetDeptByIdWithNullParams() {
        assertNull("部门ID为空时应返回null", dingDeptMananger.getDeptById(TEST_EI, null, TEST_APP_ID));
        assertNull("企业ID为空时应返回null", dingDeptMananger.getDeptById(null, TEST_DEPT_ID, TEST_APP_ID));
        assertNull("应用ID为空时应返回null", dingDeptMananger.getDeptById(TEST_EI, TEST_DEPT_ID, null));
    }
} 