package com.facishare.open.ding.provider.service.cloud;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.arg.SendTextNoticeArg;
import com.facishare.open.ding.api.enums.BindType;
import com.facishare.open.ding.api.model.OAConnectorOpenDataModel;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.cloud.CloudNotificationService;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.manager.OAConnectorOpenDataManager;
import com.facishare.open.ding.provider.model.UserTicketModel;
import com.facishare.open.ding.provider.redis.RedisDataSource;
import com.facishare.open.ding.web.template.inner.login.LoginService;
import com.facishare.open.ding.web.template.model.*;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.webhook.common.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DingLoginServiceImpl implements LoginService {
    @Autowired
    private RedisDataSource redisDataSource;
    @Resource
    @Qualifier("dingAuthServiceImpl")
    private DingAuthService dingAuthService;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;
    @Autowired
    private CloudNotificationService notificationService;
    @Autowired
    private EIEAConverter eieaConverter;

    @Override
    public Result<Code2UserInfoData> code2UserInfo(LoginAuthModel loginAuthModel) {
        return null;
    }

    @Override
    public Result<String> genFsTicket(String corpId, String appId, String outUserId, String fsEa) {
        String ticketFormat = "appId=%s&corpId=%s&outUserId=%s&fsEa=%s&timestamp=%s";
        long timestamp = System.currentTimeMillis();
        String ticket = String.format(ticketFormat, appId, corpId, outUserId, fsEa, timestamp);
        UserTicketModel ticketModel = new UserTicketModel(corpId, appId, outUserId, timestamp,fsEa);
        String ticketMd5 = MD5Util.getMD5(ticket);
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + ticketMd5;
        redisDataSource.getRedisClient().set(key, JSONObject.toJSONString(ticketModel));
        redisDataSource.getRedisClient().expire(key, GlobalValue.USER_TICKET_EXPIRE_TIME);  //10分钟有效
        log.info("DingLoginServiceImpl.genFsTicket,ticketMd5={}",ticketMd5);
        return new Result<>(ticketMd5);
    }

    @Override
    public Result<FsUserModel> getFsUser(String ticket) {
        String key = GlobalValue.USER_TICKET_KEY_PREFIX + ticket;
        String value = redisDataSource.getRedisClient().get(key);
        log.info("DingLoginServiceImpl.getFsUser,key={},value={}",key,value);
        if(StringUtils.isEmpty(value)) {
            return Result.newError(ResultCodeEnum.TICKET_NOT_EXISTS);
        }
        UserTicketModel ticketModel = JSONObject.parseObject(value,UserTicketModel.class);
        log.info("DingLoginServiceImpl.getFsUser,ticketModel={}",ticketModel);
        long offset = System.currentTimeMillis() - ticketModel.getTimestamp();
        log.info("DingLoginServiceImpl.getFsUser,offset={}",offset);
        if(offset > GlobalValue.USER_TICKET_EXPIRE_TIME * 1000L) {
            log.info("DingLoginServiceImpl.getFsUser,ticket expired");
            return Result.newError(ResultCodeEnum.TICKET_EXPIRED);
        }
        DingMappingEmployeeResult entity = dingAuthService.queryEmpById(ticketModel.getCorpId(),
                ticketModel.getUserId(), ticketModel.getAppId()).getData();
        log.info("DingLoginServiceImpl.getFsUser,entity={}",entity);
        if(ObjectUtils.isEmpty(entity)) {
            List<DingCorpMappingVo> enterpriseBindList = dingCorpMappingService.queryCorpMappingByCorpId(ticketModel.getCorpId(), ticketModel.getAppId()).getData();
            DingCorpMappingVo enterpriseBindEntity = enterpriseBindList.get(0);
            if(enterpriseBindEntity.getBindType().equals(BindType.ORDER_BIND)) {
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .ea(ticketModel.getFsEa())
                        .channelId(ChannelEnum.dingding.name())
                        .dataTypeId(DataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                        .corpId(ticketModel.getCorpId())
                        .outUserId(ticketModel.getUserId())
                        .errorCode("103")
                        .errorMsg("ticket换取纷享员工身份失败，请及时关注！") //ignorei18n
                        .build();
                oaConnectorOpenDataManager.send(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("人员从钉钉登陆纷享失败告警"); //ignorei18n
                String msg = String.format("ticket换取纷享员工身份失败\nticket=%s，info=%s\n请及时关注！", ticket, value); //ignorei18n
                arg.setMsg(msg);
                notificationService.sendDingCloudNotice(arg);
            }
            return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
        }
        String fsEa = eieaConverter.enterpriseIdToAccount(entity.getEi());
        FsUserModel fsUserModel = new FsUserModel(fsEa,entity.getEmployeeId()+"",ticketModel.getAppId());
        log.info("DingLoginServiceImpl.getFsUser,fsUserModel={}",fsUserModel);
        return Result.newSuccess(fsUserModel);
    }
}
