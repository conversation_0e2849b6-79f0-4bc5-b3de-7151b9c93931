package com.facishare.open.ding.web.controller.cloud;

import com.alibaba.fastjson.*;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
import com.dingtalk.oapi.lib.aes.Utils;
import com.facishare.converter.EIEAConverter;
import com.facishare.eservice.rest.cases.model.DingObjectDataListModel;
import com.facishare.open.ding.api.arg.ComponentQueryArg;
import com.facishare.open.ding.api.arg.ScannerLoginArg;
import com.facishare.open.ding.api.arg.SendTextNoticeArg;
import com.facishare.open.ding.api.enums.CloudDataTypeEnum;
import com.facishare.open.ding.api.enums.GenerateUrlTypeEnum;
import com.facishare.open.ding.api.enums.SourceTypeEnum;
import com.facishare.open.ding.api.model.*;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.service.*;
import com.facishare.open.ding.api.service.cloud.*;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.api.vo.HighBizDataVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.dao.DingMappingEmployeeDao;
import com.facishare.open.ding.web.arg.RegisterArg;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.ding.web.constants.ConfigCenter;
import com.facishare.open.ding.web.constants.Constant;
import com.facishare.open.ding.web.manager.DingtalkApiManager;
import com.facishare.open.ding.web.template.inner.jsapi.DingtalkJsApiTemplate;
import com.facishare.open.ding.web.template.inner.login.DingtalkLoginTemplate;
import com.facishare.open.ding.web.template.model.GetOutUserInfoByCodeArg;
import com.facishare.open.ding.web.template.model.JsapiModel;
import com.facishare.open.ding.web.template.model.OutUserModel;
import com.facishare.open.ding.web.template.outer.event.ticket.DingtalkTicketEventHandlerTemplate;
import com.facishare.open.ding.web.utils.DateUtils;
import com.facishare.open.ding.web.utils.HttpManager;
import com.facishare.open.ding.web.utils.SignatureUtils;
import com.facishare.open.ding.web.utils.TraceUtil;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.outer.oa.connector.common.api.info.ticket.GenFsTicketModel;
import com.facishare.restful.common.StopWatch;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>钉钉云业务回调接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-08-20 10:03
 */
@CrossOrigin
@Slf4j
@RestController
@RequestMapping("/cloud")
// IgnoreI18nFile
public class CloudCallBackController extends BaseController {
    @Autowired
    private SyncBizDataService syncBizDataService;

    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private DingMappingEmployeeDao dingMappingEmployeeDao;
//    @Autowired
//    private SSOLoginService ssoLoginService;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private DingProxyService dingProxyService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private CloudOrderService cloudOrderService;
    @Autowired
    private DingRefuseDataService dingRefuseDataService;
    @Autowired
    private DataStorageService dataStorageService;
    @Autowired
    private CloudNotificationService cloudNotificationService;
    @Autowired
    private CloudMonitorService cloudMonitorService;
    @Autowired
    private CloudContactsService cloudContactsService;
    @Resource
    private DingtalkTicketEventHandlerTemplate dingtalkTicketEventHandlerTemplate;
    @Resource
    private DingtalkJsApiTemplate dingtalkJsApiTemplate;
    @Resource
    private DingtalkLoginTemplate dingtalkLoginTemplate;
    @Resource
    private DingtalkApiManager dingtalkApiManager;
    @Autowired
    private EIEAConverter eieaConverter;

    private static Integer INIT_ORGANIZATION = 1;

    public static int DING_SUCCESS = 0;

    private static final String ADD_USER = "user_add_org";

    private static final String MODIFY_USER = "user_modify_org";

    private static final String LEAVE_USER = "user_leave_org";

    private static final String CREATE_DEPT = "org_dept_create";

    private static final String MODIFY_DEPT = "org_dept_modify";

    private static final String REMOVE_DEPT = "org_dept_remove";


    @ReloadableProperty("sso.redirect.url")
    private String ssoRedirectUrl;

    @ReloadableProperty("hcrm_dingtalk_login_url")
    private String hcrm_dingtalk_login_url;

    @ReloadableProperty("sso.source.web.url")
    private String ssoRedirectWebUrl;

    @ReloadableProperty("sso.source.detail.h5.url")
    private String ssoSourceDetailH5Url;

    @ReloadableProperty("new.sso.source.detail.h5.url")
    private String newSsoSourceDetailH5Url;

    @ReloadableProperty("sso.source.bpm.h5.url")
    private String ssoSourceBpmH5Url;

    @ReloadableProperty("h5Url")
    private String h5Url;
    //业务流程详情页
    private static final String BPM_URL_TYPE = "2";
    private static final String DING_REDIRECT_URL = "dingtalk://dingtalkclient/page/link?url=%s";
    /**
     * 创建应用，验证回调URL创建有效事件（第一次保存回调URL之前）
     */
    private static final String EVENT_CHECK_CREATE_SUITE_URL = "check_create_suite_url";

    /**
     * 创建应用，验证回调URL变更有效事件（第一次保存回调URL之后）
     */
    private static final String EVENT_CHECK_UPADTE_SUITE_URL = "check_update_suite_url";

    /**
     * suite_ticket推送事件
     */
    private static final String EVENT_SUITE_TICKET = "suite_ticket";

    /**
     * 企业授权开通应用事件
     */
    private static final String EVENT_TMP_AUTH_CODE = "tmp_auth_code";
    private static final String CALLBACK_RESPONSE_FAIL = "fail";
    private static String ENTERPRISE_COMBIND = "%S&%S";//企业账号结合个人账户
    private static String ENTERPRISE_NOT_READY = "EnterpriseNotReady";
    public static int DING_TIME_OUT = 853002;
    public static int DING_TEMP_CODE_NOT_AVAILABLE = 40078;

    //未授权的地址
    public static String NOT_AUTH_URL="%s&corpId=%s&authState=%s";


    private LoadingCache<String, String> TickCache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(80, TimeUnit.MINUTES).refreshAfterWrite(60, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
        @Nullable
        @Override
        public String load(@NonNull String key) throws Exception {
            //return getTicket(key);
            String ticket = dingtalkTicketEventHandlerTemplate.execute(key).getDataOrMsg();
            log.info("CloudCallBackController.loadTicket,key={},ticket={}",key,ticket);
            return ticket;
        }
    });


    @PostMapping(value = "/dingCallback")
    public String dingCallback(@RequestParam(value = "signature") String signature,
                               @RequestParam(value = "timestamp") Long timestamp,
                               @RequestParam(value = "nonce") String nonce,
                               @RequestBody(required = false) JSONObject body) {
        String params = "signature:" + signature + " timestamp:" + timestamp + " nonce:" + nonce + " body:" + body;
        try {
            log.info("begin callback:" + params);
            DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(ConfigCenter.TOKEN, ConfigCenter.ENCODING_AES_KEY, ConfigCenter.SUITE_KEY);

            // 从post请求的body中获取回调信息的加密数据进行解密处理
            String encrypt = body.getString("encrypt");
            String plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp.toString(), nonce, encrypt);
            JSONObject callBackContent = JSON.parseObject(plainText);
            //事件入库
            log.info("dingcallBack plaintext:{}", plainText);
            if (!ObjectUtils.isEmpty(callBackContent.get("bizData"))) {
                List<String> voList = JSONArray.parseArray(callBackContent.get("bizData").toString(), String.class);
                List<HighBizDataVo> dataVos = Lists.newArrayList();
                voList.stream().forEach(item -> {
                    HighBizDataVo bizDataVo = JSONObject.parseObject(item, HighBizDataVo.class);
                    dataVos.add(bizDataVo);
                });

                Result<Integer> result = syncBizDataService.batchSaveData(dataVos);
            }

            // 根据回调事件类型做不同的业务处理
            String eventType = callBackContent.getString("EventType");
            if (EVENT_CHECK_CREATE_SUITE_URL.equals(eventType)) {
                log.info("验证新创建的回调URL有效性: " + plainText);
            } else if (EVENT_CHECK_UPADTE_SUITE_URL.equals(eventType)) {
                log.info("验证更新回调URL有效性: " + plainText);
            } else if (EVENT_SUITE_TICKET.equals(eventType)) {
                // suite_ticket用于用签名形式生成accessToken(访问钉钉服务端的凭证)，需要保存到应用的db。
                // 钉钉会定期向本callback url推送suite_ticket新值用以提升安全性。
                // 应用在获取到新的时值时，保存db成功后，返回给钉钉success加密串（如本demo的return）
                log.info("应用suite_ticket数据推送: " + plainText);
            } else if (EVENT_TMP_AUTH_CODE.equals(eventType)) {
                // 本事件应用应该异步进行授权开通企业的初始化，目的是尽最大努力快速返回给钉钉服务端。用以提升企业管理员开通应用体验
                // 即使本接口没有收到数据或者收到事件后处理初始化失败都可以后续再用户试用应用时从前端获取到corpId并拉取授权企业信息，进而初始化开通及企业。
                log.info("企业授权开通应用事件: " + plainText);
            } else {
                // 其他类型事件处理
            }

            // 返回success的加密信息表示回调处理成功
            Map<String, String> success = dingTalkEncryptor.getEncryptedMap("success", System.currentTimeMillis(), Utils.getRandomStr(8));
            log.info("plaintext text:{}", plainText);
            JSONObject jsonObject = new JSONObject();
            jsonObject.putAll(success);
            return jsonObject.toString();
        } catch (Exception e) {
            //失败的情况，应用的开发者应该通过告警感知，并干预修复
            log.error("process callback fail." + params, e);
            return "fail";
        }
    }

    @PostMapping(value = "/dingEserviceCallback")
    public String dingEserviceCallback(@RequestParam(value = "signature") String signature, @RequestParam(value = "timestamp") Long timestamp, @RequestParam(value = "nonce") String nonce, @RequestParam("encodingKey") String encodingKey, @RequestParam("suiteKey") String suiteKey, @RequestBody(required = false) JSONObject body) {
        String params = "signature:" + signature + " timestamp:" + timestamp + " nonce:" + nonce + " body:" + body + " encodingKey:" + encodingKey + "suiteKey:" + suiteKey;
        try {
            log.info("begin callback:" + params);
            DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(ConfigCenter.TOKEN, encodingKey, suiteKey);

            // 从post请求的body中获取回调信息的加密数据进行解密处理
            String encrypt = body.getString("encrypt");
            String plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp.toString(), nonce, encrypt);
            JSONObject callBackContent = JSON.parseObject(plainText);
            //事件入库
            log.info("dingcallBack plaintext:{}", plainText);
            if (!ObjectUtils.isEmpty(callBackContent.get("bizData"))) {
                List<String> voList = JSONArray.parseArray(callBackContent.get("bizData").toString(), String.class);
                List<HighBizDataVo> dataVos = Lists.newArrayList();
                voList.stream().forEach(item -> {
                    HighBizDataVo bizDataVo = JSONObject.parseObject(item, HighBizDataVo.class);
                    dataVos.add(bizDataVo);
                });

                Result<Integer> result = syncBizDataService.batchSaveData(dataVos);
            }

            // 根据回调事件类型做不同的业务处理
            String eventType = callBackContent.getString("EventType");
            if (EVENT_CHECK_CREATE_SUITE_URL.equals(eventType)) {
                log.info("验证新创建的回调URL有效性: " + plainText);
            } else if (EVENT_CHECK_UPADTE_SUITE_URL.equals(eventType)) {
                log.info("验证更新回调URL有效性: " + plainText);
            } else if (EVENT_SUITE_TICKET.equals(eventType)) {
                // suite_ticket用于用签名形式生成accessToken(访问钉钉服务端的凭证)，需要保存到应用的db。
                // 钉钉会定期向本callback url推送suite_ticket新值用以提升安全性。
                // 应用在获取到新的时值时，保存db成功后，返回给钉钉success加密串（如本demo的return）
                log.info("应用suite_ticket数据推送: " + plainText);
            } else if (EVENT_TMP_AUTH_CODE.equals(eventType)) {
                // 本事件应用应该异步进行授权开通企业的初始化，目的是尽最大努力快速返回给钉钉服务端。用以提升企业管理员开通应用体验
                // 即使本接口没有收到数据或者收到事件后处理初始化失败都可以后续再用户试用应用时从前端获取到corpId并拉取授权企业信息，进而初始化开通及企业。
                log.info("企业授权开通应用事件: " + plainText);
            } else {
                // 其他类型事件处理
            }

            // 返回success的加密信息表示回调处理成功
            Map<String, String> success = dingTalkEncryptor.getEncryptedMap("success", System.currentTimeMillis(), Utils.getRandomStr(8));
            log.info("plaintext text:{}", plainText);
            JSONObject jsonObject = new JSONObject();
            jsonObject.putAll(success);
            return jsonObject.toString();
        } catch (Exception e) {
            //失败的情况，应用的开发者应该通过告警感知，并干预修复
            log.error("process callback fail." + params, e);
            return "fail";
        }
    }

    private String getTicket(String appId, String corpId, String outUserId, String fsEa) {
        GenFsTicketModel ticketModel = new GenFsTicketModel();
        ticketModel.setAppId(appId);
        ticketModel.setOutEa(corpId);
        ticketModel.setOutUserId(outUserId);
        ticketModel.setFsEa(fsEa);

        MethodContext context = MethodContext.newInstance(ticketModel);
        dingtalkLoginTemplate.genFsTicket(context);

        com.facishare.open.outer.oa.connector.common.api.result.Result<String> result = context.getResultData();
        String ticket = result.getData();
        log.info("getTicket,ticket={},ticketModel={}",ticket,ticketModel);
        return ticket;
    }

    /**
     * 钉钉安全漏洞扫描，针对需要的域名做指定
     * @param requestAuthCode
     * @param suiteId
     * @param corpId
     * @param userAgent
     * @param isApp
     * @param httpServletResponse
     * @return
     */
    @RequestMapping(value = "/login", method = RequestMethod.GET)
    @CrossOrigin(origins = {"https://app71075.eapps.dingtalkcloud.com","https://ale.fxiaoke.com","https://www.ceshi112.com","https://app79344.eapps.dingtalkcloud.com","https://app84747.eapps.dingtalkcloud.com","https://app83956.eapps.dingtalkcloud.com"})
    public Result<RedirectResult> login(@RequestParam("code") String requestAuthCode,
                                        @RequestParam(value = "suiteId", required = false) String suiteId,
                                        @RequestParam("corpId") String corpId,
                                        @RequestHeader(required = false, value = "User-Agent") String userAgent,
                                        @RequestParam(required = false, value = "isApp", defaultValue = "false") String isApp,
                                        HttpServletResponse httpServletResponse) {

        log.info("login,request pararm code :{},corpId:{},suiteId:{},userAgent:{},isApp:{}", requestAuthCode, corpId, suiteId,userAgent,isApp);
        StopWatch stopWatch = StopWatch.create("cloudLogin");
        suiteId = Optional.ofNullable(suiteId).orElse(ConfigCenter.CRM_SUITE_ID);
//        String access_token = getAccessToken(corpId, suiteId);
//        if(StringUtils.isEmpty(access_token)) {
//            log.info("CloudCallBackController.login,accessToken is null,corpId={},suite={}.", corpId, suiteId);
//            return Result.newError(ResultCode.TOKEN_NOT_EXIST);
//        }
//        String url = "https://oapi.dingtalk.com/user/getuserinfo?access_token=" + access_token + "&code=" + requestAuthCode;
//        Map<String, Object> argMap = Maps.newHashMap();
//        argMap.put("code", requestAuthCode);
//        String result = httpManager.getUrl(url, createHeader());
//        log.info("requestAuthCode result:{}", result);
//        String errCode=JSONPath.read(result,"$.errcode").toString();
//        if(!errCode.equals("0")){
//            return Result.newError(ResultCode.GET_USER_FAIL);
//        }
//        String userId = JSONPath.read(result, "$.userid").toString();
//        String userName = JSONPath.read(result, "$.name").toString();
//        log.info("user id result:{}", result);

        GetOutUserInfoByCodeArg getOutUserInfoByCodeArg = new GetOutUserInfoByCodeArg();
        getOutUserInfoByCodeArg.setCode(requestAuthCode);
        getOutUserInfoByCodeArg.setOutEa(corpId);
        getOutUserInfoByCodeArg.setSuiteId(suiteId);
        MethodContext context = MethodContext.newInstance(getOutUserInfoByCodeArg);
        dingtalkLoginTemplate.getOutUserInfoByCode(context);
        Result<OutUserModel> outUserModelResult = context.getResultData();
        log.info("login,outUserModelResult={}",outUserModelResult);
        if(!outUserModelResult.isSuccess()) {
            return Result.newError(outUserModelResult.getErrorCode(),outUserModelResult.getErrorMessage());
        }
        OutUserModel outUserModel = outUserModelResult.getData();
        String userId = outUserModel.getUserId();
        String userName = outUserModel.getUserName();

        //先查询corp的订单是否还在有效期
        //因为钉钉有留资功能，试用版本的crm过期后，不在这里拦截，由前端进行拦截
        OrderInfoResult lastOrder = cloudOrderService.queryLastOrder(corpId,suiteId);
        if (!ObjectUtils.isEmpty(lastOrder) && lastOrder.getServiceStopTime().getTime() < new Date().getTime() && !org.apache.commons.lang3.StringUtils.equalsIgnoreCase(lastOrder.getOrderChargeType(), "TRYOUT")) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String stopTime = sdf.format(lastOrder.getServiceStopTime());
            RedirectResult redirectResult = new RedirectResult(userId, corpId, "", userName, stopTime, suiteId, isApp);
            return Result.newError(ResultCode.ENTERPRISE_SERVICE_STOP, redirectResult);
        }
        //查询是否被阻拦
        Result<Integer> refuseResult = dingRefuseDataService.queryRefuseDataModel(corpId, suiteId);
        if (ObjectUtils.isEmpty(refuseResult.getData()) || refuseResult.getData() > 0) {
            return Result.newError(ResultCode.REFUSE_ENTRANCE);
        }

        AppParams appParams = com.facishare.open.ding.cloud.constants.ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        final String appId = appParams.getAppId();
        // 获取用户信息
        //查询租户的信息
        Result<List<DingCorpMappingVo>> listResult = corpMappingService.queryCorpMappingByCorpId(corpId, appId);
        log.info("login,listResult={}",listResult);
        stopWatch.lap("queryCorp");
        if (CollectionUtils.isEmpty(listResult.getData())) {
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(suiteId)
                    .channelId(SourceTypeEnum.DING_CLOUD.name())
                    .dataTypeId(CloudDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                    .corpId(corpId)
                    .outUserId(userId)
                    .errorCode("100")
                    .errorMsg("人员点击应用登陆，该企业还没有绑定关系，请及时关注！")
                    .build();
            cloudMonitorService.uploadOaConnectorOpenData(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("人员从钉钉登陆纷享失败告警");
            String msg = String.format("人员点击应用登陆，该企业还没有开通成功\n钉钉企业ea=%s，钉钉人员id=%s，traceId=%s\n请及时关注！", corpId, userId, TraceUtil.get());
            arg.setMsg(msg);
            cloudNotificationService.sendDingCloudNotice(arg);
            RedirectResult redirectResult = new RedirectResult(userId, corpId, "", "", "", suiteId, isApp);
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
        log.info("login, app params map:{},suiteId:{},appParams:{}", ConfigCenter.APP_PARAMS_MAP, suiteId, appParams);

        DingCorpMappingVo dingCorpMappingVo = listResult.getData().get(0);
        log.info("login,dingCorpMappingVo={}",dingCorpMappingVo);
        Integer ei = dingCorpMappingVo.getEi();
        String ea = dingCorpMappingVo.getEa();
        Result<DingMappingEmployeeResult> crmEmpResult = objectMappingService.queryEmpByDingUserId(ei, userId, appId);
        if (ObjectUtils.isEmpty(crmEmpResult.getData())) {
            //创建账号
            Optional<DingCorpMappingVo> corpMappingVo = listResult.getData().stream()
                    .filter(mappingVo -> mappingVo.getAppCode() == Long.parseLong(appParams.getAppId()))
                    .findFirst();

            if (corpMappingVo.isPresent()) {
                log.info("CloudCallBackController.login,ei={},corpMappingVo={}", ei, corpMappingVo.get());
                Result<Integer> contactsAddUserResult = cloudContactsService.addUser(ei, userId, suiteId, corpMappingVo.get());
                if (contactsAddUserResult.isSuccess()) {
                    crmEmpResult = objectMappingService.queryEmpByDingUserId(ei, userId, appParams.getAppId());
                } else {
                    if (contactsAddUserResult.getErrorCode() == ResultCode.NO_USER_AUTHORITY.getErrorCode()) {
                        log.info("CloudCallBackController.login,emp account is not authority,ei={},userId={}", ei, userId);
                        //不在可见范围
                        RedirectResult redirectResult = new RedirectResult(userId, corpId, "", userName, "", suiteId, isApp);
                        return Result.newError(ResultCode.NO_USER_AUTHORITY, redirectResult);
                    } else if (contactsAddUserResult.getErrorCode() == ResultCode.ENTERPRISE_COUNT_FULL.getErrorCode()) {
                        log.info("CloudCallBackController.login,emp account is full,ei={},userId={}", ei, userId);
                        //配额已满，不上报
                        RedirectResult redirectResult = new RedirectResult(userId, corpId, "", userName, "", suiteId, isApp);
                        return Result.newError(ResultCode.CRM_USER_UPPER_LIMIT_INITED, redirectResult);
                    }
                }
            }
        }

        if (ObjectUtils.isEmpty(crmEmpResult.getData())) {
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(suiteId)
                    .channelId(SourceTypeEnum.DING_CLOUD.name())
                    .dataTypeId(CloudDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                    .corpId(corpId)
                    .outUserId(userId)
                    .errorCode("102")
                    .errorMsg("人员点击应用登陆，该人员还没有创建成功，请及时关注！")
                    .build();
            cloudMonitorService.uploadOaConnectorOpenData(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("人员从钉钉登陆纷享失败告警");
            String msg = String.format("人员点击应用登陆，该人员还没有创建成功\n钉钉企业ea=%s，钉钉人员id=%s，traceId=%s\n请及时关注！", corpId, userId, TraceUtil.get());
            arg.setMsg(msg);
            cloudNotificationService.sendDingCloudNotice(arg);
            if (listResult.getData().get(0).getIsInit().equals(INIT_ORGANIZATION)) {
                RedirectResult redirectResult = new RedirectResult(userId, corpId, "", userName, "", suiteId, isApp);
                log.info("not emp");
                return Result.newError(ResultCode.NOT_QUERY_EMP_INFO, redirectResult);
            }
            RedirectResult initResult = new RedirectResult(userId, corpId, "", userName, "", suiteId, isApp);
            return Result.newError(ResultCode.ENTER_PRISE_INIT, initResult);
        }

        stopWatch.lap("queryEmp");
        log.info("login,crm mapping result:{}", crmEmpResult);
        Integer fsUserId = crmEmpResult.getData().getEmployeeId();
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, fsUserId));
//        MethodContext context2 = MethodContext.newInstance(userTokenArg);
//        dingtalkLoginTemplate.createUserToken(context2);
//        CreateUserTokenDto.Result userToken = context2.getResultData();
        stopWatch.lap("createTicket");

        String ticket = getTicket(appId,corpId,userId,ea);

        //log.info("cloud sso login token:{},ei:{},userId:{},fxEmpId:{}", userToken, ei, userId, fsUserId);

        //因为初始化企业比较快，在免登陆的时候企业可能还没就绪
        //CreateUserTokenDto.LoginStatus loginStatus = userToken.getLoginStatus();
        //需要查询appAuth

        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(corpId, Long.parseLong(appParams.getAppId()), null);

        if (CollectionUtils.isEmpty(appAuthResult.getData())) {
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(suiteId)
                    .channelId(SourceTypeEnum.DING_CLOUD.name())
                    .dataTypeId(CloudDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                    .corpId(corpId)
                    .outUserId(userId)
                    .errorCode("100")
                    .errorMsg("人员点击应用登陆，该企业还没有绑定关系，请及时关注！")
                    .build();
            cloudMonitorService.uploadOaConnectorOpenData(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("人员从钉钉登陆纷享失败告警");
            String msg = String.format("人员点击应用登陆，该企业还没有开通成功\n钉钉企业ea=%s，钉钉人员id=%s，traceId=%s\n请及时关注！", corpId, userId, TraceUtil.get());
            arg.setMsg(msg);
            cloudNotificationService.sendDingCloudNotice(arg);
            RedirectResult redirectResult = new RedirectResult(userId, corpId, "", userName, "", suiteId, "");
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
//        String fsToken = userToken.getToken();

//        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, userToken);
//        } else if (org.apache.commons.lang3.StringUtils.isBlank(fsToken)) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], fsToken[{}].", ei, fsUserId, userToken);
//        }
//        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
        log.info("login,call back controller appparams:{},:{}", appParams, appParams.getH5Url());
        String indexUrl = appParams.getH5Url() +"/login?ticket="+ticket;
        if (userAgent.contains("AliApp") || isApp.equals("true")) {
            //indexUrl = appParams.getH5Url();
        } else {
            //indexUrl = appParams.getPCurl();
            indexUrl += "&redirectUrl=" + appParams.getPCurl();
        }
        //redirectUrl = redirectUrl.concat("&source=").concat(URLEncoder.encode(indexUrl));
        log.info("login,indexUrl={}", indexUrl);
        httpServletResponse.setHeader("Access-Control-Allow-Origin", "*");
        httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET,POST");
        httpServletResponse.setHeader("Content-Type", "application/javascript");
        RedirectResult redirectResult = new RedirectResult(userId, corpId, indexUrl, userName, "", suiteId, "");
        return Result.newSuccess(redirectResult);

    }

    @RequestMapping(value = "/commonLogin", method = RequestMethod.GET)
    @CrossOrigin(origins = {"https://app71075.eapps.dingtalkcloud.com","https://ale.fxiaoke.com","https://www.ceshi112.com","https://app79344.eapps.dingtalkcloud.com","https://app84747.eapps.dingtalkcloud.com","https://app83956.eapps.dingtalkcloud.com"})
    public Result<RedirectResult> commonLogin(@RequestParam("code") String requestAuthCode,
                                              @RequestParam(value = "suiteId", required = false) String suiteId,
                                              @RequestParam("corpId") String corpId,
                                              @RequestParam("redirectUrl") String redirectUrl,
                                              @RequestHeader(required = false, value = "User-Agent") String userAgent,
                                              @RequestParam(required = false,value = "hasAuth",defaultValue = "false") Boolean hasAuth,
                                              HttpServletResponse httpServletResponse) {

        log.info("commonLogin,request pararm code :{},corpId:{},suiteId:{},redirecturl:{},userAgent:{},hasAuth:{}",
                requestAuthCode, corpId, suiteId, redirectUrl,userAgent,hasAuth);
        StopWatch stopWatch = StopWatch.create("cloudLogin");
        List<String> redirectUrlMaps=ConfigCenter.REDIRECT_URL_MAP.get(redirectUrl);
        if(!ObjectUtils.isEmpty(ConfigCenter.REDIRECT_URL_MAP.get(redirectUrl))){
            redirectUrl=ConfigCenter.REDIRECT_URL_MAP.get(redirectUrl).get(0).replace("{corpId}",corpId);
        }
        //钉钉安全扫描有时候会恶意注入url.没有进行配置的直接拒绝
//        if(CollectionUtils.isEmpty(redirectUrlMaps)){
//             return Result.newError(ResultCode.REFUSE_ENTRANCE);
//        }


        suiteId = Optional.ofNullable(suiteId).orElse(ConfigCenter.CRM_SUITE_ID).split(",")[0];//在jsp页面取跳转的地址，有时候suiteid放在redirecturl后面，导致取值suiteid会出现xxx,xxx.
        log.info("commonLogin,tries request pararm code :{},corpId:{},suiteId:{},redirecturl:{},auth:{}", requestAuthCode, corpId, suiteId, redirectUrl,hasAuth);
        //判断企业是否已经授权
        Result<HighBizDataVo> highBizDataVoResult = syncBizDataService.selectByBizType(suiteId, "4", corpId);
        if(ObjectUtils.isEmpty(highBizDataVoResult.getData())){
            //可能已经授权完成了，但是数据库还查询不到，做一次延迟
            highBizDataVoResult = retrySelect(corpId, suiteId, hasAuth);
            if(ObjectUtils.isEmpty(highBizDataVoResult.getData())){
                if(CollectionUtils.isEmpty(redirectUrlMaps)){//有可能
                      return Result.newError(ResultCode.REFUSE_ENTRANCE);
                }
                String finalUrl=redirectUrlMaps.get(1).replace("{corpId}",corpId);//0已授权页面，1未授权
                RedirectResult redirectResult = new RedirectResult("", corpId, finalUrl, "", "", suiteId, "");
                log.info("not auth url:{}",redirectResult);
                return Result.newError(ResultCode.CORP_NOT_AUTH_APP,redirectResult);
            }
        }
        AuthEnterPriseModel.BizData authModel = JSONObject.parseObject(highBizDataVoResult.getData().getBizData(), new TypeReference<AuthEnterPriseModel.BizData>() {
        });
        if(authModel.getSyncAction().equals("org_suite_relieve")){
            String finalUrl=redirectUrlMaps.get(1).replace("{corpId}",corpId);//0已授权页面，1未授权
            RedirectResult redirectResult = new RedirectResult("", corpId, finalUrl, "", "", suiteId, "");
            log.info("not auth url:{}",redirectResult);
            return Result.newError(ResultCode.CORP_NOT_AUTH_APP,redirectResult);
        }
//        String access_token = getAccessToken(corpId, suiteId);
//        if(StringUtils.isEmpty(access_token)) {
//            log.info("CloudCallBackController.commonLogin,accessToken is null,corpId={},suite={}.", corpId, suiteId);
//            return Result.newError(ResultCode.TOKEN_NOT_EXIST);
//        }
//        String url = "https://oapi.dingtalk.com/user/getuserinfo?access_token=" + access_token + "&code=" + requestAuthCode;
//        Map<String, Object> argMap = Maps.newHashMap();
//        argMap.put("code", requestAuthCode);
//        String result = httpManager.getUrl(url, createHeader());
//        log.info("result:{}", result);
//        String userId = JSONPath.read(result, "$.userid").toString();
//        String userName = JSONPath.read(result, "$.name").toString();
//        log.info("user id result:{}", result);

        GetOutUserInfoByCodeArg getOutUserInfoByCodeArg = new GetOutUserInfoByCodeArg();
        getOutUserInfoByCodeArg.setCode(requestAuthCode);
        getOutUserInfoByCodeArg.setOutEa(corpId);
        getOutUserInfoByCodeArg.setSuiteId(suiteId);
        MethodContext context = MethodContext.newInstance(getOutUserInfoByCodeArg);
        dingtalkLoginTemplate.getOutUserInfoByCode(context);
        Result<OutUserModel> outUserModelResult = context.getResultData();
        log.info("commonLogin,outUserModelResult={}",outUserModelResult);
        if(!outUserModelResult.isSuccess()) {
            return Result.newError(outUserModelResult.getErrorCode(),outUserModelResult.getErrorMessage());
        }
        OutUserModel outUserModel = outUserModelResult.getData();
        String userId = outUserModel.getUserId();
        String userName = outUserModel.getUserName();


        //先查询corp的订单是否还在有效期
        OrderInfoResult lastOrder = cloudOrderService.queryLastOrder(corpId,suiteId);
        if (!ObjectUtils.isEmpty(lastOrder) && lastOrder.getServiceStopTime().getTime() < System.currentTimeMillis()) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String stopTime = sdf.format(lastOrder.getServiceStopTime());
            RenewOrderUrl renewOrderUrl = ConfigCenter.COMMON_RENEW_ORDER_URL.get(suiteId);
            redirectUrl=renewOrderUrl.getPcUrl();
            if (userAgent.contains("AliApp")) {
                redirectUrl=renewOrderUrl.getH5Url();
            }
           String finalRedirect= redirectUrl.replace("${corpId}",corpId);
            log.info("ENTERPRISE_SERVICE_STOP url result:{}", finalRedirect);
            RedirectResult redirectResult = new RedirectResult(userId, corpId, finalRedirect, userName, stopTime, suiteId, "");
            return Result.newError(ResultCode.ENTERPRISE_SERVICE_STOP, redirectResult);
        }

        AppParams appParams = com.facishare.open.ding.cloud.constants.ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        final String appId = appParams.getAppId();
        // 获取用户信息
        //查询租户的信息
        Result<List<DingCorpMappingVo>> listResult = corpMappingService.queryCorpMappingByCorpId(corpId, appId);
        stopWatch.lap("queryCorp");
        if (CollectionUtils.isEmpty(listResult.getData())) {
            RedirectResult redirectResult = new RedirectResult(userId, corpId, "", "", "", suiteId, "");
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
        DingCorpMappingVo dingCorpMappingVo = listResult.getData().get(0);
        log.info("commonLogin,dingCorpMappingVo={}",dingCorpMappingVo);
        Integer ei = dingCorpMappingVo.getEi();
        String ea = dingCorpMappingVo.getEa();

//        GenFsTicketModel ticketModel = new GenFsTicketModel();
//        ticketModel.setAppId(appId);
//        ticketModel.setOutEa(corpId);
//        ticketModel.setOutUserId(userId);
//        ticketModel.setFsEa(ea);
//
//        context = MethodContext.newInstance(ticketModel);
//        dingtalkLoginTemplate.genFsTicket(context);
//
//        com.facishare.open.feishu.syncapi.result.Result<String> result = context.getResultData();
        String ticket = getTicket(appId,corpId,userId,ea);


        Result<DingMappingEmployeeResult> crmEmpResult = objectMappingService.queryEmpByDingUserId(ei, userId, appId);
        if (ObjectUtils.isEmpty(crmEmpResult.getData())) {
            if (listResult.getData().get(0).getIsInit().equals(INIT_ORGANIZATION)) {
                RedirectResult redirectResult = new RedirectResult(userId, corpId, "", userName, "", suiteId, "");
                log.info("not emp");
                return Result.newError(ResultCode.NOT_QUERY_EMP_INFO, redirectResult);
            }
            RedirectResult initResult = new RedirectResult(userId, corpId, "", userName, "", suiteId, "");
            return Result.newError(ResultCode.ENTER_PRISE_INIT, initResult);
        }
        stopWatch.lap("queryEmp");
        log.info("commonLogin,crm mapping result:{}", crmEmpResult);
//        Integer fsUserId = crmEmpResult.getData().getEmployeeId();
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, fsUserId));
//        MethodContext context2 = MethodContext.newInstance(userTokenArg);
//        dingtalkLoginTemplate.createUserToken(context2);
//        CreateUserTokenDto.Result userToken = context2.getResultData();
//        stopWatch.lap("createToken");
//        log.info("cloud sso login token:{},ei:{},userId:{},fxEmpId:{}", userToken, ei, userId, fsUserId);
//
//        //因为初始化企业比较快，在免登陆的时候企业可能还没就绪
//        CreateUserTokenDto.LoginStatus loginStatus = userToken.getLoginStatus();
        //需要查询appAuth

        log.info("commonLogin,app params map:{},suiteId:{},appParams:{}", ConfigCenter.APP_PARAMS_MAP, suiteId, appParams);
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(corpId, Long.parseLong(appParams.getAppId()), null);

        if (CollectionUtils.isEmpty(appAuthResult.getData())) {
            RedirectResult redirectResult = new RedirectResult(userId, corpId, "", userName, "", suiteId, "");
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
        //String fsToken = userToken.getToken();

//        String ssoUrl = String.format(ssoRedirectUrl, fsToken);
//        ssoUrl = ssoUrl.concat("&source=").concat(URLEncoder.encode(redirectUrl));
        String ssoUrl = hcrm_dingtalk_login_url + "?ticket="+ ticket + "&redirectUrl="+URLEncoder.encode(redirectUrl);
        log.info("commonLogin,ssoUrl={}", ssoUrl);
        httpServletResponse.setHeader("Access-Control-Allow-Origin", "*");
        httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET,POST");
        httpServletResponse.setHeader("Content-Type", "application/javascript");
        RedirectResult redirectResult = new RedirectResult(userId, corpId, ssoUrl, userName, "", suiteId, "");
        return Result.newSuccess(redirectResult);

    }

    private Result<HighBizDataVo> retrySelect(String corpId,String suiteId,boolean hasAuth){
        log.info("retrySelect corpId:{}.suiteId:{}.hasAuth:{}",corpId,suiteId,hasAuth);

        if(!hasAuth)return Result.newSuccess();
        for (int i = 0; i < 5; i++) {
            Result<HighBizDataVo> highBizDataVoResult = syncBizDataService.selectByBizType(suiteId, "4", corpId);
           if(!ObjectUtils.isEmpty(highBizDataVoResult.getData())){
               return highBizDataVoResult;
           }
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        }
       return Result.newSuccess();
    }



    @RequestMapping(value = "/commonLongPolling", method = RequestMethod.POST)
    @ResponseBody
    public Result<RedirectResult> commonLongPolling(@RequestParam(name = "redirectUrl") String redirectUrl,
                                                    @RequestBody RedirectResult redirectResult,
                                                    HttpServletResponse httpServletResponse,
                                                    @RequestHeader(required = false, value = "User-Agent") String userAgent) {
        StopWatch stopWatch = StopWatch.create("cloudLogin");
        log.info("commonLongPolling,polling arg:{}", redirectResult);
        String corpId = redirectResult.getCorpId();
        String userId = redirectResult.getUserId();
        String suiteId = Optional.ofNullable(redirectResult.getSuiteId()).orElse(ConfigCenter.CRM_SUITE_ID);
        //List<String> redirectUrlMaps=ConfigCenter.REDIRECT_URL_MAP.get(redirectUrl);
        if(!ObjectUtils.isEmpty(ConfigCenter.REDIRECT_URL_MAP.get(redirectUrl))){
            redirectUrl=ConfigCenter.REDIRECT_URL_MAP.get(redirectUrl).get(0).replace("{corpId}",corpId);
        }
        AppParams appParams = com.facishare.open.ding.cloud.constants.ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        final String appId = appParams.getAppId();
        Result<List<DingCorpMappingVo>> listResult = corpMappingService.queryCorpMappingByCorpId(corpId, appId);
        stopWatch.lap("queryCorp");
        if (CollectionUtils.isEmpty(listResult.getData())) {
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
        DingCorpMappingVo dingCorpMappingVo = listResult.getData().get(0);
        String fsEa = dingCorpMappingVo.getEa();
        Integer ei = dingCorpMappingVo.getEi();
        Result<DingMappingEmployeeResult> crmEmpResult = objectMappingService.queryEmpByDingUserId(ei, userId, appId);
        stopWatch.lap("queryEmp");
        if (ObjectUtils.isEmpty(crmEmpResult.getData())) {
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
//        Integer fsUserId = crmEmpResult.getData().getEmployeeId();
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, fsUserId));
//        MethodContext context = MethodContext.newInstance(userTokenArg);
//        dingtalkLoginTemplate.createUserToken(context);
//        CreateUserTokenDto.Result userToken = context.getResultData();
//        stopWatch.lap("createToken");
//        log.info("cloud sso login token:{},ei:{},userId:{}", userToken, ei, userId);
//        String fsToken = userToken.getToken();
//        CreateUserTokenDto.LoginStatus loginStatus = userToken.getLoginStatus();
        //需要查询appAuth
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(corpId, null, Long.valueOf(suiteId));
        if (CollectionUtils.isEmpty(appAuthResult.getData())) {
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
//        String ssoUrl = String.format(ssoRedirectUrl, fsToken);
//        ssoUrl = ssoUrl.concat("&source=").concat(URLEncoder.encode(redirectUrl));

        String ticket = getTicket(appId,corpId,userId,fsEa);
        String ssoUrl = hcrm_dingtalk_login_url + "?ticket=" + ticket + "&redirectUrl="+URLEncoder.encode(redirectUrl);
        log.info("commonLongPolling,ssoUrl={}", ssoUrl);

        httpServletResponse.setHeader("Access-Control-Allow-Origin", "*");
        httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET,POST");
        httpServletResponse.setHeader("Content-Type", "application/javascript");
        redirectResult = new RedirectResult(userId, corpId, ssoUrl, "", "", suiteId, "");
        return Result.newSuccess(redirectResult);

    }


    @RequestMapping(value = "/longPolling", method = RequestMethod.POST)
    @ResponseBody
    public Result<RedirectResult> longPolling(@RequestBody RedirectResult redirectResult,
                                              HttpServletResponse httpServletResponse, @RequestHeader(required = false, value = "User-Agent") String userAgent,
                                              @RequestParam(required = false, value = "isApp", defaultValue = "false") String isApp) {
        StopWatch stopWatch = StopWatch.create("cloudLogin");
        log.info("longPolling,arg:{}", redirectResult);
        String corpId = redirectResult.getCorpId();
        String userId = redirectResult.getUserId();
        String suiteId = Optional.ofNullable(redirectResult.getSuiteId()).orElse(ConfigCenter.CRM_SUITE_ID);
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        final String appId = appParams.getAppId();
        Result<List<DingCorpMappingVo>> listResult = corpMappingService.queryCorpMappingByCorpId(corpId, appId);
        stopWatch.lap("queryCorp");
        if (CollectionUtils.isEmpty(listResult.getData())) {
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
        DingCorpMappingVo dingCorpMappingVo = listResult.getData().get(0);
        String fsEa = dingCorpMappingVo.getEa();
        Integer ei = dingCorpMappingVo.getEi();
        Result<DingMappingEmployeeResult> crmEmpResult = objectMappingService.queryEmpByDingUserId(ei, userId, appId);
        stopWatch.lap("queryEmp");
        if (ObjectUtils.isEmpty(crmEmpResult.getData())) {
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
//        Integer fsUserId = crmEmpResult.getData().getEmployeeId();
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, fsUserId));
//        MethodContext context = MethodContext.newInstance(userTokenArg);
//        dingtalkLoginTemplate.createUserToken(context);
//        CreateUserTokenDto.Result userToken = context.getResultData();
//        stopWatch.lap("createToken");
//        log.info("cloud sso login token:{},ei:{},userId:{}", userToken, ei, userId);
//        String fsToken = userToken.getToken();
//        CreateUserTokenDto.LoginStatus loginStatus = userToken.getLoginStatus();
        //需要查询appAuth
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(corpId, null, Long.valueOf(suiteId));
        if (CollectionUtils.isEmpty(appAuthResult.getData())) {
            return Result.newError(ResultCode.ENTER_PRISE_INIT, redirectResult);
        }
        //String redirectUrl = String.format(ssoRedirectUrl, fsToken);

        String ticket = getTicket(appId,corpId,userId,fsEa);

        String redirectUrl = appParams.getH5Url() + "/login?ticket="+ticket;
        if (userAgent.contains("AliApp") || isApp.equals("true")) {
            //redirectUrl = redirectUrl.concat("&source=").concat(appParams.getH5Url());
        } else {
            //redirectUrl = redirectUrl.concat("&source=").concat(appParams.getPCurl());
            redirectUrl = redirectUrl + "&redirectUrl="+appParams.getPCurl();
        }
        log.info("longPolling,redirectUrl={}", redirectUrl);
        httpServletResponse.setHeader("Access-Control-Allow-Origin", "*");
        httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET,POST");
        httpServletResponse.setHeader("Content-Type", "application/javascript");
        redirectResult = new RedirectResult(userId, corpId, redirectUrl, "", "", suiteId, "");
        return Result.newSuccess(redirectResult);

    }


    //纷享的代办消息推送到钉钉时，会将该接口的url嵌入消息中，点击代办消息时会进入到该接口，钉钉点击代办
    @RequestMapping(value = "/skipMessage", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Object authorize(@RequestParam(value = "code", required = false) String code,
                            @RequestParam(value = "state", required = false) String state,
                            @RequestParam(value = "apiname", required = false) String apiName,
                            @RequestParam(value = "objectId", required = false) String objectId,
                            @RequestParam(value = "instanceId", required = false) String instanceId,
                            @RequestParam(value = "taskId", required = false) String taskId,
                            @RequestParam(value = "ei", required = false) Integer ei,
                            @RequestParam(value = "suiteId", required = false) String suiteId,
                            @RequestParam(value = "url", required = false) String url,
                            @RequestParam(value = "bizType", required = false) String bizType,
                            @RequestHeader(required = false, value = "User-Agent") String userAgent, HttpServletRequest request) {
        //查询绑定企业
        Result<List<DingCorpMappingVo>> enterPriseResult = corpMappingService.queryByEi(ei);


        log.info("CloudCallBackController.authorize,code:{},state:{},apiName:{},objectId:{},instanceId:{},taskId:{},ei:{},suiteId:{},url:{},userAgent:{},bizType:{}",
                code, state, apiName, objectId, instanceId, taskId, ei, suiteId, url, userAgent, bizType);
        if (ObjectUtils.isEmpty(enterPriseResult)) {
            log.warn("this enterprise not bind or not auth ei:{}", ei);
            return CALLBACK_RESPONSE_FAIL;
        }
        //通过code查询对应的客户信息
        String unionId = getUnionId(code);
        if (StringUtils.isEmpty(unionId)) {
            return Result.newError(ResultCode.BY_CODE_FAIL);
        }
//        //查询对应corpId
        //Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEi(ei);
        DingCorpMappingVo dingCorpMappingVo = enterPriseResult.getData().get(0);

        String appId = dingCorpMappingVo.getAppCode()+"";
        String corpId = dingCorpMappingVo.getDingCorpId();
        String ea = dingCorpMappingVo.getEa();
        suiteId = ObjectUtils.isEmpty(suiteId) ? ConfigCenter.CRM_SUITE_ID : suiteId;
        String userId = dingProxyService.getUserId(corpId, unionId, suiteId);
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        Result<DingMappingEmployeeResult> crmEmpResult = objectMappingService.queryEmpByDingUserId(ei, userId, appParams.getAppId());
        if (Objects.isNull(crmEmpResult.getData()) || StringUtils.isEmpty(crmEmpResult.getData().getEmployeeId())) {
            log.info("the emp not bind");
            return Result.newError(ResultCode.NOT_BIND_EMP);
        }
//        Integer crmEmp = crmEmpResult.getData().getEmployeeId();
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(crmEmp)));
//        MethodContext context = MethodContext.newInstance(userTokenArg);
//        dingtalkLoginTemplate.createUserToken(context);
//        CreateUserTokenDto.Result ssoResult = context.getResultData();
//
//        log.info("ssoLogin token :{}", ssoResult);
//        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
//        String fsToken = ssoResult.getToken();
//        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, crmEmp, ssoResult);
//        } else if (org.apache.commons.lang3.StringUtils.isBlank(fsToken)) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], fsToken[{}].", ei, crmEmp, ssoResult);
//        }

        String ticket = getTicket(appId,corpId,userId,ea);

        //String redirectUrl = String.format(ssoRedirectUrl, fsToken);
        //log.info("redirectUrl[{}]", redirectUrl);
        String sourceUrl = null;
        if (!StringUtils.isEmpty(taskId) && BPM_URL_TYPE.equals(taskId)) {
            if(ConfigCenter.BPM_TODO_EA.contains(enterPriseResult.getData().get(0).getEa())) {
                String todoName= Splitter.on(",").splitToList(apiName).get(0);
                sourceUrl = ssoSourceDetailH5Url + "?id=" + objectId + "&apiname=" + todoName;//业务对象详情页
            } else {
                sourceUrl = ssoSourceBpmH5Url + instanceId;//bpm业务流程详情页
            }
        } else if(!StringUtils.isEmpty(taskId)
                && (String.valueOf(GenerateUrlTypeEnum.FILE_MESSAGE_URL.getType()).equals(taskId))
                || String.valueOf(GenerateUrlTypeEnum.BI_MESSAGE_URL.getType()).equals(taskId)
                || String.valueOf(GenerateUrlTypeEnum.ATME_URL.getType()).equals(taskId)) {
            sourceUrl = new String(org.apache.commons.codec.binary.Base64.decodeBase64(url.getBytes()));
        } else {
            if(ConfigCenter.TODO_GRAY_EA.contains(enterPriseResult.getData().get(0).getEa()) || ConfigCenter.TODO_GRAY_EA.contains("dingtalk_true")) {
                if(!StringUtils.isEmpty(instanceId)
                        && !instanceId.equals("100")
                        && !StringUtils.isEmpty(apiName)
                        && !StringUtils.isEmpty(objectId)
                        && "452".equals(bizType)
                        && isNewPagesEi(ei)) {
                    sourceUrl = ConfigCenter.CRM_APPROVAL_INSTANCE_URL
                            .replace("{workflowInstanceId}", instanceId)
                            .replace("{objectApiName}", Splitter.on(",").splitToList(apiName).get(0))
                            .replace("{objectId}", objectId);
                } else {
                    String todoName= Splitter.on(",").splitToList(apiName).get(0);
                    sourceUrl = ssoSourceDetailH5Url + "?id=" + objectId + "&apiname=" + todoName;//业务对象详情页
                }
            } else {
                if(ConfigCenter.NEW_H5_GRAY_TENANTS.contains(enterPriseResult.getData().get(0).getEa()) && !StringUtils.isEmpty(instanceId) && !instanceId.equals("100")) {
                    sourceUrl = newSsoSourceDetailH5Url + "?apiName=" + ConfigCenter.APPROVAL_INSTANCE_OBJ + "&dataId=" + instanceId;//业务对象详情页
                } else {
                    String todoName= Splitter.on(",").splitToList(apiName).get(0);
                    sourceUrl = ssoSourceDetailH5Url + "?id=" + objectId + "&apiname=" + todoName;//业务对象详情页
                }
            }
        }
        String finalUrl = hcrm_dingtalk_login_url+"?ticket="+ticket + "&redirectUrl="+URLEncoder.encode(sourceUrl);
        //finalUrl = redirectUrl + "&source=" + URLEncoder.encode(sourceUrl);

        if(!StringUtils.isEmpty(taskId)
                && (String.valueOf(GenerateUrlTypeEnum.FILE_MESSAGE_URL.getType()).equals(taskId)
                || String.valueOf(GenerateUrlTypeEnum.BI_MESSAGE_URL.getType()).equals(taskId))) {
            finalUrl = String.format(DING_REDIRECT_URL, URLEncoder.encode(finalUrl + "&ddtab=true"));
        }

        log.info("finalUrl[{}]", finalUrl);
        ModelAndView mv = new ModelAndView("redirect:" + finalUrl);
        mv.addObject("message", "登录成功！");
        return mv;
    }

    private String getUnionId(String code) {
        Long timeStamp = new Date().getTime();
        String signatureValue = SignatureUtils.snsInfoSignature(ConfigCenter.SUITE_SECRET, timeStamp);
        String snsInfoUrl = "https://oapi.dingtalk.com/sns/getuserinfo_bycode?accessKey=" + ConfigCenter.SUITE_KEY + "&timestamp=" + timeStamp + "&signature=" + signatureValue;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("tmp_auth_code", code);
        Map<String, Object> objectMap = httpManager.postUrl(snsInfoUrl, argMap, dingtalkApiManager.createHeader());
        log.info("message result:{}", objectMap);
        String result = objectMap.get("body").toString();
        Integer errCode = Integer.parseInt(JSONPath.read(result, "$.errcode").toString());
        if (errCode.equals(DING_TIME_OUT)) {
            log.warn("cloud dingding by code queryInfo fail:{}", code);
            Long refreshTime = DateUtils.timeStamp(objectMap.get("time").toString());
            signatureValue = SignatureUtils.snsInfoSignature(ConfigCenter.SUITE_SECRET, refreshTime);
            snsInfoUrl = "https://oapi.dingtalk.com/sns/getuserinfo_bycode?accessKey=" + ConfigCenter.SUITE_KEY + "&timestamp=" + refreshTime + "&signature=" + signatureValue;
            objectMap = httpManager.postUrl(snsInfoUrl, argMap, dingtalkApiManager.createHeader());
            result = objectMap.get("body").toString();

        }
        //
        if (errCode.equals(DING_TEMP_CODE_NOT_AVAILABLE)) {
            return null;
        }
        String unionId = JSONPath.read(result, "$.user_info.unionid").toString();
        return unionId;
    }

    @RequestMapping(value = "/chooseEnterPrise", method = RequestMethod.GET)
    @ResponseBody
        public Result<List<ScannerEnterPriseResult>> chooseEnterPrise(@RequestParam("code") String code, @RequestParam("state") String state, @RequestParam("appId") String appId) {
        StopWatch stopWatch = StopWatch.create("scannerLogin");
        String unionId = getUnionId(code);
        if (ObjectUtils.isEmpty(unionId)) {
            return Result.newError(ResultCode.BY_CODE_FAIL);
        }
        //只能获取到UnionId,所以钉钉官方建议ISV需要根据userId自己检索数据库是否有该员工授权

        Result<List<OuterOaEmployeeBindEntity>> corpResult = objectMappingService.queryInfoByUserId(unionId, appId);

        log.info("choose enterprise getUnion id:{},corpResult:{}", unionId, corpResult);
        if (ObjectUtils.isEmpty(corpResult.getData())) {
            return Result.newError(ResultCode.NOT_QUERY_EMP_INFO);
        }
        List<ScannerEnterPriseResult> scannerEnterPriseResults = Lists.newArrayList();
        for (OuterOaEmployeeBindEntity datum : corpResult.getData()) {
            Integer ei = eieaConverter.enterpriseAccountToId(datum.getFsEa());
            Result<List<DingCorpMappingVo>> listResult = corpMappingService.queryByEi(ei);
            if (!CollectionUtils.isEmpty(listResult.getData())) {
                DingCorpMappingVo corpVo = listResult.getData().get(0);
                ScannerEnterPriseResult scannerEnterPriseResult = new ScannerEnterPriseResult();
                scannerEnterPriseResult.setDingCorpId(corpVo.getDingCorpId());
                scannerEnterPriseResult.setEnterPriseAccount(corpVo.getEa());
                scannerEnterPriseResult.setEnterPriseId(corpVo.getEi());
                scannerEnterPriseResult.setName(corpVo.getEnterpriseName());
                scannerEnterPriseResult.setFsEmpId(Integer.valueOf(datum.getOutEmpId()));
                scannerEnterPriseResults.add(scannerEnterPriseResult);
            }
        }
        return Result.newSuccess(scannerEnterPriseResults);
    }

    @RequestMapping(value = "/scannerLogin", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> scannerLogin(@RequestBody ScannerLoginArg scannerLoginArg) {
        //CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(scannerLoginArg.getEi(), scannerLoginArg.getFsEmpId()));
        try {
//            MethodContext context = MethodContext.newInstance(userTokenArg);
//            dingtalkLoginTemplate.createUserToken(context);
//            CreateUserTokenDto.Result userToken = context.getResultData();
//            stopWatch.lap("createToken");
//            log.info("cloud sso login token:{}", userToken);
//            String fsToken = userToken.getToken();
//            CreateUserTokenDto.LoginStatus loginStatus = userToken.getLoginStatus();
//            if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus || org.apache.commons.lang3.StringUtils.isBlank(fsToken)) {
//                log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, scannerLoginArg{}, result[{}].", scannerLoginArg, userToken);
//                return Result.newError(ResultCode.EMP_OR_ENTERPRISE_STOP);
//            }
//            String redirectUrl = String.format(ssoRedirectUrl, fsToken);
            Result<List<DingCorpMappingVo>> corpMapping = corpMappingService.queryByEi(scannerLoginArg.getEi());
            log.info("scannerLogin,corpMapping={}",corpMapping);
            if(!CollectionUtils.isEmpty(corpMapping.getData())) {
                DingCorpMappingVo dingCorpMappingVo = corpMapping.getData().get(0);
                String appId = dingCorpMappingVo.getAppCode()+"";
                String corpId = dingCorpMappingVo.getDingCorpId();
                String ea = dingCorpMappingVo.getEa();

                DingMappingEmployeeResult employeeMapping = dingMappingEmployeeDao.findMappingByEmployeeId(scannerLoginArg.getEi(),
                        scannerLoginArg.getFsEmpId());
                log.info("scannerLogin,employeeMapping={}",employeeMapping);
                if(employeeMapping!=null) {
                    String outUserId = employeeMapping.getDingEmployeeId();
                    String ticket = getTicket(appId,corpId,outUserId,ea);
                    String redirectUrl = hcrm_dingtalk_login_url + "?ticket="+ticket;
                    log.info("scannerLogin,cloud authorizeByApp SignOnManager redirectThirdRequest, scannerLoginArg[{}], url[{}]", scannerLoginArg, redirectUrl);
                    return Result.newSuccess(redirectUrl);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.newError(ResultCode.EMP_OR_ENTERPRISE_STOP);
    }


    @RequestMapping("/skip/manager")
    @ResponseBody
    @CrossOrigin(origins = {"https://app71075.eapps.dingtalkcloud.com"})
    public Object skipManager(@RequestParam(value = "code") String code, @RequestParam(value = "appId", required = false) String appId) {
        log.info("skipManager,code={}", code);
        String managerUrl = "https://oapi.dingtalk.com/sso/gettoken?corpid=" + Constant.ISV_CORP_ID + "&corpsecret=" + Constant.SSO_SECRET;
        String result = httpManager.getUrl(managerUrl, dingtalkApiManager.createHeader());
        log.info("skipManager,execute getAccessToken :{}", result);
        Integer errCode = Integer.parseInt(JSONPath.read(result, "$.errcode").toString());
        if (Objects.isNull(errCode) || errCode != DING_SUCCESS) {
            log.warn("getToken appKey或appSecret错误.");
            return null;
        }
        // 获取企业的access_token
        String accessToken = JSONPath.read(result, "$.access_token").toString();

        // 获取应用管理员的身份信息
        Map<String, String> objectMap = getAdminUserInfo(code, accessToken);
        String corpId = objectMap.get("corpId");
        String userId = objectMap.get("userId");
        //查询对应的企业信息
        Result<List<DingCorpMappingVo>> listResult = corpMappingService.queryCorpMappingByCorpId(corpId, appId);

        if (CollectionUtils.isEmpty(listResult.getData())) {
            String combindValue = String.format(ENTERPRISE_COMBIND, corpId, userId);
            return Result.newError(ResultCode.ENTER_PRISE_INIT, combindValue);
        }
        DingCorpMappingVo dingCorpMappingVo = listResult.getData().get(0);
        log.info("skipManager,dingCorpMappingVo={}", dingCorpMappingVo);
        appId = dingCorpMappingVo.getAppCode()+"";

        Integer ei = dingCorpMappingVo.getEi();
        Result<DingMappingEmployeeResult> crmEmpResult = objectMappingService.queryEmpByDingUserId(ei, userId, appId);
        if (ObjectUtils.isEmpty(crmEmpResult) || ObjectUtils.isEmpty(crmEmpResult.getData())) {
            return Result.newError(ResultCode.NOT_QUERY_EMP_INFO);
        }

//        Integer fsUserId = crmEmpResult.getData().getEmployeeId();
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, fsUserId));
//        MethodContext context = MethodContext.newInstance(userTokenArg);
//        dingtalkLoginTemplate.createUserToken(context);
//        CreateUserTokenDto.Result userToken = context.getResultData();
//
//        log.info("cloud sso login token:{}", userToken);
//        String fsToken = userToken.getToken();
//        CreateUserTokenDto.LoginStatus loginStatus = userToken.getLoginStatus();
//        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, userToken);
//        } else if (org.apache.commons.lang3.StringUtils.isBlank(fsToken)) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], fsToken[{}].", ei, fsUserId, userToken);
//        }

        String fsEa = dingCorpMappingVo.getEa();

        String ticket = getTicket(appId,corpId,userId,fsEa);
        String redirectUrl = hcrm_dingtalk_login_url + "?ticket={}"+ticket;
        //String redirectUrl = String.format(ssoRedirectUrl, fsToken);

        log.info("skipManager,redirectUrl={}", redirectUrl);
        ModelAndView mv = new ModelAndView("redirect:" + redirectUrl);
        mv.addObject("message", "登录成功！");
        return mv;
    }

    @RequestMapping(value = "/getJsApiTicket", method = RequestMethod.GET)
    @ResponseBody
    public Result<SignResult> getJsApiTicket(@RequestParam("corpId") String corpId, @RequestParam("timeStamp") Long timeStamp) {
        String ticket = TickCache.get(corpId);
        JsapiModel jsapiModel = new JsapiModel();
        jsapiModel.setTicket(ticket);
        jsapiModel.setTimeStamp(timeStamp);
        jsapiModel.setOutEa(corpId);

        MethodContext context = MethodContext.newInstance(jsapiModel);
        dingtalkJsApiTemplate.getJsApiSignature(context);

        SignResult signResult = context.getResultData();
//        //获取签名参数
//        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(corpId, ConfigCenter.APP_CRM_ID, null);
//        Long agentId = appAuthResult.getData().get(0).getAgentId();
//        String signValue = "";
//        try {
//            signValue = JSApiUtils.sign(ticket, ConfigCenter.NONCE_STR, timeStamp, ConfigCenter.h5Url);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        SignResult signResult = new SignResult(signValue, agentId);
        return Result.newSuccess(signResult);
    }

    //应用内授权


//    @RequestMapping(value = "/updateSql", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    public Result<Integer> updateSql(@RequestBody String sql) {
//        log.info("update sql:{}", sql);
//        Result<Integer> result = syncBizDataService.updateSql(sql);
//        return result;
//    }

//    @RequestMapping(value = "/fixOrderEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    public Result<Integer> fixOrderEvent(@RequestParam("startTime") Long startTime,@RequestParam("endTime") Long endTime) {
//
//        Result<Integer> result = syncBizDataService.fixOrderEventData(null,startTime,endTime);
//        return result;
//    }
    @GetMapping("/deferredResultUser")
    public DeferredResult<Result<List<UserVo>>> deferredResultListUser(@RequestParam("time")Long timeOut) {
        DeferredResult<Result<List<UserVo>>> deferredResult =
                new DeferredResult<>(timeOut, Result.newError(ResultCode.SYSTEM_ERROR));
        deferredResult.onTimeout(() -> {
            log.info("调用超时");
        });

        deferredResult.onCompletion(() -> {
            log.info("调用完成");
        });

        new Thread(() -> {
            try {
                log.info("starting ...");
                TimeUnit.SECONDS.sleep(10);
                deferredResult.setResult(Result.newSuccess(Lists.newArrayList(new UserVo())));
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }).start();
        return deferredResult;
    }


//    @RequestMapping(value = "/insertSql", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
//    @ResponseBody
//    public Result<Integer> insertSql(@RequestBody String sql) {
//        log.info("update sql:{}", sql);
//        Result<Integer> result = syncBizDataService.insertSql(sql);
//        return result;
//    }

    @RequestMapping(value = "/getUpdateSql", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Result<Integer> updateSql(@RequestParam("nowTime") String nowTime, @RequestParam("id") Integer id) {
        log.info("update nowTime:{},id:{}", nowTime, id);
        Result<Integer> result = syncBizDataService.getUpdateSql(nowTime, id);
        return result;
    }

    @RequestMapping(value = "/refreshOrder", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Result<Integer> updateSql(@RequestParam(value = "corpId", required = false) String enterPriseId, @RequestParam(value = "name", required = false) String name, @RequestParam("nowTime") String nowTime) {
        if (StringUtils.isEmpty(enterPriseId)) {
            Result<String> dingCorpId = syncBizDataService.getDingCorpId(name);
            enterPriseId = dingCorpId.getData();
        }
        String updateSql = "update open_sync_biz_data set gmt_modified = " + '\'' + nowTime + '\'' + "where corp_id=" + '\'' + enterPriseId + '\'' + ";";
        Result<Integer> result = syncBizDataService.updateSql(updateSql);
        return result;
    }

    @RequestMapping(value = "/queryEaByCorpId", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Result<String> queryEaByCorpId(@RequestParam(value = "corpId", required = false) String enterPriseId) {
        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryCorpMappingByCorpId(enterPriseId, null);
        if (CollectionUtils.isEmpty(corpResult.getData())) {
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        String ea = corpResult.getData().get(0).getEa();
        return Result.newSuccess(ea);
    }


//    private String getTicket(String corpId) {
//        String accessToken = getAccessToken(corpId, ConfigCenter.CRM_SUITE_ID);
//        if(StringUtils.isEmpty(accessToken)) {
//            log.info("CloudCallBackController.getTicket,accessToken is null,corpId={},suite={}.", corpId, ConfigCenter.CRM_SUITE_ID);
//            return null;
//        }
//        String apiUrl = "https://oapi.dingtalk.com/get_jsapi_ticket?access_token=" + accessToken;
//        String result = httpManager.getUrl(apiUrl, createHeader());
//        Integer errCode = Integer.parseInt(JSONPath.read(result, "$.errcode").toString());
//        if (Objects.isNull(errCode) || errCode != DING_SUCCESS) {
//            log.warn("getToken appKey或appSecret错误.");
//            return null;
//        }
//        String ticket = JSONPath.read(result, "$.ticket").toString();
//        return ticket;
//    }


    public Map<String, String> getAdminUserInfo(String code, String access_token) {
        String getAccessUrl = "https://oapi.dingtalk.com/sso/getuserinfo?code=" + code + "&access_token=" + access_token;
        String result = httpManager.getUrl(getAccessUrl, dingtalkApiManager.createHeader());
        Integer errCode = Integer.parseInt(JSONPath.read(result, "$.errcode").toString());
        if (Objects.isNull(errCode) || errCode != DING_SUCCESS) {
            log.warn("getToken appKey或appSecret错误.");
            return null;
        }
        String userId = JSONPath.read(result, "$.user_info.userid").toString();
        String corpId = JSONPath.read(result, "$.corp_info.corpid").toString();
        Map<String, String> objectMap = Maps.newHashMap();
        objectMap.put("userId", userId);
        objectMap.put("corpId", corpId);
        return objectMap;
    }

    @RequestMapping(value = "/registerData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public DingStorageResult<DingObjectDataListModel.Result> registerData(HttpServletRequest httpServletRequest, @RequestParam(value = "corpId", required = false) String enterPriseId, @RequestParam(value = "appId") String appId) {
        ComponentQueryArg componentQueryArg = new ComponentQueryArg();
        String userid = httpServletRequest.getParameter("userid");
        Integer cursor = Integer.valueOf(httpServletRequest.getParameter("cursor"));
        String componentId = httpServletRequest.getParameter("componentId");
        String dataId = httpServletRequest.getParameter("dataId");
        Integer pageSize = Integer.valueOf(httpServletRequest.getParameter("pageSize"));


        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryCorpMappingByCorpId(enterPriseId, appId);
        if (CollectionUtils.isEmpty(corpResult.getData())) {
            return DingStorageResult.newSuccess();
        }
        Result<DingMappingEmployeeResult> empResult = objectMappingService.queryEmpByDingUserId(corpResult.getData().get(0).getEi(), userid, appId);
        if (StringUtils.isEmpty(empResult.getData())) {
            return DingStorageResult.newSuccess();
        }

        // 换取成纷享的客户
        //转换钉钉的对象id与纷享的数据id
        RegisterObjMapping registerObjMapping = ConfigCenter.COMPONENT_MAP.get(componentId);
        Integer tenantId = corpResult.getData().get(0).getEi();
        String initEmpUrl = ConfigCenter.ERP_PUSH_DATA_URL.concat("/out/ding/crm/connector/dingCrmObjectId2FsCrmObjectId?tenantId=").
                concat(tenantId.toString()).concat("&dingObjectApiName=").concat(registerObjMapping.getDingObjApiName()).concat("&dingObjectId=").concat(dataId);
        Map<String, Object> objectMap = httpManager.postUrl(initEmpUrl, Maps.newHashMap(), dingtalkApiManager.createHeader());
        log.info("posturl body:{}",objectMap);
        String errCode= JsonPath.read(objectMap.get("body").toString(),"$.errCode");
        if(!errCode.equals("s106240000")){
            log.warn("query crm objectData fail:{}",dataId);
            return DingStorageResult.newSuccess();
        }
        String data=JsonPath.read(objectMap.get("body").toString(),"$.data");
        componentQueryArg.setAccountId(data);
        componentQueryArg.setEi(corpResult.getData().get(0).getEi());
        componentQueryArg.setEa(corpResult.getData().get(0).getEa());
        componentQueryArg.setApiName(registerObjMapping.getCrmObjApiName());
        componentQueryArg.setFsUserId(empResult.getData().getEmployeeId());
        componentQueryArg.setOffset(cursor);
        componentQueryArg.setLimit(pageSize);
        DingStorageResult<DingObjectDataListModel.Result> eserviceWorkData = dataStorageService.getEserviceWorkData(componentQueryArg);
        eserviceWorkData.getData().getDataList().forEach(item ->{
            String detailUrl= ObjectUtils.isEmpty(item.get("detailUrl"))?null:item.get("detailUrl").toString().replace("$","").replaceAll("CORPID", corpResult.getData().get(0).getDingCorpId());
            item.put("detailUrl",detailUrl);
        });

        String replaceUrl= eserviceWorkData.getData().getCreateUrl().replace("$","").replaceAll("CORPID", corpResult.getData().get(0).getDingCorpId());
        log.info("replaceUrl message:{}",replaceUrl);
        eserviceWorkData.getData().setCreateUrl(replaceUrl);
        log.info("register data result:{},arg:{}", eserviceWorkData);
        return eserviceWorkData;
    }



    @RequestMapping(value = "/registerDataBak", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public DingStorageResult<DingObjectDataListModel.Result> registerDataBak(RegisterArg registerArg, @RequestParam(value = "corpId", required = false) String enterPriseId) {


        DingStorageResult<DingObjectDataListModel.Result> eserviceWorkData = dataStorageService.getEserviceWorkData(null);
        log.info("register data result:{},arg:{}", eserviceWorkData);
        return eserviceWorkData;
    }

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ResponseBody
    public BaseObjSyncResult create(@RequestBody ErpPushDataObj erpPushDataObj, HttpServletRequest request) {
       String tenantId= request.getHeader("tenantId");
//        Result<String> personCustomer = dataStorageService.createPersonCustomer(erpPushDataObj, Integer.valueOf(tenantId));
//        log.info("personCustomer create data:{}",personCustomer);
        BaseObjSyncResult baseObjSyncResult=new BaseObjSyncResult();
//        baseObjSyncResult.setErrCode(personCustomer.getErrorCode());
//        baseObjSyncResult.setErrMsg(personCustomer.getErrorMessage());
        BaseObjSyncResult.CreateObjData createObjData=new BaseObjSyncResult.CreateObjData();
//        createObjData.setMasterDataId(personCustomer.getData());
        baseObjSyncResult.setData(createObjData);
        return baseObjSyncResult ;
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Result<Integer> update(@RequestBody ErpPushDataObj erpPushDataObj,@RequestHeader(required = false, value = "tenant_id") Integer tenantId) {
    
        return Result.newSuccess(tenantId);
    }



//    public static String calcSignature(String apiSecret, long ts) {
//        try {
//            Mac mac = Mac.getInstance("HmacSHA256");
//            SecretKeySpec key = new SecretKeySpec(apiSecret.getBytes(), "HmacSHA256");
//            mac.init(key);
//            return Base64.getEncoder().encodeToString(mac.doFinal(Long.toString(ts).getBytes()));
//        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
//            log.info("calcSignature exception:{}", e.getMessage());
//        }
//        return null;
//    }


//    //获取accessToken
//    public String getAccessToken(String authCorpId, String suiteId) {
//        //TODO 获取suiteTicket
//
//        Result<String> result = syncBizDataService.queryToken(authCorpId, suiteId);
//        log.info("getAccess token:{}", result);
//        if(!result.isSuccess() || StringUtils.isEmpty(result.getData())) {
//            return null;
//        }
//        return result.getData();
//    }


//    public static Map<String, String> createHeader() {
//        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
//        headers.put("Content-Type", "application/json");
//        return headers;
//    }

    public Boolean isNewPagesEi(Integer ei) {
        if(ConfigCenter.pagesEiSet.contains(String.valueOf(ei))) {
            return Boolean.TRUE;
        }

        Integer lowerKey = ConfigCenter.rangeMap.floorKey(ei); // 最接近的开始边界（包含ei的）
        if(lowerKey == null) {
            return Boolean.FALSE; // 没有找到小于等于ei的开始边界
        }

        Integer upperBoundary = ConfigCenter.rangeMap.get(lowerKey); // 对应的结束边界
        return ei >= lowerKey && ei <= upperBoundary; // ei必须在开始和结束边界之间
    }
}
