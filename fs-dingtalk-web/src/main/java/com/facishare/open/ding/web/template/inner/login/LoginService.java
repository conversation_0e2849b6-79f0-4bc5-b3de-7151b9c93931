package com.facishare.open.ding.web.template.inner.login;


import com.facishare.open.ding.web.template.model.Code2UserInfoData;
import com.facishare.open.ding.web.template.model.FsUserModel;
import com.facishare.open.ding.web.template.model.LoginAuthModel;
import com.facishare.open.outer.oa.connector.common.api.result.Result;

public interface LoginService {
    /**
     * 飞书免登录code换飞书用户身份
     * @param loginAuthModel
     * @return
     */
    Result<Code2UserInfoData> code2UserInfo(LoginAuthModel loginAuthModel);

    /**
     * 生成纷享ticket
     * @param corpId
     * @param appId
     * @param userId
     * @param fsEa 一个飞书企业对多个CRM企业场景需要用到这个参数
     * @return
     */
    Result<String> genFsTicket(String corpId, String appId, String userId, String fsEa);

    /**
     * 根据ticket获取纷享用户信息
     * @param ticket
     * @return
     */
    Result<FsUserModel> getFsUser(String ticket);
}