package com.facishare.open.feishu.sync.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.model.connect.AppConnectParams;
import com.facishare.open.feishu.syncapi.result.data.ContactScopeData;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.feishu.syncapi.service.FeishuContactsService;
import com.facishare.open.feishu.syncapi.service.FeishuTenantService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.FeiShuConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.LarkConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 飞书OA连接管理服务 处理飞书连接器的配置校验、保存和授权URL获取等操作
 */
@Slf4j
@Component("feishuOAConnectManager")
public class FeishuOuterOaConnectManager implements OuterAbstractSettingService<FeiShuConnectorVo> {

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    private final ChannelEnum channel = ChannelEnum.feishu;
    @Autowired
    private FeishuTenantService feishuTenantService;
    @Autowired
    private FeishuContactsService feishuContactsService;
    @Autowired
    private ContactsService contactsService;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    /**
     * 保存连接器配置信息 此方法供Dubbo REST调用，确保参数类型兼容性
     *
     * @param outerOAConnectSettingResult 连接器配置
     * @param channel                     渠道类型
     * @param outerOaAppInfoTypeEnum      外部OA应用信息类型
     * @return 处理结果
     */
    @Override
    public Result<Void> doValidateConfigAndSave(OuterOAConnectSettingResult outerOAConnectSettingResult,
            ChannelEnum channel, OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        try {
            // 参数校验
            if (outerOAConnectSettingResult == null) {
                log.error("doValidateConfigAndSave failed: connectorVo is null");
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
            }


            String dataCenterId = outerOAConnectSettingResult.getCurrentDcId();
            // 获取企业绑定实体
            OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
            if(entityById==null){
                return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
            }
            FeiShuConnectorVo feiShuConnectorVo= null;
            if(outerOAConnectSettingResult.getChannelEnum().equals(ChannelEnum.feishu)){
                 feiShuConnectorVo = outerOAConnectSettingResult.getConnectParams().getFeishu();
            }else{
                feiShuConnectorVo=outerOAConnectSettingResult.getConnectParams().getLark();
            }
            Result<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntityResult = validConnect(feiShuConnectorVo, entityById);
            if(!outerOaEnterpriseBindEntityResult.isSuccess()){
                return Result.newError(ResultCodeEnum.FEISHU_APP_SECRET_ERROR);
            }
            AppConnectParams appConnectParams=AppConnectParams.builder().appId(feiShuConnectorVo.getAppId()).appSecret(feiShuConnectorVo.getAppSecret()).build();
            //需要存在应用信息
            OuterOaAppInfoEntity outerOaAppInfoEntity=OuterOaAppInfoEntity.builder()
                    .appId(feiShuConnectorVo.getAppId())
                    .outEa(entityById.getOutEa())
                    .id(IdGenerator.get())
                    .status(OuterOaAppInfoStatusEnum.normal)
                    .channel(entityById.getChannel())
                    .appType(outerOaAppInfoTypeEnum)
                    .appInfo(JSONObject.toJSONString(appConnectParams))
                    .build();
            Integer count = outerOaAppInfoManager.batchUpsertInfos(Lists.newArrayList(outerOaAppInfoEntity));
            log.info("doValidateConfigAndSave success data:{}", JSONObject.toJSONString(outerOaEnterpriseBindEntityResult));
            return Result.newSuccess();
        } catch (Exception e) {
            log.error("doValidateConfigAndSave error: error={}", e.getMessage(), e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    public Result<OuterOaEnterpriseBindEntity> validConnect(FeiShuConnectorVo feiShuConnectorVo,OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity){
        if(OuterOaAppInfoTypeEnum.isv!=feiShuConnectorVo.getAppType()){
            String domain=feiShuConnectorVo.getBaseUrl()==null?"feishu.cn":feiShuConnectorVo.getBaseUrl();
            com.facishare.open.feishu.syncapi.result.Result<QueryTenantInfoData> queryTenantInfoDataResult = feishuTenantService.initQueryTenantInfo(feiShuConnectorVo.getAppId(), feiShuConnectorVo.getAppSecret(), domain);
            if(queryTenantInfoDataResult.isSuccess()){
                log.info("feishu getEnterprise data outea:{}", queryTenantInfoDataResult.getData());
                QueryTenantInfoData data = queryTenantInfoDataResult.getData();
                outerOaEnterpriseBindEntity.setOutEa(data.getTenant().getTenantKey());
                outerOaEnterpriseBindEntity.setBindStatus(BindStatusEnum.normal);
                outerOaEnterpriseBindEntity.setAppId(feiShuConnectorVo.getAppId());
                feiShuConnectorVo.setEnterpriseName(data.getTenant().getName());
                feiShuConnectorVo.setBindStatus(BindStatusEnum.normal);
                feiShuConnectorVo.setDisplayId(data.getTenant().getDisplayId());
                outerOaEnterpriseBindEntity.setConnectInfo(JSONObject.toJSONString(feiShuConnectorVo));
            }else {
                return Result.newError(ResultCodeEnum.FEISHU_APP_SECRET_ERROR);
            }
        }else{
            com.facishare.open.feishu.syncapi.result.Result<QueryTenantInfoData> queryTenantInfoDataResult = feishuTenantService.queryTenantInfo(feiShuConnectorVo.getAppId(), feiShuConnectorVo.getCorpId() );
            if(queryTenantInfoDataResult.isSuccess()){
                QueryTenantInfoData data = queryTenantInfoDataResult.getData();
                feiShuConnectorVo.setCorpId(data.getTenant().getTenantKey());
                feiShuConnectorVo.setDisplayId(data.getTenant().getDisplayId());
                feiShuConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
                feiShuConnectorVo.setEnterpriseName(data.getTenant().getName());
                outerOaEnterpriseBindEntity.setAppId(feiShuConnectorVo.getAppId());
                outerOaEnterpriseBindEntity.setOutEa(data.getTenant().getTenantKey());
            }
            outerOaEnterpriseBindEntity.setBindStatus(BindStatusEnum.normal);

            outerOaEnterpriseBindEntity.setConnectInfo(JSONObject.toJSONString(feiShuConnectorVo));
        }
        outerOaEnterpriseBindManager.batchUpsertById(Lists.newArrayList(outerOaEnterpriseBindEntity));
        return Result.newSuccess(outerOaEnterpriseBindEntity);
    }



    @Override
    public Result<String> doGetAuthUrl(String connectInfo, ChannelEnum channel,
            OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        //获取跳转链接
        FeiShuConnectorVo feiShuConnectorVo = JSONObject.parseObject(connectInfo, FeiShuConnectorVo.class);
        // 获取飞书企业id
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(feiShuConnectorVo.getDataCenterId());

        com.facishare.open.feishu.syncapi.result.Result<String> stringResult = enterpriseBindService.queryScanCodeAuthUrl(entityById.getFsEa(), entityById.getId());
        if (stringResult.isSuccess()) {
            return Result.newSuccess(stringResult.getData());
        }

        return null;
    }

    @Override
    public List<AlertTypeEnum> getChannelSupportedAlertTypes() {
        return Arrays.asList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_SCHEDULE, AlertTypeEnum.CRM_NOTIFICATION);
    }

    @Override
    public Result<String> queryOuterEmpByValue(String dataCenterId, ChannelEnum channel, String queryFieldApiName,
            String queryFieldValue) {
        return null;
    }

    @Override
    public Result<Void> refreshOuterEmpData(String dataCenterId, ChannelEnum channel) {

        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        log.info("refresh outemp data:{}",entityById.getOutEa());
        //获取飞书通讯录数据
        contactsService.initOrSaveContactData(dataCenterId);
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> routeNewPage(String tenantId, ChannelEnum channel) {
        //飞书不走灰度
        return Result.newSuccess(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> unbindConnect(String dataCenterId, ChannelEnum channel) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        enterpriseBindService.fsUnBindWithFeishu(entityById);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> employeeBindChangeEvent(String dataCenterId, List<String> ids, EmplyeeBindChangeTypeEnum type) {
        return null;
    }
}
