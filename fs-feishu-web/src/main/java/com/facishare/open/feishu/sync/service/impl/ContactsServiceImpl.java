package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.model.connect.FeishuEnterpriseConnectParams;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.web.utils.BeanUtil;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutDepartmentInfoDoc;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutUserInfoDoc;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorSyncEventDataDoc;
import com.facishare.open.feishu.sync.threadpool.ThreadPoolHelper;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.DepartmentBindEntity;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.params.*;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.feishu.syncapi.model.ContactScopeModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.ContactScopeData;
import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.*;
import com.facishare.open.order.contacts.proxy.api.arg.FsDeptArg;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.enums.FsEmployeeRoleCodeEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.object.FeishuEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.params.QueryOaConnectorSyncEventDataArg;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service("contactsService")
public class ContactsServiceImpl implements ContactsService {
    @Resource
    private FsContactsServiceProxy fsContactsServiceProxy;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;
    @Resource
    private FeishuContactsService feishuContactsService;
    @Resource
    private FeishuDepartmentService feishuDepartmentService;
    @Resource
    private FeishuUserService feishuUserService;
    @Resource
    private FeishuAppService feishuAppService;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private DepartmentBindManager departmentBindManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;
    @Autowired
    private OuterOaDeptDataManager outerOaDeptDataManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private OaConnectorSyncEventDataManager oaConnectorSyncEventDataManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private OaConnectorOutUserInfoManager oaConnectorOutUserInfoManager;
    @Resource
    private OaConnectorOutDepartmentInfoManager oaConnectorOutDepartmentInfoManager;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private EnterpriseBindService enterpriseBindService;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    /**
     * 其实现在只是异步存储可见范围数据，飞书推送的数据过来，异步存储可见范围数据，同步任务从mongo中获取数据，重新处理数据。
     */
    // TODO 现在只有反绑定的企业，才存储MongoDB数据。
    private static final List<String> refreshContactScopeDataEventTypes = Lists.newArrayList(
            "contact.department.created_v3", "contact.department.updated_v3", "contact.scope.updated_v3",
            "contact.user.created_v3", "contact.user.updated_v3");
    private final String GET_CONTACT_SCOPE_CACHE_KEY = "get_contact_scope_cache_key_";
    @Autowired
    private ObjectDataManager objectDataManager;

    private List<DepartmentData.Department> getAllDepartmentList(String appId,String outerEa,
            List<String> outDepIdList,ChannelEnum channelEnum) {
        List<DepartmentData.Department> departmentList = new ArrayList<>();
        if (CollectionUtils.isEmpty(outDepIdList))
            return departmentList;

        // 拉取所有部门信息
        for (String depId : outDepIdList) {
            Result<DepartmentData.Department> deptInfo = feishuDepartmentService.getDeptInfo(appId,
                    outerEa, depId);
            if (deptInfo.isSuccess()) {
                departmentList.add(deptInfo.getData());
                List<DepartmentData.Department> childDept = getAllChildDepartmentList(appId,
                        outerEa, depId);
                if (CollectionUtils.isNotEmpty(childDept)) {
                    departmentList.addAll(childDept);
                }
            }
        }
        //批量插入更新
        List<OuterOaDeptDataEntity> outerOaDeptDataEntities=Lists.newArrayList();
        for (DepartmentData.Department department : departmentList) {
            OuterOaDeptDataEntity entity = new OuterOaDeptDataEntity();
            entity.setId(IdGenerator.get());
            entity.setChannel(channelEnum);
            entity.setOutEa(outerEa);
            entity.setAppId(appId);
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdateTime(System.currentTimeMillis());
            entity.setDeptName(department.getName());
            entity.setOutDeptInfo(JSONObject.parseObject(JSON.toJSONString(department)));
            entity.setOutDeptId(department.getOpenDepartmentId());
            entity.setParentDeptId(department.getParentDepartmentId());
            outerOaDeptDataEntities.add(entity);
        }
        int count = outerOaDeptDataManager.batchUpdateOutDeptId(outerOaDeptDataEntities);
        LogUtils.info("ContactsServiceImpl.getAllDepartmentList,departmentList.size={}", departmentList.size());
        return departmentList;
    }

    private List<DepartmentData.Department> getAllDepartmentList2(String appId, String outEa,
            List<DepartmentData.Department> departmentList) {
        List<DepartmentData.Department> totalDepartmentList = new ArrayList<>();

        for (DepartmentData.Department department : departmentList) {
            totalDepartmentList.add(department);

            List<DepartmentData.Department> childDepartmentList = getAllChildDepartmentList(appId, outEa,
                    department.getOpenDepartmentId());
            if (CollectionUtils.isNotEmpty(childDepartmentList)) {
                totalDepartmentList.addAll(childDepartmentList);
            }
        }

        LogUtils.info("ContactsServiceImpl.getAllDepartmentList,totalDepartmentList.size={},totalDepartmentList={}",
                totalDepartmentList.size(), totalDepartmentList);

        return totalDepartmentList;
    }

    private List<DepartmentData.Department> getAllChildDepartmentList(String appId, String outEa, String depId) {
        List<DepartmentData.Department> departmentList = new ArrayList<>();

        Result<List<DepartmentData.Department>> result = feishuDepartmentService.getChildDept(appId, outEa, depId,
                true);
        if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            departmentList.addAll(result.getData());
        }

        return departmentList;
    }

    @Override
    public Result<Void> initContactsAsync(String fsEa) {
        Executors.newScheduledThreadPool(1).schedule(() -> {
            initContacts(fsEa);
        }, 3 * 1000L, TimeUnit.MILLISECONDS);
        return Result.newSuccess();
    }

    /**
     * 这里不涉及到多应用企业。因为企业只有一次初始化
     * 
     * @param fsEa
     */
    private void initContacts(String fsEa) {
        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntitiesByOuterEaChanelEnums(Arrays.asList(ChannelEnum.feishu,ChannelEnum.lark), fsEa, null, null);
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = entities.get(0);
        String appId = ObjectUtils.isNotEmpty(outerOaEnterpriseBindEntity.getAppId())
                ? outerOaEnterpriseBindEntity.getAppId()
                : ConfigCenter.feishuCrmAppId;

        Result<ContactScopeData> result = feishuContactsService.getContactScopeData(appId,
                outerOaEnterpriseBindEntity.getOutEa());
        LogUtils.info("ContactsServiceImpl.initContactsAsync,get contact scope dat,result={}", result);
        if (result.isSuccess() == false) {
            return;
        }

        // 拉取所有部门包括子部门信息
        List<DepartmentData.Department> departmentList = getAllDepartmentList(outerOaEnterpriseBindEntity.getAppId(),outerOaEnterpriseBindEntity.getOutEa(),
                result.getData().getDepartmentIds(),outerOaEnterpriseBindEntity.getChannel());

        initDepList(ei, outerOaEnterpriseBindEntity, departmentList, appId);

        initUserList(ei,  outerOaEnterpriseBindEntity, result.getData().getUserIds(), departmentList, appId);

        updateAdminMainDep(appId, ei,  outerOaEnterpriseBindEntity);
    }
    //这里将全部员工放入数据库，内存需要优化？
    @Override
    public Result<ContactScopeModel> getContactScopeData(String appId, String outEa,ChannelEnum channelEnum) {
        Result<ContactScopeData> result = feishuContactsService.getContactScopeData(appId, outEa);
        LogUtils.info("ContactsServiceImpl.initContactsAsync,get contact scope dat,result={}", result);
        if (result.isSuccess() == false) {
            return new Result<>(result.getCode(), result.getMsg(), null);
        }

        // 拉取所有部门包括子部门信息
        List<DepartmentData.Department> departmentList = getAllDepartmentList(appId, outEa,
                result.getData().getDepartmentIds(),channelEnum);

        List<UserData.User> userList = batchGetDepartmentUserList(appId, outEa, departmentList);
        // userIds为空直接跳过
        if (CollectionUtils.isNotEmpty(result.getData().getUserIds())) {
            userList.addAll(batchGetUserList(appId, outEa, result.getData().getUserIds(),channelEnum));
        }

        ContactScopeModel model = new ContactScopeModel();
        model.setUserList(userList);
        model.setDepartmentList(departmentList);

        return Result.newSuccess(model);
    }

    @Override
    public Result<Void> initOrSaveContactData(String dataCenterId) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if (entityById == null) {
            return Result.newSuccess();
        }
        String appId = entityById.getAppId();
        String outEa = entityById.getOutEa();
        Result<ContactScopeData> result = feishuContactsService.getContactScopeData(appId, outEa);
        LogUtils.info("ContactsServiceImpl.initContactsAsync,get contact scope dat,result={}", result);
        if (result.isSuccess() == false) {
            return Result.newSuccess();
        }

        // 拉取所有部门包括子部门信息
        List<DepartmentData.Department> departmentList = getAllDepartmentList(entityById.getAppId(),
                entityById.getOutEa(), result.getData().getDepartmentIds(),entityById.getChannel());


        List<UserData.User> allUserList = getAllUserList(entityById, result.getData().getUserIds(), departmentList,
                appId,entityById.getChannel());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addUserList(String appId, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String outEa,
            List<UserData.User> userList) {

        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        //需要在这里判断规则是不是自动同步
        OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
        if(entityByDataCenterId.getConfigInfo()!=null){
            SettingAccountRulesModel settingAccountRulesModel= JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            if(settingAccountRulesModel.getSyncTypeEnum()!=EnterpriseConfigAccountSyncTypeEnum.accountSync){
                LogUtils.warn("enterprise not support createuser:{}", fsEa);
                return Result.newSuccess();
            }
        }

        for (UserData.User user : userList) {
            OuterOaEmployeeBindParams params = OuterOaEmployeeBindParams.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel()).outEa(outerOaEnterpriseBindEntity.getOutEa())
                    .fsEa(outerOaEnterpriseBindEntity.getFsEa()).outEmpId(user.getOpenId()).build();
            // 根据outEmpId查询,不需要依赖appid，避免多应用影响重新新增
            List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager.getUserNotDependAppid(params);
            if (CollectionUtils.isEmpty((entities))) {
                // 员工不存在，新增员工并更新员工绑定表
                addUser(appId, ei, outerOaEnterpriseBindEntity, user);
            } else {
                String fsEmpId = entities.get(0).getFsEmpId();
                // 员工存在，更新纷享员工状态并更新员工绑定状态
                // fs员工存在，需要判断是不是改应用下的人员。
                List<OuterOaEmployeeBindEntity> userEntities = entities.stream()
                        .filter(item -> item.getAppId().equals(appId)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(userEntities)) {
                    // 需要插入员工绑定这个应用
                    OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = OuterOaEmployeeBindEntity.builder()
                            .fsEmpId(fsEmpId).fsEa(fsEa).outEa(outEa).appId(appId).outEmpId(user.getOpenId())
                            .bindStatus(BindStatusEnum.normal).createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis()).build();
                    outerOaEmployeeBindManager.insert(outerOaEmployeeBindEntity);
                }
                com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.toggle(
                        ei + "", fsEmpId, true, Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                        FsEmployeeRoleCodeEnum.SALES.getRoleCode());
                LogUtils.info("ContactsServiceImpl.addUserList,result={}", result);
                outerOaEmployeeBindManager.batchUpdateBindStatus(fsEa, Lists.newArrayList(fsEmpId),
                        BindStatusEnum.normal, outEa, appId);
            }

        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> removeUserList(String appId, String outEa, List<UserData.User> userList,
            OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {

        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        for (UserData.User user : userList) {
            OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel())
                    // .appId(appId)
                    .outEmpId(user.getOpenId()).outEa(outerOaEnterpriseBindEntity.getOutEa()).build();
            // 这里需要判断是否有这个应用下的员工，如果有，则更新状态为停用，如果没有，则不处理。
            List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager
                    .getEntities(outerOaEmployeeBindParams);
            if (CollectionUtils.isNotEmpty(entities)) {
                // 获取当前应用下的员工记录
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = entities.stream()
                        .filter(item -> appId.equals(item.getAppId())).findFirst().orElse(null);
                // 1000号员工不处理
                if("1000".equals(outerOaEmployeeBindEntity.getFsEmpId())){
                    LogUtils.info("delete 1000 user not allow");
                    continue;
                }
            }
            objectDataManager.removeEmpData(outerOaEnterpriseBindEntity, user.getOpenId(),RemoveEmployeeEventType.RESIGN_EMPLOYEE);
            LogUtils.info("remove empdata :{}",user);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> removeRangeUserList(String appId, String outEa, List<UserData.User> userList, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {
        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        for (UserData.User user : userList) {
            OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel())
                    // .appId(appId)
                    .outEmpId(user.getOpenId()).outEa(outerOaEnterpriseBindEntity.getOutEa()).build();
            // 这里需要判断是否有这个应用下的员工，如果有，则更新状态为停用，如果没有，则不处理。
            List<OuterOaEmployeeBindEntity> entities = outerOaEmployeeBindManager
                    .getEntities(outerOaEmployeeBindParams);
            if (CollectionUtils.isNotEmpty(entities)) {
                // 获取当前应用下的员工记录
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = entities.stream()
                        .filter(item -> appId.equals(item.getAppId())).findFirst().orElse(null);
                // 1000号员工不处理
                if("1000".equals(outerOaEmployeeBindEntity.getFsEmpId())){
                    LogUtils.info("delete 1000 user not allow");
                    continue;
                }
            }
            objectDataManager.removeEmpData(outerOaEnterpriseBindEntity, user.getOpenId(),RemoveEmployeeEventType.REMOVE_RANGE);
            LogUtils.info("removeRangeUserList empdata :{}",user);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addDepList(String appId, String outEa, List<DepartmentData.Department> departmentList,
            OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {

        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        for (DepartmentData.Department department : departmentList) {
            // 查询部门绑定关系
            // 先查询部门有没有绑定了（去掉apid查询）
            OuterOaDepartmentBindParams outerOaDepartmentBindParams = OuterOaDepartmentBindParams.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel()).outEa(outEa).fsEa(fsEa)
                    .outDepId(department.getOpenDepartmentId()).build();
            List<OuterOaDepartmentBindEntity> outerOaDepartmentBindEntities = outerOaDepartmentBindManager
                    .getEntities(outerOaDepartmentBindParams);
            List<DepartmentData.Department> allDepList = getAllDepartmentList2(appId, outEa,
                    Lists.newArrayList(department));
            List<UserData.User> departmentUserList = batchGetDepartmentUserList(appId, outEa, allDepList);
            if (CollectionUtils.isEmpty(outerOaDepartmentBindEntities)) {
                // 1. 创建纷享部门包括子部门并更新部门绑定表
                batchAddDepartment(ei, outerOaEnterpriseBindEntity, allDepList, appId);
                // 2. 创建纷享员工并更新员工绑定表
                addUserList(appId, outerOaEnterpriseBindEntity, outEa, departmentUserList);
            } else {
                OuterOaDepartmentBindEntity dependDeptEntities = outerOaDepartmentBindEntities.stream()
                        .filter(item -> !item.getAppId().equals(outerOaEnterpriseBindEntity.getAppId())).findFirst().orElse(null);
                if (dependDeptEntities == null) {
                    // 需要插入部门绑定这个应用
                    String fsDeptId = outerOaDepartmentBindEntities.get(0).getFsDepId();
                    OuterOaDepartmentBindEntity outerOaDepartmentBindEntity = new OuterOaDepartmentBindEntity();
                    outerOaDepartmentBindEntity.setChannel(outerOaEnterpriseBindEntity.getChannel());
                    outerOaDepartmentBindEntity.setFsEa(fsEa);
                    outerOaDepartmentBindEntity.setOutEa(outEa);
                    outerOaDepartmentBindEntity.setDcId(outerOaEnterpriseBindEntity.getId());
                    outerOaDepartmentBindEntity.setAppId(appId);
                    outerOaDepartmentBindEntity.setFsDepId(fsDeptId);
                    outerOaDepartmentBindEntity.setOutDepId(department.getOpenDepartmentId());
                    outerOaDepartmentBindEntity.setBindStatus(BindStatusEnum.normal);
                    outerOaDepartmentBindEntity.setCreateTime(System.currentTimeMillis());
                    outerOaDepartmentBindEntity.setUpdateTime(System.currentTimeMillis());
                    outerOaDepartmentBindManager.batchInsert(Lists.newArrayList(outerOaDepartmentBindEntity));
                }
                //需要编辑部门信息
                OuterOaDepartmentBindEntity parentEntity = outerOaDepartmentBindManager.getDeptEntityByDcId(outerOaEnterpriseBindEntity.getId(), null, department.getParentDepartmentId());
                FsDeptArg updateArg = FsDeptArg.builder().ei(ei + "").name(department.getName())
                        .code(department.getOpenDepartmentId()).status("0").build();
                OuterOaEmployeeBindEntity employeeLeaderEntity = outerOaEmployeeBindManager.getEntitiesByDcId(outerOaEnterpriseBindEntity.getId(), null, department.getLeaderUserId());
                if(parentEntity !=null){
                    updateArg.setParentId(Lists.newArrayList(parentEntity.getFsDepId()));
                }
                if(employeeLeaderEntity !=null){
                    updateArg.setParentId(Lists.newArrayList(employeeLeaderEntity.getFsEmpId()));
                }
                fsDepartmentServiceProxy.update(updateArg);
                // 1. 批量启用当前部门及子部门纷享员工
                batchResumeFsEmployee(ei, outerOaEnterpriseBindEntity, department);

                // 2. 启用当前纷享部门及子部门
                batchResumeFsDepartment(ei, outerOaEnterpriseBindEntity, department);

                // 3. 更新部门下所有纷享员工的部门
                updateUserMainDept(ei, outerOaEnterpriseBindEntity, departmentUserList);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> removeDepList(String appId, String outEa, List<DepartmentData.Department> departmentList,
            OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {

        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for (DepartmentData.Department department : departmentList) {
            OuterOaDepartmentBindEntity departmentBindEntity = outerOaDepartmentBindManager.getByOutDeptId(outerOaEnterpriseBindEntity.getChannel(),
                    outerOaEnterpriseBindEntity.getOutEa(), fsEa,appId,
                    department.getOpenDepartmentId());
            if (departmentBindEntity == null) {
                continue;
            }
            //需要删除员工数据
            Integer deleteDeptCount = outerOaEmployeeDataManager.delelteDataByDeptId(outEa, outerOaEnterpriseBindEntity.getChannel(), outerOaEnterpriseBindEntity.getAppId(), department.getOpenDepartmentId());
            LogUtils.info("delete dept data count:{}",deleteDeptCount);
            // 1. 批量停用当前部门及子部门纷享员工并更新绑定表为已停用
            batchStopFsEmployee(ei, departmentBindEntity.getFsDepId(), departmentBindEntity.getOutDepId(),outerOaEnterpriseBindEntity);

            // 2. 批量停用当前纷享部门及子部门并更新部门绑定表为已停用
            batchStopFsDepartment(ei, departmentBindEntity.getFsDepId(), outerOaEnterpriseBindEntity);

        }

        return Result.newSuccess();
    }

    private void batchStopFsEmployee(int ei, String fsDepId,String outerDeptId, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {
        // com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>>
        // listResult = fsEmployeeServiceProxy.list(ei,fsDepId);
        // LogUtils.info("ContactsServiceImpl.batchStopEmployee,listResult={}",
        // listResult);
        // if(listResult.isSuccess()==false) return;
        //
        // List<String> fsUserIdList =
        // listResult.getData().stream().map(ObjectData::getId).collect(Collectors.toList());
        //
        // //1.批量停用当前部门纷享员工
        // com.facishare.open.order.contacts.proxy.api.result.Result<Void> result =
        // fsEmployeeServiceProxy.bulkStop(ei + "",
        // fsUserIdList);
        // LogUtils.info("ContactsServiceImpl.batchStopEmployee,result={}", result);
        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        // 后续考虑挪到ObjectDataManager
        OuterOaConfigInfoEntity entityByAutoFieldDeFault = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
        if (ObjectUtils.isNotEmpty(entityByAutoFieldDeFault)) {
            SettingAccountRulesModel settingAccountRulesModel = JSON
                    .parseObject(entityByAutoFieldDeFault.getConfigInfo(), SettingAccountRulesModel.class);
            if (settingAccountRulesModel.getEmployeeRangeRemoveRule().getStopEmp()) {
                com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchStopFsEmp = fsContactsServiceProxy
                        .batchStopFsEmp(ei, fsEa, fsDepId, null);
                // 2.批量更新员工绑定状态为停用
                if (batchStopFsEmp.isSuccess()) {
                    outerOaEmployeeBindManager.batchUpdateEmpStatus(outerOaEnterpriseBindEntity.getChannel(), fsEa,
                            batchStopFsEmp.getData(), BindStatusEnum.stop, outEa,
                            outerOaEnterpriseBindEntity.getAppId());
                }


            }
        }

    }

    private void batchStopFsDepartment(int ei, String fsDepId,
            OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {
        // objectDataManager.removeDepartment(outerOaEnterpriseBindEntity, fsDepId);
        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        String appId = outerOaEnterpriseBindEntity.getAppId();
        com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchStopFsDep = fsContactsServiceProxy
                .batchStopFsDep(ei, outerOaEnterpriseBindEntity.getFsEa(), fsDepId);
        // 2.批量更新部门绑定状态为停用
        if (batchStopFsDep.isSuccess()) {
            outerOaDepartmentBindManager.updateStatusByFsDeptIds(outerOaEnterpriseBindEntity.getChannel(), fsEa, outEa,
                    appId, batchStopFsDep.getData(), BindStatusEnum.stop);
        }
    }

    private void batchResumeFsEmployee(int ei, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            DepartmentData.Department department) {
        // 获取当前部门的所有子部门
        String appId = outerOaEnterpriseBindEntity.getAppId();
        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        String dcId = outerOaEnterpriseBindEntity.getId();
        List<DepartmentData.Department> departmentList = getAllDepartmentList2(appId, outEa,
                Lists.newArrayList(department));
        List<String> fsUserIdList = getFsUserIdList(appId, dcId, fsEa, outEa, departmentList);

        // 1.批量启用当前部门纷享员工
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.bulkResume(
                ei + "", fsUserIdList, Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                FsEmployeeRoleCodeEnum.SALES.getRoleCode());
        LogUtils.info("ContactsServiceImpl.batchResumeFsEmployee,result={}", result);
        // 2.批量更新员工绑定状态为正常
        outerOaEmployeeBindManager.batchUpdateBindStatus(fsEa, fsUserIdList, BindStatusEnum.normal, outEa, appId);
    }

    private void batchResumeFsDepartment(int ei, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            DepartmentData.Department department) {

        // 获取当前部门的所有子部门
        String appId = outerOaEnterpriseBindEntity.getAppId();
        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        String dcId = outerOaEnterpriseBindEntity.getId();
        List<String> fsDepIdList = getFsDepIdList(appId, outEa, Lists.newArrayList(department));
        // //1.批量启用当前部门及子部门
        // for(String depId : fsDepIdList) {
        // com.facishare.open.order.contacts.proxy.api.result.Result<Void> result =
        // fsDepartmentServiceProxy.toggle(ei + "",
        // depId,true);
        // LogUtils.info("ContactsServiceImpl.toggle,enable department,result={}",
        // result);
        // }

        com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchResumeFsDep = fsContactsServiceProxy
                .batchResumeFsDep(ei, fsEa, fsDepIdList);

        // 2.批量更新部门绑定状态为正常
        if (batchResumeFsDep.isSuccess()) {
            outerOaEmployeeBindManager.batchUpdateBindStatus(fsEa, fsDepIdList, BindStatusEnum.normal, outEa, appId);
        }
    }

    /**
     * 初始化纷享用户
     *
     * @param entity
     * @param userIdList
     * @param departmentList
     */
    private List<UserData.User> initUserList(Integer ei, OuterOaEnterpriseBindEntity entity,
            List<String> userIdList, List<DepartmentData.Department> departmentList, String appId) {
        String fsEa = entity.getFsEa();
        List<UserData.User> totalUserList = getAllUserList(entity, userIdList, departmentList, appId,entity.getChannel());
        // 创建CRM员工并入库
        batchAddUser(appId, ei, entity, totalUserList);

        // 4.查询当前企业根部门下所有员工并更新负责人信息
        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> listResult = fsEmployeeServiceProxy
                .listAll(ei, GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
        if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
            for (ObjectData objectData : listResult.getData()) {
                EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntityByFsUserId(fsEa,
                        objectData.getId(), entity.getOutEa());
                LogUtils.info("ContactsServiceImpl.initUserList,employeeBindEntity={}", employeeBindEntity);
                if (employeeBindEntity == null)
                    continue;

                EmployeeBindEntity leaderBinderEntity = null;
                if (StringUtils.isNotEmpty(employeeBindEntity.getOutLeaderUserId())) {
                    leaderBinderEntity = employeeBindManager.getEntity(employeeBindEntity.getOutEa(),
                            employeeBindEntity.getOutLeaderUserId(), fsEa);
                    LogUtils.info("ContactsServiceImpl.initUserList,leaderBinderEntity={}", leaderBinderEntity);
                }
                if (leaderBinderEntity == null)
                    continue;

                FsEmpArg arg = FsEmpArg.builder().ei(ei + "").id(objectData.getId())
                        .leader(Lists.newArrayList(leaderBinderEntity.getFsUserId())).build();
                // 更新纷享员工负责人
                com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy
                        .update(arg, null, null);
                LogUtils.info("ContactsServiceImpl.initUserList,update employee leader,updateResult={}", updateResult);
                if (updateResult.isSuccess() == false) {
                    LogUtils.info(
                            "ContactsServiceImpl.initUserList,update employee leader failed,arg={},updateResult={}",
                            arg, updateResult);
                }
            }
        }
        return totalUserList;
    }

    private List<UserData.User> getAllUserList(OuterOaEnterpriseBindEntity entity, List<String> userIdList,
            List<DepartmentData.Department> departmentList, String appId,ChannelEnum channelEnum) {
        List<UserData.User> totalUserList = new ArrayList<>();
        // 1.拉取应用可见范围飞书用户信息
        if (CollectionUtils.isNotEmpty(userIdList)) {
            totalUserList.addAll(batchGetUserList(appId, entity.getOutEa(), userIdList,channelEnum));
        }

        // 2.拉取应用可见范围内部门用户信息
        List<UserData.User> userList = batchGetDepartmentUserList(appId, entity.getOutEa(), departmentList);
        if (CollectionUtils.isNotEmpty(userList)) {
            totalUserList.addAll(userList);
        }
        return totalUserList;
    }

    /**
     * 批量新增纷享员工并插入中间表
     *
     * @param ei
     * @param outerOaEnterpriseBindEntity
     * @param userList
     */
    private void batchAddUser(String appId, Integer ei, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, List<UserData.User> userList) {
        for (UserData.User user : userList) {
            addUser(appId, ei,outerOaEnterpriseBindEntity, user);
        }
    }

    private void addUser(String appId, Integer ei, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            UserData.User user) {
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        String sex = null;
        if (user.getGender() == 1) {
            sex = "M";
        } else if (user.getGender() == 2) {
            sex = "F";
        }

        if (CollectionUtils.isEmpty(user.getDepartment_ids())) {
            List<UserData.User> userList = batchGetUserList(appId, outEa, Lists.newArrayList(user.getOpenId()),outerOaEnterpriseBindEntity.getChannel());
            user.setDepartment_ids(userList.get(0).getDepartment_ids());
        }
        // todotest
        com.facishare.open.outer.oa.connector.common.api.result.Result<ActionAddResult> employee = objectDataManager
                .createEmployee(outerOaEnterpriseBindEntity, user.getOpenId());
        if (employee.isSuccess()) {
            OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = OuterOaEmployeeBindEntity.builder()
                    .channel(outerOaEnterpriseBindEntity.getChannel()).fsEa(fsEa)
                    .appId(outerOaEnterpriseBindEntity.getAppId())
                    .fsEmpId(employee.getData().getObjectData().getId()).outEa(outEa).outEmpId(user.getOpenId())
                    .bindStatus(BindStatusEnum.normal).dcId(outerOaEnterpriseBindEntity.getId()).build();
            outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(outerOaEmployeeBindEntity));
            LogUtils.info("ContactsServiceImpl.addUser,create,arg={}", outerOaEmployeeBindEntity);
        }

        // List<String> fsDepIdList = getFsDepIdList(outEa, user.getDepartment_ids());
        // List<String> mainDepIdList = CollectionUtils.isEmpty(fsDepIdList)
        // ? Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "")
        // : fsDepIdList;
        //
        // FsEmpArg arg = FsEmpArg.builder().ei(ei + "")
        // .name(StringUtils.isEmpty(user.getNickname()) ? user.getName() :
        // user.getNickname())
        // .fullName(user.getName()).sex(sex).phone(user.getMobile()) // 商店应用获取不到值，永远为空
        // .mainDepartment(mainDepIdList).viceDepartments(new
        // ArrayList<>()).status("0").isActive(true).build();
        // LogUtils.info("ContactsServiceImpl.addUser,create,arg={}", arg);
        // com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result
        // = fsEmployeeServiceProxy.create(
        // arg, Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
        // FsEmployeeRoleCodeEnum.SALES.getRoleCode());

        // LogUtils.info("ContactsServiceImpl.addUser,create,result={}", result);
        // if (result.isSuccess()) {
        //
        // LogUtils.info("ContactsServiceImpl.addUser,insert employee mapping,count={}",
        // count);
        // } else {
        // LogUtils.info("ContactsServiceImpl.addUser,create fs user failed", result);
        // }
    }

    private List<UserData.User> batchGetUserList(String appId, String outEa, List<String> userList,ChannelEnum channelEnum) {
        List<UserData.User> totalUserList = new ArrayList<>();
        for (String userId : userList) {
            Result<UserData.User> userInfo = feishuUserService.getUserInfo(appId, outEa, userId);
            if (userInfo.isSuccess()) {
                String mobile=userInfo.getData().getMobile();
                if(StringUtils.isNotEmpty(mobile)){
                    userInfo.getData().setMobile(mobile.replace("+86", ""));
                }
                totalUserList.add(userInfo.getData());
            }
        }
        // 插入数据
        List<FeishuEmployeeObject> feishuEmployeeObjects = BeanUtil.copyList(totalUserList, FeishuEmployeeObject.class);
        // 插入数据库
        List<OuterOaEnterpriseBindEntity> entitiesByOutEa = outerOaEnterpriseBindManager
                .getEntitiesByOutEa(channelEnum, outEa, appId);
        if (CollectionUtils.isEmpty(entitiesByOutEa)) {
            LogUtils.warn("No enterprise bind entities found for outEa={}, appId={}, channel={}", outEa, appId, channelEnum);
            return totalUserList;
        }
        
        String dataCenterId = entitiesByOutEa.get(0).getId();
        
        try {
            // 使用分批处理方法，每批处理50条记录
            Integer userUpdateCount = outerOaEmployeeDataManager.batchUpsert(feishuEmployeeObjects, channelEnum, dataCenterId);
            LogUtils.info("ContactsServiceImpl.batchGetUserList - user update count: {}", userUpdateCount);
        } catch (Exception e) {
            LogUtils.error("Error in batch upsert operation: {}", e.getMessage(), e);
            
            // 如果批量更新失败，尝试使用分批处理
            LogUtils.info("Trying chunked batch processing instead");
            
            // 将FeishuEmployeeObject转换为OuterOaEmployeeDataEntity
            List<OuterOaEmployeeDataEntity> entities = new ArrayList<>();
            long now = System.currentTimeMillis();
            
            for (FeishuEmployeeObject obj : feishuEmployeeObjects) {
                OuterOaEmployeeDataEntity entity = new OuterOaEmployeeDataEntity();
                entity.setId(IdGenerator.get());
                entity.setChannel(channelEnum);
                entity.setOutEa(outEa);
                entity.setAppId(appId);
                entity.setOutUserId(obj.getOpenId());
                entity.setOutUserInfo((JSONObject) JSONObject.toJSON(obj));
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                entities.add(entity);
            }
            
            // 使用分批处理，每批次50条数据
            Integer chunkUpdateCount = outerOaEmployeeDataManager.batchUpsertInChunks(entities, 50);
            LogUtils.info("ContactsServiceImpl.batchGetUserList - chunked update count: {}", chunkUpdateCount);
        }
        
        return totalUserList;
    }

    private List<UserData.User> getDepartmentUserList(String appId, String outEa,
            DepartmentData.Department department) {
        List<UserData.User> userList = new ArrayList<>();
        Result<List<UserData.User>> userListResult = feishuUserService.getAllDepartmentUsers(appId, outEa,
                department.getOpenDepartmentId());
        if (userListResult.isSuccess() && CollectionUtils.isNotEmpty(userListResult.getData())) {
            userListResult.getData().forEach(user -> {
                if(user.getMobile()!=null){
                    String mobile=user.getMobile();
                    user.setMobile(mobile.replace("+86", ""));
                }
            });
            userList.addAll(userListResult.getData());
        }
        return userList;
    }

    private List<UserData.User> batchGetDepartmentUserList(String appId, String outEa,
            List<DepartmentData.Department> departmentList) {
        List<UserData.User> totalUserList = new ArrayList<>();
        // dataCenterId_fixmore
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().outEa(outEa)
                .appId(appId).build();
        List<OuterOaEnterpriseBindEntity> entitiesByOutEa = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = entitiesByOutEa.get(0);
        for (DepartmentData.Department department : departmentList) {
            List<UserData.User> userList = getDepartmentUserList(appId, outEa, department);
            if (CollectionUtils.isNotEmpty(userList)) {
                totalUserList.addAll(userList);
                // 插入数据库
                List<FeishuEmployeeObject> feishuEmployeeObjects = BeanUtil.copyList(userList,
                        FeishuEmployeeObject.class);
                Integer count = outerOaEmployeeDataManager.batchUpsert(feishuEmployeeObjects,
                        outerOaEnterpriseBindEntity.getChannel(), outerOaEnterpriseBindEntity.getId());
            }
        }
        return totalUserList;
    }

    /**
     * 初始化纷享部门
     *
     * @param ei
     * 
     * @param entity
     * @param departmentList
     */
    private void initDepList(Integer ei, OuterOaEnterpriseBindEntity entity,
            List<DepartmentData.Department> departmentList, String appId) {
        batchAddDepartment(ei, entity, departmentList, appId);
    }

    private void batchAddDepartment(Integer ei, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            List<DepartmentData.Department> departmentList, String appId) {
        for (DepartmentData.Department department : departmentList) {
            addDepartment(ei, outerOaEnterpriseBindEntity, department, appId);
        }
    }

    private void addDepartment(Integer ei, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            DepartmentData.Department department, String appId) {
        // 1.查询父部门绑定信息
        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        ChannelEnum channelEnum = outerOaEnterpriseBindEntity.getChannel();

        OuterOaDepartmentBindEntity parentDepEntity = outerOaDepartmentBindManager.getByOutDeptId(channelEnum, outEa,outerOaEnterpriseBindEntity.getFsEa(),
                appId, department.getParentDepartmentId());
        LogUtils.info("ContactsServiceImpl.initDepList,parentDepEntity={}", parentDepEntity);
        List<String> parentIdList = null;
        String fsLeaderUserId = null;
        if (parentDepEntity != null) {
            parentIdList = Lists.newArrayList(parentDepEntity.getFsDepId());
        } else {
            parentIdList = Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
        }
        // 2.查询飞书部门Leader对应的纷享userId
        if (StringUtils.isNotEmpty(department.getLeaderUserId())) {

            OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder()
                    .dcId(outerOaEnterpriseBindEntity.getId()).outEmpId(department.getLeaderUserId()).fsEa(fsEa)
                    .appId(appId).build();

            List<OuterOaEmployeeBindEntity> leaderBindEntity = outerOaEmployeeBindManager
                    .getEntities(outerOaEmployeeBindParams);
            if (CollectionUtils.isNotEmpty(leaderBindEntity)) {
                fsLeaderUserId = leaderBindEntity.get(0).getFsEmpId();
            }
        }

        FsDeptArg arg = FsDeptArg.builder().ei(ei + "").name(department.getName())
                .code(department.getOpenDepartmentId()).status("0").parentId(parentIdList)
                .managerId(StringUtils.isNotEmpty(fsLeaderUserId) ? Lists.newArrayList(fsLeaderUserId) : null).build();
        LogUtils.info("ContactsServiceImpl.initDepList,createArg={}", arg);
        // 3.创建纷享部门
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> createDepResult = fsDepartmentServiceProxy
                .create(arg);
        LogUtils.info("ContactsServiceImpl.initDepList,createDepResult={}", createDepResult);
        if (createDepResult.isSuccess() == false) {
            LogUtils.info("ContactsServiceImpl.initDepList,create dep failed,break,createArg={}", arg);
            return;
        }
        OuterOaDepartmentBindEntity outerOaDepartmentBindEntity = OuterOaDepartmentBindEntity.builder()
                .appId(appId)
                .channel(outerOaEnterpriseBindEntity.getChannel()).fsEa(fsEa).fsDepId(createDepResult.getData().getId())
                .outEa(outEa).dcId(outerOaEnterpriseBindEntity.getId()).createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis()).outDepId(department.getOpenDepartmentId())
                .bindStatus(BindStatusEnum.normal).build();

        // 4.插入部门绑定表数据
        Integer count = outerOaDepartmentBindManager.batchUpsert(Lists.newArrayList(outerOaDepartmentBindEntity));
        LogUtils.info("ContactsServiceImpl.initDepList,insert department mapping,count={}", count);
    }

    /**
     * 更新纷享用户主属部门信息
     *
     * @param ei
     * @param outerOaEnterpriseBindEntity
     * @param totalUserList
     */
    private void updateUserMainDept(Integer ei, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            List<UserData.User> totalUserList) {
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        for (UserData.User user : totalUserList) {
            OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager
                    .getEntitiesByDcId(outerOaEnterpriseBindEntity.getId(), user.getOpenId(), user.getUserId());
            LogUtils.info("ContactsServiceImpl.updateUserMainDep,employeeBindEntity={}", employeeBindEntity);
            if (employeeBindEntity == null) {
                continue;
            }
            List<String> fsDepList = getFsDepIdList(outEa, user.getDepartment_ids());
            if (CollectionUtils.isEmpty(fsDepList)) {
                continue;
            }
            // 这里应该不能支持多个主属部门。后续需要考虑修改
            FsEmpArg arg = FsEmpArg.builder().ei(ei + "").id(employeeBindEntity.getFsEmpId())
                    .mainDepartment(Lists.newArrayList(fsDepList)).build();
            // 更新纷享员工主属部门
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy
                    .update(arg, null, null);
            LogUtils.info("ContactsServiceImpl.updateUserMainDep,update employee main department,updateResult={}",
                    updateResult);
            if (updateResult.isSuccess() == false) {
                LogUtils.info(
                        "ContactsServiceImpl.initUserList,update employee main department failed,arg={},updateResult={}",
                        arg, updateResult);
            }
        }
    }

    /**
     * 更新管理员主属部门
     *
     * @param ei

     * @param outerOaEnterpriseBindEntity
     */
    private void updateAdminMainDep(String appId, Integer ei,
            OuterOaEnterpriseBindEntity enterpriseBindEntity) {
        String outEa = enterpriseBindEntity.getOutEa();
        OuterOaEmployeeBindEntity entitiesByDcId = outerOaEmployeeBindManager
                .getEntitiesByDcId(enterpriseBindEntity.getId(), "1000", null);
        if (entitiesByDcId == null)
            return;

        Result<UserData.User> result = feishuUserService.getUserInfo(appId, outEa, entitiesByDcId.getOutEmpId());
        if (result.isSuccess()) {
            updateUserMainDept(ei, enterpriseBindEntity, Lists.newArrayList(result.getData()));
        }
    }

    private List<String> getFsUserIdList(String appId, String dataCenterId, String fsEa, String outEa,
            List<DepartmentData.Department> departmentList) {
        List<UserData.User> userList = batchGetDepartmentUserList(appId, outEa, departmentList);
        List<String> openIds = userList.stream().map(UserData.User::getOpenId).collect(Collectors.toList());
        List<OuterOaEmployeeBindEntity> entitiesByDcId = outerOaEmployeeBindManager.getEntitiesByDcId(dataCenterId,
                openIds, null);
        List<String> userIdList = entitiesByDcId.stream().map(OuterOaEmployeeBindEntity::getFsEmpId)
                .collect(Collectors.toList());
        return userIdList;
    }

    private List<String> getFsDepIdList(String outEa, List<String> outDepIdList) {
        List<String> fsDepList = new ArrayList<>();
        for (String depId : outDepIdList) {
            // 飞书根部门对应纷享根部门
            if (StringUtils.equalsIgnoreCase(depId, "0")) {
                fsDepList.add(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
                continue;
            }
            DepartmentBindEntity entityByOutEa = departmentBindManager.getEntityByOutEa(outEa, depId);
            if (entityByOutEa == null) {
                continue;
            }
            fsDepList.add(entityByOutEa.getFsDepId());
        }
        LogUtils.info("ContactsServiceImpl.getFsDepIdList,fsDepList={}", fsDepList);

        return fsDepList;
    }

    private List<String> getFsDepIdList(String appId, String outEa,
            List<DepartmentData.Department> departmentDataList) {
        List<DepartmentData.Department> allDepList = getAllDepartmentList2(appId, outEa, departmentDataList);

        List<String> fsDepIdList = new ArrayList<>();
        for (DepartmentData.Department department : allDepList) {
            DepartmentBindEntity entity = departmentBindManager.getEntity(outEa, department.getOpenDepartmentId());
            if (entity == null) {
                continue;
            }
            fsDepIdList.add(entity.getFsDepId());
        }

        return fsDepIdList;
    }

    @Override
    public Result<Void> resumeEmployee(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String outUserId) {

        OuterOaEmployeeBindEntity entitiesByDcId = outerOaEmployeeBindManager.getEntitiesByDcId(outerOaEnterpriseBindEntity.getId(), null, outUserId);
        if (entitiesByDcId == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        outerOaEmployeeBindManager.updateEmpStatusById(entitiesByDcId.getId(),BindStatusEnum.normal,outerOaEnterpriseBindEntity.getFsEa());
        int ei = eieaConverter.enterpriseAccountToId(outerOaEnterpriseBindEntity.getFsEa());
        List<String> roleCodeList = Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode());
        String mainRoleCode = FsEmployeeRoleCodeEnum.SALES.getRoleCode();
        if (!StringUtils.equalsIgnoreCase(entitiesByDcId.getFsEmpId(), "1000")) {
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy
                    .bulkResume(ei + "", Lists.newArrayList(entitiesByDcId.getFsEmpId()), roleCodeList,
                            mainRoleCode);
            LogUtils.info("ContactsServiceImpl.resumeEmployee,result={}", result);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> stopEmployee(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String outUserId) {
        OuterOaEmployeeBindEntity entitiesByDcId = outerOaEmployeeBindManager.getEntitiesByDcId(outerOaEnterpriseBindEntity.getId(), null, outUserId);
        if (entitiesByDcId == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        if (!StringUtils.equalsIgnoreCase(entitiesByDcId.getFsEmpId(), "1000")) {
            // 只有非CRM管理员，才允许停用
            com.facishare.open.outer.oa.connector.common.api.result.Result<Void> voidResult = objectDataManager.removeEmpData(outerOaEnterpriseBindEntity, outUserId, RemoveEmployeeEventType.RESIGN_EMPLOYEE);
            LogUtils.info("ContactsServiceImpl.resumeEmployee,result={}", voidResult);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> autoGetContactData() {
        int pageNum = 0;
        int size;
        QueryOaConnectorSyncEventDataArg arg = new QueryOaConnectorSyncEventDataArg();
        arg.setChannelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark));
        // arg.setAppId(ConfigCenter.feishuCrmAppId);
        arg.setEventType(refreshContactScopeDataEventTypes);
        arg.setStatus(0);
        arg.setPageSize(1000);
        // 分页循环，防止单次拉取数据太大导致oom
        Map<String, Set<OuterOaAppInfoParams>> eaLists = Maps.newHashMap();
        do {
            arg.setPageNum(pageNum++);
            List<OaConnectorSyncEventDataDoc> syncEventDataDocs = oaConnectorSyncEventDataManager
                    .pageByQuerySyncEventDataArg(arg);

            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(syncEventDataDocs)) {
                for (OaConnectorSyncEventDataDoc syncEventDataDoc : syncEventDataDocs) {
                    eaLists.computeIfAbsent(syncEventDataDoc.getOutEa(), k -> Sets.newHashSet())
                            .add(OuterOaAppInfoParams.builder().appId(syncEventDataDoc.getAppId()).channel(syncEventDataDoc.getChannel()).build());
                }
            }
            size = syncEventDataDocs.size();
        } while (size == 1000);
        LogUtils.info("ContactsServiceImpl.autoGetContactData,outEas={}", eaLists.size());
        if (eaLists.isEmpty()) {
            return Result.newSuccess();
        }
        // 删除数据，再丢线程池
        for (String outEa : eaLists.keySet()) {
            QueryOaConnectorSyncEventDataArg delArg = new QueryOaConnectorSyncEventDataArg();
            // delArg.setChannel(ChannelEnum.feishu);
            // delArg.setAppId(ConfigCenter.feishuCrmAppId);
            delArg.setEventType(refreshContactScopeDataEventTypes);
            delArg.setStatus(0);
            delArg.setOutEa(outEa);
            DeleteResult result = oaConnectorSyncEventDataManager.deleteTableDataByDelArg(delArg);
            LogUtils.info("ContactsServiceImpl.autoGetContactData,result={}", result);
            for (OuterOaAppInfoParams appInfoParams : eaLists.get(outEa)) {
                ThreadPoolHelper.saveOrUpdateContactDataThreadPool.submit(() -> {
                    TraceUtils.initTraceId("OA_BASE_"+outEa+UUID.randomUUID());
                    saveOrUpdateContactData(appInfoParams.getAppId(), outEa,appInfoParams.getChannel());
                });
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<ContactScopeModel> saveOrUpdateContactData(String appId, String outEa, ChannelEnum channelEnum) {
        LogUtils.info("ContactsServiceImpl.saveOrUpdateContactData,appId={},outEa={}", appId, outEa);
        // 分布式锁
        RLock rLock = redissonClient.getLock(GET_CONTACT_SCOPE_CACHE_KEY + appId + outEa);
        Result<ContactScopeModel> contactScopeModelResult = null;
        try {
            boolean isGetLock = rLock.tryLock(60 * 5, 60 * 5, TimeUnit.SECONDS);
            LogUtils.info("ContactsServiceImpl.saveOrUpdateContactData,Asynchronous threading,isGetLock={}", isGetLock);
            if (isGetLock) {
                try {
                    contactScopeModelResult = getContactScopeData(appId, outEa,channelEnum);
                    if (!contactScopeModelResult.isSuccess()) {
                        return contactScopeModelResult;
                    }
                    ContactScopeModel contactScopeModel = contactScopeModelResult.getData();
                    List<OuterOaEnterpriseBindEntity> entitiesByOutEa = outerOaEnterpriseBindManager.getEntitiesByOutEa(channelEnum, outEa, appId);
                    for (OuterOaEnterpriseBindEntity enterpriseBindEntity : entitiesByOutEa) {
                        if (ObjectUtils.isNotEmpty(contactScopeModel.getUserList())) {
                            saveOrUpdateOutUserInfo(appId, enterpriseBindEntity, channelEnum, contactScopeModel, Boolean.TRUE);
                        }

                        if (ObjectUtils.isNotEmpty(contactScopeModel.getDepartmentList())) {
                            saveOrUpdateOutDepartmentInfo(appId, outEa, channelEnum, contactScopeModel, Boolean.TRUE);
                        }
                    }

                } finally {
                    rLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ObjectUtils.isNotEmpty(contactScopeModelResult) ? contactScopeModelResult
                : Result.newSuccess(new ContactScopeModel());
    }

    @Override
    public Result<Void> refreshContactScopeDataCacheAsync(String eventType, String appId, String outEa,
            String eventData, ChannelEnum channelEnum) {
        LogUtils.info("ContactsServiceImpl.refreshContactScopeDataCacheAsync,outEa={},eventData={}", outEa, eventData);
        OaConnectorSyncEventDataDoc doc = new OaConnectorSyncEventDataDoc();
        doc.setId(ObjectId.get());
        doc.setAppId(appId);
        doc.setOutEa(outEa);
        doc.setStatus(0);
        doc.setEventType(eventType);
        doc.setChannel(channelEnum);
        doc.setEvent(eventData);
        doc.setCreateTime(System.currentTimeMillis());
        doc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult = oaConnectorSyncEventDataManager.batchReplace(Lists.newArrayList(doc));
        LogUtils.info("ContactsServiceImpl.refreshContactScopeDataCacheAsync,bulkWriteResult={}", bulkWriteResult);
        return Result.newSuccess();
    }

    private void saveOrUpdateOutUserInfo(String appId, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,ChannelEnum channelEnum,
            ContactScopeModel contactScopeModel, Boolean isDeleteNotInCollectionDocs) {

        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        String dataCenterId = outerOaEnterpriseBindEntity.getId();
        List<FeishuEmployeeObject> feishuEmployeeObjects = BeanUtil.copyList(contactScopeModel.getUserList(),
                FeishuEmployeeObject.class);
        //需要注意，手机区号需要去掉
        feishuEmployeeObjects.stream().forEach(item ->{
            String mobile = item.getMobile();
            if(StringUtils.isNotEmpty(mobile)){
                item.setMobile(mobile.replace("+86", ""));
            }});

        // 删除不在应用的数据
        if (isDeleteNotInCollectionDocs) {
            Set<String> outEmpList = feishuEmployeeObjects.stream().map(FeishuEmployeeObject::getOpenId)
                    .collect(Collectors.toSet());
            Integer outEmpDataIds = outerOaEmployeeDataManager.deleteInvisibleUsers(outEa, channelEnum, appId,
                    outEmpList);
            LogUtils.info("ContactsServiceImpl.saveOrUpdateOutUserInfo,user,outEmpDataIds={}", outEmpDataIds);
        }

        ThreadPoolHelper.autoBindThreadPool.submit(() -> {
            // 通过outEa查找对应的fsEa
            if ( ObjectUtils.isEmpty(contactScopeModel)) {
                return;
            }
            employeeBindService.autoBindBySettingUnique(outerOaEnterpriseBindEntity, outerOaEnterpriseBindEntity.getAppId(),
                    contactScopeModel);
            // 是否灰度
//            for (OuterOaEnterpriseBindEntity entity : entities) {

//                if (ConfigCenter.AUTO_BIND_ACCOUNT_BY_NAME_EA.contains(entity.getFsEa())) {
//                    employeeBindService.autoBindEmpBySameName(outEa, entity.getFsEa(), contactScopeModel);
//                } else if (entity.getConnectInfo() != null) {
//
//                    FeishuEnterpriseConnectParams connectParam = entity.getChannel()
//                            .getConnectParam(entity.getConnectInfo());
//                    // 底层做了判空
//                    String autoField = connectParam.getAutoField();
//                    employeeBindService.autoBindEmpByFeishuEmployeeNumber(entity, entity.getAppId(),
//                            contactScopeModel, autoField);
//                }
//            }
        });
    }

    private void saveOrUpdateOutDepartmentInfo(String appId, String outEa, ChannelEnum channelEnum,
            ContactScopeModel contactScopeModel, Boolean isDeleteNotInCollectionDocs) {
        Gson gson = new Gson();
        List<OaConnectorOutDepartmentInfoDoc> docs = new LinkedList<>();
        List<OuterOaDeptDataEntity> outerOaDeptDataEntities = Lists.newArrayList();
        for (DepartmentData.Department department : contactScopeModel.getDepartmentList()) {
            OuterOaDeptDataEntity outerOaDeptDataEntity = new OuterOaDeptDataEntity();
            outerOaDeptDataEntity.setId(IdGenerator.get());
            outerOaDeptDataEntity.setOutEa(outEa);
            outerOaDeptDataEntity.setChannel(channelEnum);
            outerOaDeptDataEntity.setAppId(appId);
            outerOaDeptDataEntity.setOutDeptId(department.getOpenDepartmentId());
            outerOaDeptDataEntity.setDeptName(department.getName());
            outerOaDeptDataEntity.setOutDeptInfo(JSONObject.parseObject(JSON.toJSONString(department)));
            outerOaDeptDataEntity.setCreateTime(System.currentTimeMillis());
            outerOaDeptDataEntity.setUpdateTime(System.currentTimeMillis());
            outerOaDeptDataEntities.add(outerOaDeptDataEntity);
        }
        int bulkWriteResult = outerOaDeptDataManager.batchInsert(outerOaDeptDataEntities);
        LogUtils.info("ContactsServiceImpl.saveOrUpdateOutDepartmentInfo,department,bulkWriteResult={}",
                bulkWriteResult);
        if (isDeleteNotInCollectionDocs) {
            // TODO fix
            // DeleteResult deleteResult = oaConnectorOutDepartmentInfoManager
            // .deleteNotInCollectionDocs(ChannelEnum.feishu, outEa, appId,docs);
            Set<String> visibleDeptIds = docs.stream().map(doc -> doc.getOutDepartmentId()).collect(Collectors.toSet());
            Integer deleteDepts = outerOaDeptDataManager.deleteInvisibleDepts(outEa, channelEnum, appId,
                    visibleDeptIds);
            LogUtils.info("ContactsServiceImpl.saveOrUpdateOutDepartmentInfo,department,deleteResult={}", deleteDepts);
        }
    }

    @Override
    public Result<Void> saveOrUpdateContactUser(String eventType, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, ChannelEnum channelEnum,
            UserData.User user, UserData.User oldUser, String eventData) {
        String outEa=outerOaEnterpriseBindEntity.getOutEa();
        String appId=outerOaEnterpriseBindEntity.getAppId();
        Long count = outerOaEmployeeDataManager.selectCount(channelEnum, outEa, appId);
        if (count == null || count == 0) {
            refreshContactScopeDataCacheAsync(eventType, appId, outEa, eventData, channelEnum);
            return Result.newSuccess();
        }
        if (ObjectUtils.isNotEmpty(oldUser) && CollectionUtils.isNotEmpty(oldUser.getDepartment_ids())) {

            List<OuterOaDeptDataEntity> outerOaDeptDataEntities = outerOaDeptDataManager.batchGetByOutDeptIds(outEa,
                    channelEnum, appId, user.getDepartment_ids());
            LogUtils.info("ContactsServiceImpl.saveOrUpdateContactUser,departmentInfoDocs.size={}",
                    outerOaDeptDataEntities.size());
            if (CollectionUtils.isEmpty(outerOaDeptDataEntities)) {
                deleteContactUser(channelEnum, appId, outEa, user);
                return Result.newSuccess();
            }
        }
        ContactScopeModel contactScopeModel = new ContactScopeModel();
        //需要注意，手机区号需要去掉
        if(ObjectUtils.isNotEmpty(user.getMobile())){
            user.setMobile(user.getMobile().replace("+86", ""));
        }
        contactScopeModel.setUserList(Lists.newArrayList(user));
        List<FeishuEmployeeObject> feishuEmployeeObjects = BeanUtil.copyList(contactScopeModel.getUserList(),
                FeishuEmployeeObject.class);
        Integer upsertCount = outerOaEmployeeDataManager.batchUpsert(feishuEmployeeObjects, channelEnum, outerOaEnterpriseBindEntity.getId());
        saveOrUpdateOutUserInfo(appId, outerOaEnterpriseBindEntity, channelEnum, contactScopeModel, Boolean.FALSE);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteContactUser(ChannelEnum channelEnum, String appId, String outEa, UserData.User user) {
        Integer deleteResult = outerOaEmployeeDataManager.deleteByUserId(channelEnum, outEa, appId, user.getUnionId());
        LogUtils.info("ContactsServiceImpl.deleteContactUser,deleteResult={}", deleteResult);
        return null;
    }

    @Override
    public Result<Void> deleteContactDepartment(ChannelEnum channelEnum,String appId, String outEa, DepartmentData.Department department) {
        DeleteResult deleteResult = oaConnectorOutDepartmentInfoManager.deleteDepartmentInfoByUserId(channelEnum,
                outEa, department.getOpenDepartmentId());
        LogUtils.info("ContactsServiceImpl.deleteContactDepartment,deleteResult={}", deleteResult);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> saveOrUpdateContactDepartment(String eventType,
            OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, DepartmentData.Department department,
            DepartmentData.Department oldDepartment, String eventData) {
        String appId = outerOaEnterpriseBindEntity.getAppId();
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        ChannelEnum channel = outerOaEnterpriseBindEntity.getChannel();
        OuterOaDeptDataParams outerOaDeptDataParams = OuterOaDeptDataParams.builder()
                .outEa(outerOaEnterpriseBindEntity.getOutEa()).channel(outerOaEnterpriseBindEntity.getChannel())
                .appId(outerOaEnterpriseBindEntity.getAppId()).build();
        Long count = outerOaDeptDataManager.queryCount(outerOaDeptDataParams);
        if (count == null || count == 0) {
            refreshContactScopeDataCacheAsync(eventType, appId, outEa, eventData, channel);
            return Result.newSuccess();
        }
        if (ObjectUtils.isNotEmpty(oldDepartment) && StringUtils.isNotEmpty(oldDepartment.getParentDepartmentId())) {
            refreshContactScopeDataCacheAsync(eventType, appId, outEa, eventData, channel);
            return Result.newSuccess();
        }
        ContactScopeModel contactScopeModel = new ContactScopeModel();
        contactScopeModel.setDepartmentList(Lists.newArrayList(department));
        saveOrUpdateOutDepartmentInfo(appId, outEa, channel, contactScopeModel, Boolean.FALSE);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addUser(String appId, String outEa, UserData.User user) {
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams=
                OuterOaEnterpriseBindParams.builder()
                        .outEa(outEa)
                        .appId(appId)
                        .build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);


        if (CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);

        String fsEa = enterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        List<OuterOaEmployeeBindEntity> entitiesOrOuterFsEmpId = outerOaEmployeeBindManager.getEntitiesOrOuterFsEmpId(enterpriseBindEntity.getId(), null, user.getOpenId());

        if (entitiesOrOuterFsEmpId == null) {
            // 员工不存在，新增员工并更新员工绑定表
            String sex = null;
            if (user.getGender() == 1) {
                sex = "M";
            } else if (user.getGender() == 2) {
                sex = "F";
            }

            if (CollectionUtils.isEmpty(user.getDepartment_ids())) {
                List<UserData.User> userList = batchGetUserList(ConfigCenter.feishuCrmAppId, outEa,
                        Lists.newArrayList(user.getOpenId()),enterpriseBindEntity.getChannel());
                user.setDepartment_ids(userList.get(0).getDepartment_ids());
            }

            List<String> fsDepIdList = getFsDepIdList(outEa, user.getDepartment_ids());
            List<String> mainDepIdList = CollectionUtils.isEmpty(fsDepIdList)
                    ? Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "")
                    : fsDepIdList;

            FsEmpArg arg = FsEmpArg.builder().ei(ei + "")
                    .name(StringUtils.isEmpty(user.getNickname()) ? user.getName() : user.getNickname())
                    .fullName(user.getName()).sex(sex).phone(user.getMobile()) // 商店应用获取不到值，永远为空
                    .mainDepartment(mainDepIdList).status("0").isActive(true).build();
            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy
                    .create(arg, Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                            FsEmployeeRoleCodeEnum.SALES.getRoleCode());
            if (result.isSuccess()) {
                EmployeeBindEntity addEmployeeBindEntity = EmployeeBindEntity.builder().channel(ChannelEnum.feishu)
                        .fsEa(fsEa).fsUserId(result.getData().getId()).outEa(outEa).outUserId(user.getOpenId())
                        .outLeaderUserId(user.getLeader_user_id()).bindStatus(BindStatusEnum.normal)
                        .bindType(BindTypeEnum.auto).build();
                int count = employeeBindManager.insert(addEmployeeBindEntity);
                LogUtils.info("ContactsServiceImpl.addUser,insert employee mapping,count={}", count);
            } else {
                return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(result.getCode()),
                        ResultCodeEnum.CRM_USER_ACCOUNT_CREATE_ERROR.getCode()), result.getMsg());
            }
        } else {
            OuterOaEmployeeBindEntity outerOaEmployeeBindEntity=entitiesOrOuterFsEmpId.get(0);
            // 员工存在，更新纷享员工状态并更新员工绑定状态
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.toggle(
                    ei + "", outerOaEmployeeBindEntity.getFsEmpId(), true,
                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                    FsEmployeeRoleCodeEnum.SALES.getRoleCode());
            LogUtils.info("ContactsServiceImpl.addUserList,result={}", result);
            employeeBindManager.batchUpdateBindStatus(fsEa, Lists.newArrayList(outerOaEmployeeBindEntity.getFsEmpId()),
                    BindStatusEnum.normal, outEa);
        }

        return Result.newSuccess();
    }
}
