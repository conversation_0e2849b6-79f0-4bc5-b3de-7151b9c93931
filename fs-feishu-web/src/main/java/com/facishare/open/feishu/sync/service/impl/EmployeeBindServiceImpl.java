package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.model.connect.AppConnectParams;
import com.facishare.open.feishu.syncapi.model.connect.FeishuEnterpriseConnectParams;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.mongo.document.FsDepartmentInfoDoc;
import com.facishare.open.oa.base.dbproxy.mongo.document.FsUserInfoDoc;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutDepartmentInfoDoc;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutUserInfoDoc;
import com.facishare.open.feishu.sync.threadpool.ThreadPoolHelper;
import com.facishare.open.feishu.syncapi.arg.InsertEmployeeBindArg;
import com.facishare.open.feishu.syncapi.arg.QueryEmployeeBindArg;
import com.facishare.open.feishu.syncapi.arg.UpdateEmployeeBindArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.consts.Constant;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.feishu.syncapi.info.EmployeeBindInfo;
import com.facishare.open.feishu.syncapi.model.*;
import com.facishare.open.feishu.syncapi.model.event2.FeishuContactUserUpdatedV3Event;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.model.info.EmployeesBindSyncInfo;
import com.facishare.open.feishu.syncapi.model.info.FsEmployeeDetailInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.result.data.GetAppAdminUserListData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.*;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.PageUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.info.EmployeeBindModel;
import com.facishare.open.outer.oa.connector.common.api.info.PageModel;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.params.FeishuAppConnectParams;
import com.facishare.open.outer.oa.connector.common.api.result.EmpDataListItemVO;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.result.FindEmployeeDtoByFullNameResult;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByNameResult;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataQueryListResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.mongodb.bulk.BulkWriteResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("employeeBindService")
public class EmployeeBindServiceImpl implements EmployeeBindService {
    @Resource
    private FeishuUserService feishuUserService;
    @Resource
    private FeishuAppService feishuAppService;
    @Resource
    private AppInfoManager appInfoManager;
    @Resource
    private CorpInfoManager corpInfoManager;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;
    @Resource
    private ContactsService contactsService;
    @Resource
    private RedisDataSource redisDataSource;
    @Resource
    private FsUserInfoManager fsUserInfoManager;
    @Resource
    private FsDepartmentInfoManager fsDepartmentInfoManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private OaConnectorSyncEventDataManager oaConnectorSyncEventDataManager;
    @Resource
    private DepartmentBindService departmentBindService;
    @Resource
    private OaConnectorOutDepartmentInfoManager oaConnectorOutDepartmentInfoManager;
    @Resource
    private OaConnectorOutUserInfoManager oaConnectorOutUserInfoManager;
    @Resource
    private I18NStringManager i18NStringManager;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    private final String FS_EMPLOYEE_DATA_CACHE_KEY = "fs_employee_data_cache_key_";

    private ExecutorService executorService = Executors.newCachedThreadPool();
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    private ObjectDataManager objectDataManager;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;

    @Override
    public Result<UserData.User> getAppInstallerInfo(String appId, String tenantKey) {
        OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(null, tenantKey, appId);
        LogUtils.info("OuterOaAppInfoManager.getEntity:{}:{}:{},entityes:{}",tenantKey,appId,appInfoEntity);
        if (ObjectUtils.isEmpty(appInfoEntity)) {
            return Result.newError(ResultCodeEnum.GET_CRM_INSTALLER_FAILED);
        }
        AppConnectParams appConnectParams=JSONObject.parseObject(appInfoEntity.getAppInfo(), AppConnectParams.class);
        String openUserId = appConnectParams.getInstallerOpenId();
        if(StringUtils.isEmpty(openUserId)){
            openUserId=appConnectParams.getInstallerEmployeeOpenId();
        }
        Result<UserData.User> singleUserInfo = feishuUserService.getUserInfo(appId, tenantKey, openUserId);
        LogUtils.info("EmployeeBindServiceImpl.getAppInstallerInfo,singleUserInfo={}", singleUserInfo);
        if (!singleUserInfo.isSuccess()) {
            return Result.newError(singleUserInfo.getCode(), singleUserInfo.getMsg());
        }
        return singleUserInfo;
    }

    @Override
    public Result<UserData.User> getAppAdminInfo(String appId, String tenantKey) {
        Result<GetAppAdminUserListData> result = feishuAppService.getAdminUserList(appId, tenantKey);
        if (result.isSuccess() == false) {
            return Result.newError(result.getCode(), result.getMsg());
        }
        String openUserId = result.getData().getUserList().get(0).getOpenId();
        Result<UserData.User> userResult = feishuUserService.getUserInfo(appId, tenantKey, openUserId);
        return Result.newSuccess(userResult.getData());
    }

    @Override
    public Result<PageModel<List<EmployeeBindModel>>> searchBind(String fsEa, String outEa, String content, Integer pageSize) {
        Result<List<EmployeeBindModel>> result = queryBindOld(fsEa, outEa);
        if(result.isSuccess()==false)
            return Result.newError(result.getCode(),result.getMsg());

        List<EmployeeBindModel> matchedList = result.getData();
        if(StringUtils.isNotEmpty(content)) {
            matchedList = matchedList
                    .stream()
                    .filter(v -> StringUtils.containsIgnoreCase(v.getFsEmployee().getUserName(),content)
                    || StringUtils.containsIgnoreCase(v.getFsEmployee().getMobile(),content))
                    .collect(Collectors.toList());
        }

        return getPagedBindData(matchedList,pageSize);
    }

    @Override
    public Result<PageModel<List<EmployeeBindModel.FsEmployee>>> searchUnBind(String fsEa, String outEa, String content, Integer pageSize) {
        Result<List<EmployeeBindModel.FsEmployee>> result = queryFsUnbindOld(fsEa, outEa);
        if(result.isSuccess()==false) {
            return Result.newError(result.getCode(),result.getMsg());
        }

        List<EmployeeBindModel.FsEmployee> matchedList = result.getData();
        if(StringUtils.isNotEmpty(content)) {
            matchedList = matchedList
                    .stream()
                    .filter(v -> StringUtils.containsIgnoreCase(v.getUserName(),content)
                    || StringUtils.containsIgnoreCase(v.getMobile(),content))
                    .collect(Collectors.toList());
        }

        return getPagedFsUnbind(matchedList,pageSize);
    }

    @Override
    public Result<PageModel<List<EmployeeBindModel>>> queryBind(String fsEa, String outEa, Integer pageSize) {
        Result<List<EmployeeBindModel>> result = queryBindOld(fsEa, outEa);
        if(result.isSuccess()==false)
            return Result.newError(result.getCode(),result.getMsg());

        return getPagedBindData(result.getData(),pageSize);
    }

    private Result<PageModel<List<EmployeeBindModel>>> getPagedBindData(List<EmployeeBindModel> employeeBindList,Integer pageSize) {
        PageModel<List<EmployeeBindModel>> pageModel = new PageModel<>();
        List<List<EmployeeBindModel>> pageList = PageUtils.getPageList(employeeBindList,pageSize);
        pageModel.setPageList(pageList);
        pageModel.setPageSize(pageSize);
        pageModel.setTotalPage(pageList.size());
        pageModel.setTotal(employeeBindList.size());

        return Result.newSuccess(pageModel);
    }

    private Result<List<EmployeeBindModel>> queryBindOld(String fsEa, String outEa) {
        String tenantId = eieaConverter.enterpriseAccountToId(fsEa) + "";
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindManager.getEntity(fsEa, outEa);
        if(enterpriseBindEntity==null) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByTenantKey(enterpriseBindEntity.getOutEa());
        if(corpInfoEntity==null) {
            return Result.newError(ResultCodeEnum.CRM_APP_NOT_INSTALLED);
        }

        List<EmployeeBindEntity> entityList = employeeBindManager.getEntityList(fsEa, outEa);
        if(CollectionUtils.isEmpty(entityList)) {
            return Result.newSuccess(new LinkedList<>());
        }
        List<Integer> fsUserList = entityList.stream()
                .map(item->Integer.valueOf(item.getFsUserId()))
                .collect(Collectors.toList());
        List<EmployeeDto> employeeEditionDataList = getEmployeeDtoList(fsEa, null, fsUserList, null);
        Map<Integer, EmployeeDto> employeeDtoMap = employeeEditionDataList.stream()
                .collect(Collectors.toMap(EmployeeDto::getEmployeeId, Function.identity(), (existing, replacement) -> existing));
        Set<Integer> departmentIds = employeeEditionDataList.stream()
                .filter(v -> ObjectUtils.isNotEmpty(v.getMainDepartmentId()))
                .map(EmployeeDto::getMainDepartmentId).collect(Collectors.toSet());
        departmentIds.add(GlobalValue.ALL_COMPANY_DEPARTMENT_ID);
        departmentIds.add(GlobalValue.UNALLOCATED_DEPARTMENT_ID);
        List<DepartmentDto> getDepartmentDtoList = getDepartmentDtoList(fsEa, null, new LinkedList<>(departmentIds));
        Map<Integer, DepartmentDto> departmentDtoMap = getDepartmentDtoList.stream()
                .collect(Collectors.toMap(DepartmentDto::getDepartmentId, Function.identity(), (existing, replacement) -> existing));
        //主动拉取范围人员
        String appId=ObjectUtils.isEmpty(enterpriseBindEntity.getAppId())?ConfigCenter.feishuCrmAppId:enterpriseBindEntity.getAppId();
        ContactScopeModel contactScopeModel = getContactScopeData(enterpriseBindEntity.getOutEa(),appId,ChannelEnum.feishu);
        Map<String, DepartmentData.Department> departmentMap = contactScopeModel.getDepartmentList().stream()
                .collect(Collectors.toMap(DepartmentData.Department::getOpenDepartmentId, Function.identity(), (existing, replacement) -> existing));
        Map<String, UserData.User> userMap = contactScopeModel.getUserList().stream()
                .collect(Collectors.toMap(UserData.User::getOpenId, Function.identity(), (existing, replacement) -> existing));
        String lang = TraceUtils.getLocale();
        List<EmployeeBindModel> employeeBindList = entityList.parallelStream()
                .map(entity -> {
                    if(!employeeDtoMap.containsKey(Integer.valueOf(entity.getFsUserId()))) {
                        return null;
                    }
                    EmployeeDto employeeDto = employeeDtoMap.get(Integer.valueOf(entity.getFsUserId()));

                    EmployeeBindModel.FsEmployee fsEmployee = getFsEmployee(employeeDto, departmentDtoMap);

                    EmployeeBindModel.OutEmployee outEmployee = getOutEmployee(userMap,departmentMap,entity.getOutUserId(),corpInfoEntity.getTenantName(),tenantId, lang);

                    EmployeeBindModel model = new EmployeeBindModel();
                    model.setFsEmployee(fsEmployee);
                    model.setOutEmployee(outEmployee);
                    return model;
                })
                .filter(ObjectUtils::isNotEmpty)
                .collect(Collectors.toList());
        return Result.newSuccess(employeeBindList);
    }

    private EmployeeBindModel.FsEmployee getFsEmployee(EmployeeDto employeeDto,Map<Integer, DepartmentDto> departmentDtoMap) {
        EmployeeBindModel.FsEmployee fsEmployee = new EmployeeBindModel.FsEmployee();
        fsEmployee.setUserId(employeeDto.getEmployeeId() + "");
        fsEmployee.setUserName(employeeDto.getName());
        fsEmployee.setMobile(employeeDto.getMobile());
        fsEmployee.setEmail(employeeDto.getEmail());
        fsEmployee.setStatus(employeeDto.getStatus().getValue());

        Integer mainDepId = null;
        List<Integer> depIdList = employeeDto.getMainDepartmentIds();
        if (CollectionUtils.isEmpty(depIdList)) {
            depIdList = Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID);
        }
        mainDepId = depIdList.get(0);

        fsEmployee.setDepartmentId(mainDepId + "");
        DepartmentDto departmentDto = departmentDtoMap.get(mainDepId);
        if (departmentDto != null) {
            fsEmployee.setDepartmentName(departmentDto.getName());
        }
        return fsEmployee;
    }

    private EmployeeBindModel.OutEmployee getOutEmployee(Map<String, UserData.User> userMap,
            Map<String, DepartmentData.Department> departmentMap, String outUserId, String corpName, String tenantId,
            String lang) {
        if (!userMap.containsKey(outUserId)) {
            // 对于已绑定的，
            EmployeeBindModel.OutEmployee outEmployee = new EmployeeBindModel.OutEmployee();
            outEmployee.setUserId(outUserId);
            outEmployee.setUserName(i18NStringManager.get(I18NStringEnum.s134, lang, tenantId));
            outEmployee.setDepartmentId("0");
            outEmployee.setDepartmentName(i18NStringManager.get(I18NStringEnum.s135, lang, tenantId));
            return outEmployee;
        }

        UserData.User user = userMap.get(outUserId);

        // 员工在可见范围，可见范围有部门，但是这些部门不是该员工的所属部门时会报错
        DepartmentData.Department departmentData;
        if (CollectionUtils.isNotEmpty(user.getDepartment_ids())) {
            if (!departmentMap.containsKey(user.getDepartment_ids().get(0))) {
                departmentData = new DepartmentData.Department();
                if (StringUtils.equalsIgnoreCase(user.getDepartment_ids().get(0), "0")) {
                    departmentData.setDepartmentId("0");
                    departmentData.setName(corpName);
                } else {
                    departmentData.setDepartmentId(user.getDepartment_ids().get(0));
                    departmentData.setName(i18NStringManager.get(I18NStringEnum.s135, lang, tenantId));
                }
            } else {
                departmentData = departmentMap.get(user.getDepartment_ids().get(0));
            }
        } else {
            departmentData = new DepartmentData.Department();
            departmentData.setDepartmentId("0");
            departmentData.setName(i18NStringManager.get(I18NStringEnum.s135, lang, tenantId));
        }

        EmployeeBindModel.OutEmployee outEmployee = new EmployeeBindModel.OutEmployee();
        outEmployee.setUserId(outUserId);
        outEmployee.setUserName(user.getName());
        outEmployee.setDepartmentId(
                CollectionUtils.isNotEmpty(user.getDepartment_ids()) ? user.getDepartment_ids().get(0) : "0");
        outEmployee.setDepartmentName(departmentData.getName());

        return outEmployee;
    }

    private DepartmentDto getDepartmentDto(int departmentId, List<DepartmentDto> departmentDtoList) {
        for (DepartmentDto departmentDto : departmentDtoList) {
            if (departmentDto.getDepartmentId() == departmentId) {
                return departmentDto;
            }
        }
        return null;
    }

    // public Result<Void> autoBindEmpBySameName(String outEa, String fsEa,
    // ContactScopeModel contactScopeModel) {
    // LogUtils.info("EmployeeBindServiceImpl.autoBindEmpBySameName,fsEa={},user.size={}",
    // fsEa,
    // contactScopeModel.getUserList().size());
    // int ei = eieaConverter.enterpriseAccountToId(fsEa);
    // // 去掉已绑定的员工
    // List<EmployeeBindEntity> bindEntityList =
    // employeeBindManager.getEntityList(fsEa, outEa);
    // List<String> outUserList =
    // bindEntityList.stream().map(EmployeeBindEntity::getOutUserId)
    // .collect(Collectors.toList());
    // List<EmployeeBindEntity> employeeBindEntityList = new LinkedList<>();
    // for (UserData.User user : contactScopeModel.getUserList()) {
    // // 过滤掉已绑定的飞书员工
    // if (outUserList.contains(user.getOpenId()))
    // continue;
    // // 通过名称或者昵称进行匹配
    // int fsUserId = 0;
    // if (StringUtils.isNotEmpty(user.getName())) {
    // com.facishare.open.order.contacts.proxy.api.result.Result<FindEmployeeDtoByFullNameResult>
    // employeeDtoByFullNameResult = fsEmployeeServiceProxy
    // .getEmployeesByFullName(ei, user.getName());
    // if (employeeDtoByFullNameResult.isSuccess()
    // && ObjectUtils.isNotEmpty(employeeDtoByFullNameResult.getData())
    // &&
    // CollectionUtils.isNotEmpty(employeeDtoByFullNameResult.getData().getEmployeeDtos()))
    // {
    // fsUserId =
    // employeeDtoByFullNameResult.getData().getEmployeeDtos().get(0).getEmployeeId();
    // } else {
    // com.facishare.open.order.contacts.proxy.api.result.Result<GetEmployeesDtoByNameResult>
    // employeesDtoByNameResult = fsEmployeeServiceProxy
    // .getEmployeesByName(ei, user.getName());
    // if (employeesDtoByNameResult.isSuccess()
    // && ObjectUtils.isNotEmpty(employeesDtoByNameResult.getData())
    // &&
    // ObjectUtils.isNotEmpty(employeesDtoByNameResult.getData().getEmployeeDto()))
    // {
    // fsUserId =
    // employeesDtoByNameResult.getData().getEmployeeDto().getEmployeeId();
    // }
    // }
    // }
    // if (fsUserId == 0 && StringUtils.isNotEmpty(user.getNickname())) {
    // com.facishare.open.order.contacts.proxy.api.result.Result<FindEmployeeDtoByFullNameResult>
    // employeeDtoByFullNameResult = fsEmployeeServiceProxy
    // .getEmployeesByFullName(ei, user.getNickname());
    // if (employeeDtoByFullNameResult.isSuccess()
    // && ObjectUtils.isNotEmpty(employeeDtoByFullNameResult.getData())
    // &&
    // CollectionUtils.isNotEmpty(employeeDtoByFullNameResult.getData().getEmployeeDtos()))
    // {
    // fsUserId =
    // employeeDtoByFullNameResult.getData().getEmployeeDtos().get(0).getEmployeeId();
    // } else {
    // com.facishare.open.order.contacts.proxy.api.result.Result<GetEmployeesDtoByNameResult>
    // employeesDtoByNameResult = fsEmployeeServiceProxy
    // .getEmployeesByName(ei, user.getNickname());
    // if (employeesDtoByNameResult.isSuccess()
    // && ObjectUtils.isNotEmpty(employeesDtoByNameResult.getData())
    // &&
    // ObjectUtils.isNotEmpty(employeesDtoByNameResult.getData().getEmployeeDto()))
    // {
    // fsUserId =
    // employeesDtoByNameResult.getData().getEmployeeDto().getEmployeeId();
    // }
    // }
    // }
    // if (fsUserId == 0) {
    // continue;
    // }
    // Set<String> fsAccountsWithNotBind =
    // employeeBindEntityList.stream().map(EmployeeBindEntity::getFsUserId)
    // .collect(Collectors.toSet());
    // if (CollectionUtils.isNotEmpty(fsAccountsWithNotBind)
    // && fsAccountsWithNotBind.contains(String.valueOf(fsUserId))) {
    // continue;
    // }
    // EmployeeBindEntity employeeBindEntity =
    // EmployeeBindEntity.builder().channel(ChannelEnum.feishu).fsEa(fsEa)
    // .fsUserId(String.valueOf(fsUserId)).outEa(outEa).outUserId(user.getOpenId())
    // .outLeaderUserId(user.getLeader_user_id()).bindStatus(BindStatusEnum.normal)
    // .bindType(BindTypeEnum.manual).build();
    // employeeBindEntityList.add(employeeBindEntity);
    // EmployeeBindEntity employeeBindManagerEntity =
    // employeeBindManager.getEntity(outEa, user.getOpenId(), fsEa);
    // if (ObjectUtils.isNotEmpty(employeeBindManagerEntity)) {
    // LogUtils.info("EmployeeBindServiceImpl.addUser,select employee
    // mapping,fsEa={},outEa={},user", fsEa,
    // outEa, user);
    // continue;
    // }
    // employeeBindManager.insert(employeeBindEntity);
    // }
    // LogUtils.info("EmployeeBindServiceImpl.autoBindEmpBySameName,employeeBindEntityList={}",
    // employeeBindEntityList);
    // return Result.newSuccess();
    // }

    // private ContactScopeModel getContactScopeDataWithCache(String outEa) {
    // String key = GET_CONTACT_SCOPE_CACHE_KEY + outEa;
    // String jsonValue = redisDataSource.getRedisClient().get(key);
    // ContactScopeModel contactScopeModel = null;
    //
    // if(StringUtils.isNotEmpty(jsonValue)) {
    // contactScopeModel =
    // JSONObject.parseObject(jsonValue,ContactScopeModel.class);
    // executorService.submit(()->{
    // getContactScopeData(outEa);
    // });
    // return contactScopeModel;
    // }
    // contactScopeModel = getContactScopeData(outEa);
    // return contactScopeModel;
    // }

    private List<EmployeeDto> batchGetEmployeeData(int ei, List<Integer> fsUserList) {
        List<EmployeeDto> totalList = new ArrayList<>();
        List<List<Integer>> pageList = PageUtils.getPageList(fsUserList, 100);
        for (List<Integer> page : pageList) {
            com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> result = fsEmployeeServiceProxy
                    .batchGetEmployeeDto(ei, page);
            LogUtils.info("EnterpriseBindServiceImpl.queryAccountBind,result={}", result);
            if (CollectionUtils.isNotEmpty(result.getData())) {
                totalList.addAll(result.getData());
            }
        }
        String key = FS_EMPLOYEE_DATA_CACHE_KEY + fsUserList.hashCode();
        redisDataSource.getRedisClient().set(key, JSONObject.toJSONString(totalList));
        redisDataSource.getRedisClient().expire(key, 10 * 60L);// 有效期10分钟
        return totalList;
    }

    private List<EmployeeDto> batchGetEmployeeDataWithCache(int ei, List<Integer> fsUserList) {
        String key = FS_EMPLOYEE_DATA_CACHE_KEY + fsUserList.hashCode();
        String fsEmployeeDataJson = redisDataSource.getRedisClient().get(key);
        List<EmployeeDto> totalList = null;

        if (StringUtils.isNotEmpty(fsEmployeeDataJson)) {
            totalList = JSONObject.parseArray(fsEmployeeDataJson, EmployeeDto.class);
            executorService.submit(() -> {
                batchGetEmployeeData(ei, fsUserList);
            });
            return totalList;
        }
        totalList = batchGetEmployeeData(ei, fsUserList);
        return totalList;
    }

    private Result<List<EmployeeBindModel.FsEmployee>> queryFsUnbindOld(String fsEa, String outEa) {
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        // 1.查询企业绑定关系
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                .outEa(outEa).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = entities.get(0);
        // 2.查询CRM应用是否安装
        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByTenantKey(enterpriseBindEntity.getOutEa());
        if (corpInfoEntity == null) {
            return Result.newError(ResultCodeEnum.CRM_APP_NOT_INSTALLED);
        }

        // 3.获取已绑定的员工数据
        List<EmployeeBindEntity> bindEntityList = employeeBindManager.getEntityList(fsEa, outEa);
        List<Integer> fsUserList = bindEntityList.stream().map(item -> Integer.valueOf(item.getFsUserId()))
                .collect(Collectors.toList());

        List<EmployeeDto> employeeEditionDataList = getEmployeeDtoList(fsEa, 1, null, fsUserList);
        if (CollectionUtils.isEmpty(employeeEditionDataList)) {
            return Result.newSuccess(new LinkedList<>());
        }
        Set<Integer> departmentIds = employeeEditionDataList.stream()
                .filter(v -> ObjectUtils.isNotEmpty(v.getMainDepartmentId())).map(EmployeeDto::getMainDepartmentId)
                .collect(Collectors.toSet());
        departmentIds.add(GlobalValue.ALL_COMPANY_DEPARTMENT_ID);
        departmentIds.add(GlobalValue.UNALLOCATED_DEPARTMENT_ID);
        List<DepartmentDto> getDepartmentDtoList = getDepartmentDtoList(fsEa, 1, new LinkedList<>(departmentIds));
        Map<Integer, DepartmentDto> departmentDtoMap = getDepartmentDtoList.stream().collect(Collectors
                .toMap(DepartmentDto::getDepartmentId, Function.identity(), (existing, replacement) -> existing));
        // 6.组装未绑定的员工数据
        List<EmployeeBindModel.FsEmployee> fsEmployeeList = employeeEditionDataList.parallelStream()
                .map(employeeDto -> getFsEmployee(employeeDto, departmentDtoMap)).filter(ObjectUtils::isNotEmpty)
                .collect(Collectors.toList());
        return Result.newSuccess(fsEmployeeList);
    }

    private Result<PageModel<List<EmployeeBindModel.FsEmployee>>> getPagedFsUnbind(
            List<EmployeeBindModel.FsEmployee> fsEmployeeList, Integer pageSize) {
        PageModel<List<EmployeeBindModel.FsEmployee>> pageModel = new PageModel<>();
        List<List<EmployeeBindModel.FsEmployee>> pageList = PageUtils.getPageList(fsEmployeeList, pageSize);
        pageModel.setPageList(pageList);
        pageModel.setPageSize(pageSize);
        pageModel.setTotalPage(pageList.size());
        pageModel.setTotal(fsEmployeeList.size());

        return Result.newSuccess(pageModel);
    }

    @Deprecated
    @Override
    public Result<PageModel<List<EmployeeBindModel.FsEmployee>>> queryFsUnbind(String fsEa, String outEa,
            Integer pageSize) {
        Result<List<EmployeeBindModel.FsEmployee>> result = queryFsUnbindOld(fsEa, outEa);
        if (result.isSuccess() == false) {
            return Result.newError(result.getCode(), result.getMsg());
        }
        return getPagedFsUnbind(result.getData(), pageSize);
    }

    @Deprecated
    @Override
    public Result<List<EmployeeBindModel.OutEmployee>> queryOutUnbind(String fsEa, String outEa, String searchText,
            Integer pageSize) {
        String tenantId = eieaConverter.enterpriseAccountToId(fsEa) + "";
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        // 1.查询企业绑定关系
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                .outEa(outEa).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = entities.get(0);
        // 2.查询CRM应用是否安装
        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByTenantKey(enterpriseBindEntity.getOutEa());
        if (corpInfoEntity == null) {
            return Result.newError(ResultCodeEnum.CRM_APP_NOT_INSTALLED);
        }

        // 3.获取已绑定的员工数据
        List<EmployeeBindEntity> bindEntityList = employeeBindManager.getEntityList(fsEa, outEa);
        Set<String> outUserList = bindEntityList.stream().map(EmployeeBindEntity::getOutUserId)
                .collect(Collectors.toSet());

        // 4.读取飞书CRM应用可见范围内的员工信息
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId
                : enterpriseBindEntity.getAppId();
        ContactScopeModel contactScopeModel = getContactScopeData(enterpriseBindEntity.getOutEa(), appId,
                enterpriseBindEntity.getChannel());
        Map<String, DepartmentData.Department> departmentMap = contactScopeModel.getDepartmentList().stream()
                .collect(Collectors.toMap(DepartmentData.Department::getOpenDepartmentId, Function.identity(),
                        (existing, replacement) -> existing));
        Map<String, UserData.User> userMap = contactScopeModel.getUserList().stream().collect(
                Collectors.toMap(UserData.User::getOpenId, Function.identity(), (existing, replacement) -> existing));
        // 5.组装飞书CRM应用可见范围内的员工信息
        String lang = TraceUtils.getLocale();
        LogUtils.info("EnterpriseBindServiceImpl.queryOutUnbind.lang={}", lang);
        List<EmployeeBindModel.OutEmployee> outEmployeeList = contactScopeModel.getUserList().parallelStream()
                .filter(v -> !outUserList.contains(v.getOpenId())).map(outEmployee -> getOutEmployee(userMap,
                        departmentMap, outEmployee.getOpenId(), corpInfoEntity.getTenantName(), tenantId, lang))
                .distinct().collect(Collectors.toList());
        if (StringUtils.isNotEmpty(searchText)) {
            outEmployeeList = outEmployeeList.stream()
                    .filter(v -> StringUtils.containsIgnoreCase(v.getUserName(), searchText))
                    .collect(Collectors.toList());
        }
        LogUtils.info("EmployeeBindServiceImpl.queryOutUnbind,distinct,matchedList.size={}", outEmployeeList.size());

        return Result.newSuccess(outEmployeeList);
    }

    @Deprecated
    @Override
    public Result<EmployeeUnBindModel> queryUnbind(String fsEa, String outEa, Integer pageSize) {
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        String tenantId = ei + "";
        // 1.查询企业绑定关系
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                .outEa(outEa).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = entities.get(0);
        // 2.查询CRM应用是否安装
        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByTenantKey(enterpriseBindEntity.getOutEa());
        if (corpInfoEntity == null) {
            return Result.newError(ResultCodeEnum.CRM_APP_NOT_INSTALLED);
        }

        // 3.获取已绑定的员工数据
        List<EmployeeBindEntity> bindEntityList = employeeBindManager.getEntityList(fsEa, outEa);
        List<Integer> fsUserList = bindEntityList.stream().map(item -> Integer.valueOf(item.getFsUserId()))
                .collect(Collectors.toList());
        List<String> outUserList = bindEntityList.stream().map(EmployeeBindEntity::getOutUserId)
                .collect(Collectors.toList());

        EmployeeUnBindModel unBindModel = new EmployeeUnBindModel();
        // 4.读取纷享所有部门数据
        com.facishare.open.order.contacts.proxy.api.result.Result<List<DepartmentDto>> departmentResult = fsDepartmentServiceProxy
                .getAllDepartmentDto(ei);
        // 5.读取纷享根部门下所有员工数据，排除已绑定的员工数据，也就是未绑定的员工数据
        com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> employeeResult = fsEmployeeServiceProxy
                .batchGetAllEmployeeDto(ei, fsUserList);
        if (employeeResult.isSuccess() && CollectionUtils.isEmpty(employeeResult.getData())) {
            return Result.newError(ResultCodeEnum.NO_UNBIND_FS_EMPLOYEE);
        }
        Map<Integer, DepartmentDto> departmentDtoMap = departmentResult.getData().stream().collect(Collectors
                .toMap(DepartmentDto::getDepartmentId, Function.identity(), (existing, replacement) -> existing));
        // 6.组装未绑定的员工数据
        for (EmployeeDto employeeDto : employeeResult.getData()) {
            EmployeeBindModel.FsEmployee fsEmployee = getFsEmployee(employeeDto, departmentDtoMap);
            unBindModel.getFsEmployeeList().add(fsEmployee);
        }

        // 7.读取飞书CRM应用可见范围内的员工信息
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId
                : enterpriseBindEntity.getAppId();
        ContactScopeModel contactScopeModel = getContactScopeData(enterpriseBindEntity.getOutEa(), appId,
                enterpriseBindEntity.getChannel());
        Map<String, DepartmentData.Department> departmentMap = contactScopeModel.getDepartmentList().stream()
                .collect(Collectors.toMap(DepartmentData.Department::getOpenDepartmentId, Function.identity(),
                        (existing, replacement) -> existing));
        Map<String, UserData.User> userMap = contactScopeModel.getUserList().stream().collect(
                Collectors.toMap(UserData.User::getOpenId, Function.identity(), (existing, replacement) -> existing));
        List<EmployeeBindModel.OutEmployee> outEmployeeList = new ArrayList<>();
        // 8.组装飞书CRM应用可见范围内的员工信息
        String lang = TraceUtils.getLocale();
        for (UserData.User user : contactScopeModel.getUserList()) {
            // 9.过滤掉已绑定的飞书员工
            if (outUserList.contains(user.getOpenId()))
                continue;
            EmployeeBindModel.OutEmployee outEmployee = getOutEmployee(userMap, departmentMap, user.getOpenId(),
                    corpInfoEntity.getTenantName(), tenantId, lang);
            if (outEmployee == null)
                continue;
            unBindModel.getOutEmployeeList().add(outEmployee);
        }
        return Result.newSuccess(unBindModel);
    }

    @Deprecated
    @Override
    public Result<List<EmployeeBindModel.OutEmployee>> queryOutEmployeeList(String fsEa, String outEa) {
        String tenantId = eieaConverter.enterpriseAccountToId(fsEa) + "";
        // 1.查询企业绑定关系
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                .outEa(outEa).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = entities.get(0);

        // 2.查询CRM应用是否安装
        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByTenantKey(enterpriseBindEntity.getOutEa());
        if (corpInfoEntity == null) {
            return Result.newError(ResultCodeEnum.CRM_APP_NOT_INSTALLED);
        }

        // 3.获取已绑定的人员列表
        List<EmployeeBindEntity> bindEntityList = employeeBindManager.getEntityList(fsEa, outEa);
        List<String> outUserList = bindEntityList.stream().map(EmployeeBindEntity::getOutUserId)
                .collect(Collectors.toList());

        // 4.获取飞书版CRM应用可见范围内的员工列表
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId
                : enterpriseBindEntity.getAppId();
        ContactScopeModel contactScopeModel = getContactScopeData(enterpriseBindEntity.getOutEa(), appId,
                enterpriseBindEntity.getChannel());
        Map<String, DepartmentData.Department> departmentMap = contactScopeModel.getDepartmentList().stream()
                .collect(Collectors.toMap(DepartmentData.Department::getOpenDepartmentId, Function.identity(),
                        (existing, replacement) -> existing));
        Map<String, UserData.User> userMap = contactScopeModel.getUserList().stream().collect(
                Collectors.toMap(UserData.User::getOpenId, Function.identity(), (existing, replacement) -> existing));
        List<EmployeeBindModel.OutEmployee> outEmployeeList = new ArrayList<>();
        // 5.组装飞书CRM应用可见范围内的员工信息
        String lang = TraceUtils.getLocale();
        for (UserData.User user : contactScopeModel.getUserList()) {
            // 6.过滤掉已绑定的飞书员工
            if (outUserList.contains(user.getOpenId()))
                continue;
            EmployeeBindModel.OutEmployee outEmployee = getOutEmployee(userMap, departmentMap, user.getOpenId(),
                    corpInfoEntity.getTenantName(), tenantId, lang);
            if (outEmployee == null)
                continue;
            outEmployeeList.add(outEmployee);
        }
        return Result.newSuccess(outEmployeeList);
    }

    @Deprecated
    @Override
    public Result<Boolean> unBind(String fsEa, String fsUserId, String outUserId) {
        EmployeeBindEntity entity = employeeBindManager.getBindEntity(fsEa, fsUserId, outUserId);
        if (entity == null) {
            return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
        }
        int count = employeeBindManager.deleteById(entity.getId());
        return Result.newSuccess(count == 1);
    }

    @Deprecated
    @Override
    public Result<Boolean> reBind(String fsEa, String fsUserId, String outUserId, String outEa) {
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                .outEa(outEa).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = entities.get(0);
        EmployeeBindEntity entity = employeeBindManager.getBindEntity(fsEa, fsUserId, outUserId);
        if (entity != null) {
            return Result.newError(ResultCodeEnum.EMPLOYEE_HAS_BIND);
        }
        entity = employeeBindManager.getEntityByFsUserId(fsEa, fsUserId, outEa);
        int count = 0;
        // 存在绑定关系，则更新
        if (entity != null) {
            entity.setOutUserId(outUserId);
            entity.setBindStatus(BindStatusEnum.normal);
            entity.setBindType(BindTypeEnum.manual);
            count = employeeBindManager.updateById(entity);
        } else {
            // 不存在绑定关系，则新增
            count = employeeBindManager.insert(ChannelEnum.feishu, fsEa, fsUserId, enterpriseBindEntity.getOutEa(),
                    outUserId, BindTypeEnum.manual, BindStatusEnum.normal);
        }
        return Result.newSuccess(count == 1);
    }

    @Deprecated
    @Override
    public Result<List<EmployeeBindModel2>> batchBind(String fsEa, List<EmployeeBindModel2> employeeList,
            String outEa) {
        List<EmployeeBindModel2> failedList = new ArrayList<>();
        for (EmployeeBindModel2 employee : employeeList) {
            Result<Boolean> result = reBind(fsEa, employee.getFsUserId(), employee.getOutUserId(), outEa);
            LogUtils.info("EmployeeBindServiceImpl.batchBind,result={}", result);
            if (result.isSuccess() == false) {
                failedList.add(employee);
            }
        }
        return Result.newSuccess(failedList);
    }

    @Deprecated
    @Override
    public Result<List<EmployeeBindModel2>> batchUnBind(String fsEa, List<EmployeeBindModel2> employeeList) {
        List<EmployeeBindModel2> failedList = new ArrayList<>();
        for (EmployeeBindModel2 employee : employeeList) {
            Result<Boolean> result = unBind(fsEa, employee.getFsUserId(), employee.getOutUserId());
            LogUtils.info("EmployeeBindServiceImpl.batchUnBind,result={}", result);
            if (result.isSuccess() == false) {
                failedList.add(employee);
            }
        }
        return Result.newSuccess(failedList);
    }

    @Deprecated
    @Override
    public Result<Void> updateEmployeeStatus(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String outUserId,
            BindStatusEnum bindStatus) {
        if (bindStatus == BindStatusEnum.normal) {
            return contactsService.resumeEmployee(outerOaEnterpriseBindEntity, outUserId);
        } else if (bindStatus == BindStatusEnum.stop) {
            return contactsService.stopEmployee(outerOaEnterpriseBindEntity, outUserId);
        }
        return Result.newSuccess();
    }

    @Deprecated
    @Override
    public Result<Void> addOrUpdateEmployee(FeishuEventModel2.EventModelHeader header,
            FeishuContactUserUpdatedV3Event event, OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity) {
        String outEa = header.getTenantKey();
        String outUserId = event.getObject().getOpenId();
        String appId = header.getAppId();
        // 查询企业绑定状态
        LogUtils.info("EmployeeBindServiceImpl.addOrUpdateEmployee,enterpriseBindList={}", outerOaEnterpriseBindEntity);
        // 遍历所有绑定的企业，根据appId和outEa进行匹配
            String fsEa = outerOaEnterpriseBindEntity.getFsEa();
            // 处理员工停用事件
            if (event.getObject().getStatus().is_frozen() && event.getOldObject().getStatus() != null
                    && event.getOldObject().getStatus().is_frozen() == false) {
                com.facishare.open.outer.oa.connector.common.api.result.Result<Void> voidResult = objectDataManager
                        .removeEmpData(outerOaEnterpriseBindEntity, outUserId, RemoveEmployeeEventType.RESIGN_EMPLOYEE);
                LogUtils.info(
                        "EmployeeBindServiceImpl.addOrUpdateEmployee,stop user,fsEa={},outEa={},appId={},result={}",
                        fsEa, outEa, appId, voidResult);
            } else {
                // 查询员工绑定状态，使用fsEa和outEa确定唯一绑定关系
                OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager
                        .getEntitiesByDcId(outerOaEnterpriseBindEntity.getId(), null, outUserId);
                LogUtils.info(
                        "EmployeeBindServiceImpl.addOrUpdateEmployee,fsEa={},outEa={},appId={},employeeBindEntity={}",
                        fsEa, outEa, appId, employeeBindEntity);

                if (ObjectUtils.isNotEmpty(employeeBindEntity)) {
                    // 更新名称或者启用
                    if (event.getObject().getStatus().is_frozen() == false && event.getOldObject().getStatus() != null
                            && event.getOldObject().getStatus().is_frozen()) {
                        // 员工启用事件
                        Result<Void> result = updateEmployeeStatus(outerOaEnterpriseBindEntity, outUserId,
                                BindStatusEnum.normal);
                        LogUtils.info(
                                "EmployeeBindServiceImpl.addOrUpdateEmployee,enable user,fsEa={},outEa={},appId={},result={}",
                                fsEa, outEa, appId, result);
                    } else if (event.getOldObject().getName() != null || event.getOldObject().getNickname() != null) {
                        // 更改姓名
                        int currentEi = eieaConverter.enterpriseAccountToId(fsEa);
                        FsEmpArg arg = FsEmpArg.builder().ei(currentEi + "").id(employeeBindEntity.getFsEmpId())
                                .name(event.getObject().getName())
                                .fullName(event.getObject().getName()).build();
                        // 更新纷享员工姓名
                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy
                                .update(arg, null, null);
                        LogUtils.info(
                                "EmployeeBindServiceImpl.addOrUpdateEmployee,update employee name,fsEa={},outEa={},appId={},updateResult={}",
                                fsEa, outEa, appId, updateResult);
                    } else {
                        LogUtils.info(
                                "EmployeeBindServiceImpl.addOrUpdateEmployee,not support,fsEa={},outEa={},appId={}",
                                fsEa, outEa, appId);
                    }
                } else {
                    // 新增
                    Result<Void> result = contactsService.addUserList(appId, outerOaEnterpriseBindEntity, outEa,
                            Lists.newArrayList(event.getObject()));
                    LogUtils.info(
                            "EmployeeBindServiceImpl.addOrUpdateEmployee,create user,fsEa={},outEa={},appId={},result={}",
                            fsEa, outEa, appId, result);
                }

        }
        return Result.newSuccess();
    }

    @Override
    public Result<FsEmployeeDetailInfo> getFsCurEmployeeDetailInfo(Integer ei, Integer userId) {
        com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> employeeDtoListResult = fsEmployeeServiceProxy
                .batchGetEmployeeDto(ei, Lists.newArrayList(userId));
        if (!employeeDtoListResult.isSuccess()) {
            return Result.newError(employeeDtoListResult.getCode(), employeeDtoListResult.getMsg());
        }
        com.facishare.open.order.contacts.proxy.api.result.Result<BatchGetRoleCodesByEmployeeIds.Result> employeeRoleCodesResult = fsEmployeeServiceProxy
                .batchGetEmployeeRoleCodes(ei, Lists.newArrayList(userId));
        if (!employeeRoleCodesResult.isSuccess()) {
            return Result.newError(employeeRoleCodesResult.getCode(), employeeRoleCodesResult.getMsg());
        }
        FsEmployeeDetailInfo info = new FsEmployeeDetailInfo();
        info.setEnterpriseId(ei);
        info.setEmployeeId(userId);
        info.setFullName(employeeDtoListResult.getData().get(0).getFullName());
        info.setName(employeeDtoListResult.getData().get(0).getName());
        info.setRoleCodes(employeeRoleCodesResult.getData().getEmployeeIdRolesMap().get(String.valueOf(userId)));
        return Result.newSuccess(info);
    }

    @Override
    public Result<Void> uploadEmployeesBindSyncFile(List<EmployeesBindSyncInfo> employeesBindSyncInfos) {
        TraceUtils.initTraceId(UUID.randomUUID().toString());
        if (CollectionUtils.isEmpty(employeesBindSyncInfos)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String displayId = employeesBindSyncInfos.get(0).getDisplayId();
        // 飞书编号id换取飞书企业id
        CorpInfoEntity corpInfoEntity = corpInfoManager.getEntityByDisplayId(displayId);
        if (ObjectUtils.isEmpty(corpInfoEntity)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        String outEa = corpInfoEntity.getTenantKey();
        Map<String, EmployeesBindSyncInfo> employeesBindSyncInfoMap = employeesBindSyncInfos.stream()
                .collect(Collectors.toMap(EmployeesBindSyncInfo::getMobile, Function.identity(), (v1, v2) -> v1));
        // 手机号码换员工id,最多50
        // 如果要处理的列表非常大，且每个批次的处理需要较长时间，可能需要考虑使用并发或并行处理的策略来提高效率。
        List<String> mobiles = employeesBindSyncInfos.stream().map(EmployeesBindSyncInfo::getMobile)
                .collect(Collectors.toList());
        for (int i = 0; i < mobiles.size(); i += 50) {
            int end = Math.min(mobiles.size(), i + 50);
            List<String> batch = mobiles.subList(i, end);
            Result<List<UserData.User>> batchGetUserIdsResult = feishuUserService
                    .batchGetUserIds(ConfigCenter.feishuCrmAppId, outEa, new LinkedList<>(), batch);
            if (batchGetUserIdsResult.isSuccess() && CollectionUtils.isNotEmpty(batchGetUserIdsResult.getData())) {
                List<UserData.User> batchGetUserIds = batchGetUserIdsResult.getData().stream()
                        .filter(v -> StringUtils.isNotEmpty(v.getUserId())).collect(Collectors.toList());
                LogUtils.info("EmployeeBindServiceImpl.uploadEmployeesBindSyncFile,batchGetUserIds={}",
                        batchGetUserIds);
                if (CollectionUtils.isNotEmpty(batchGetUserIds)) {
                    for (UserData.User user : batchGetUserIds) {
                        if (employeesBindSyncInfoMap.containsKey(user.getMobile())) {
                            EmployeesBindSyncInfo employeesBindSyncInfo = employeesBindSyncInfoMap
                                    .get(user.getMobile());
                            if (StringUtils.isNotEmpty(user.getUserId())) {
                                EmployeeBindEntity entity = employeeBindManager.getEntity(outEa, user.getUserId(),
                                        employeesBindSyncInfo.getFsEa());
                                if (ObjectUtils.isEmpty(entity)) {
                                    EmployeeBindEntity employeeBindEntity = EmployeeBindEntity.builder()
                                            .channel(ChannelEnum.feishu).fsEa(employeesBindSyncInfo.getFsEa())
                                            .fsUserId(employeesBindSyncInfo.getFsUserId()).outEa(outEa)
                                            .outUserId(user.getUserId()).bindStatus(BindStatusEnum.normal)
                                            .bindType(BindTypeEnum.manual).build();
                                    int insert = employeeBindManager.insert(employeeBindEntity);
                                    LogUtils.info(
                                            "EmployeeBindServiceImpl.uploadEmployeesBindSyncFile,fsUserId={},insert={}",
                                            employeesBindSyncInfo.getFsUserId(), insert);
                                }
                            }
                        }
                    }
                }
            }
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return Result.newSuccess();
    }

    private ContactScopeModel getContactScopeData(String outEa, String appId, ChannelEnum channelEnum) {
        // 这个只能查询全部，不然不知道是否进行了初始同步，怕导致一直初始化
        Gson gson = new Gson();
        // 查询人员
        List<OaConnectorOutUserInfoDoc> userInfoDocs = oaConnectorOutUserInfoManager.queryUserInfos(channelEnum,
                outEa);
        LogUtils.info("EmployeeBindServiceImpl.getContactScopeData,outEa={},userInfoDocs.size={}", outEa,
                userInfoDocs.size());
        if (CollectionUtils.isEmpty(userInfoDocs)) {
            return contactsService.saveOrUpdateContactData(appId, outEa, channelEnum).getData();
        }
        // 查询部门
        List<OaConnectorOutDepartmentInfoDoc> departmentInfoDocs = oaConnectorOutDepartmentInfoManager
                .queryDepartmentInfos(channelEnum, outEa);
        LogUtils.info("EmployeeBindServiceImpl.getContactScopeData,outEa={},departmentInfoDocs.size={}", outEa,
                departmentInfoDocs.size());
        ContactScopeModel contactScopeModel = new ContactScopeModel();
        contactScopeModel.setUserList(userInfoDocs.stream()
                .map(userInfoDoc -> gson.fromJson(userInfoDoc.getOutUserInfo(), UserData.User.class))
                .collect(Collectors.toList()));
        contactScopeModel.setDepartmentList(departmentInfoDocs.stream().map(departmentInfoDoc -> gson
                .fromJson(departmentInfoDoc.getOutDepartmentInfo(), DepartmentData.Department.class))
                .collect(Collectors.toList()));
        // contactScopeModel.setUserList(new ArrayList<>(userInfoDocs.stream()
        // .map(userInfoDoc -> gson.fromJson(userInfoDoc.getOutUserInfo(),
        // UserData.User.class))
        // .collect(Collectors.toMap(UserData.User::getOpenId, employee -> employee,
        // (employee1, employee2) -> employee1))
        // .values()));
        // contactScopeModel.setDepartmentList(new
        // ArrayList<>(departmentInfoDocs.stream()
        // .map(departmentInfoDoc ->
        // gson.fromJson(departmentInfoDoc.getOutDepartmentInfo(),
        // DepartmentData.Department.class))
        // .collect(Collectors.toMap(DepartmentData.Department::getOpenDepartmentId,
        // department -> department, (department1, department2) -> department1))
        // .values()));
        return contactScopeModel;
    }

    private List<EmployeeDto> getEmployeeDtoList(String fsEa, Integer status, List<Integer> userIds,
            List<Integer> removeUserIds) {
        Gson gson = new Gson();
        List<FsUserInfoDoc> userInfoDocs = fsUserInfoManager.queryUserInfosByIds(fsEa, status, userIds);
        LogUtils.info("EmployeeBindServiceImpl.getEmployeeDtoList,fsEa={},userInfoDocs.size={}", fsEa,
                userInfoDocs.size());
        if (CollectionUtils.isNotEmpty(userInfoDocs)) {
            if (CollectionUtils.isNotEmpty(removeUserIds)) {
                userInfoDocs = userInfoDocs.stream().filter(v -> !removeUserIds.contains(v.getFsUserId()))
                        .collect(Collectors.toList());
            }
            List<EmployeeDto> employeeEditionDataList = userInfoDocs.stream()
                    .map(userInfoDoc -> gson.fromJson(userInfoDoc.getFsUserInfo(), EmployeeDto.class))
                    .collect(Collectors.toList());
            // List<EmployeeDto> employeeEditionDataList = new
            // ArrayList<>(userInfoDocs.stream()
            // .map(userInfoDoc -> gson.fromJson(userInfoDoc.getFsUserInfo(),
            // EmployeeDto.class))
            // .collect(Collectors.toMap(EmployeeDto::getEmployeeId, employee -> employee,
            // (employee1, employee2) -> employee1))
            // .values());
            return employeeEditionDataList;
        }
        Result<List<EmployeeDto>> result = fsEmployeeInfoCache(fsEa, null);
        if (result.isSuccess()) {
            List<EmployeeDto> employeeDtoList = result.getData();
            if (CollectionUtils.isNotEmpty(userIds)) {
                employeeDtoList = employeeDtoList.stream()
                        .filter(v -> (userIds.contains(v.getEmployeeId())
                                && (ObjectUtils.isEmpty(status) || status.equals(v.getStatus().getValue()))))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(removeUserIds)) {
                employeeDtoList = employeeDtoList.stream()
                        .filter(v -> (!removeUserIds.contains(v.getEmployeeId())
                                && (ObjectUtils.isEmpty(status) || status.equals(v.getStatus().getValue()))))
                        .collect(Collectors.toList());
            }
            return employeeDtoList;
        }
        return new LinkedList<>();
    }

    private List<DepartmentDto> getDepartmentDtoList(String fsEa, Integer status, List<Integer> departmentIds) {
        Gson gson = new Gson();
        List<FsDepartmentInfoDoc> departmentInfoDocs = fsDepartmentInfoManager.queryDepartmentInfosByIds(fsEa, status,
                departmentIds);
        LogUtils.info("EmployeeBindServiceImpl.getEmployeeDtoList,fsEa={},departmentInfoDocs.size={}", fsEa,
                departmentInfoDocs.size());
        if (CollectionUtils.isNotEmpty(departmentInfoDocs)) {
            List<DepartmentDto> employeeEditionDataList = departmentInfoDocs.stream().map(
                    departmentInfoDoc -> gson.fromJson(departmentInfoDoc.getFsDepartmentInfo(), DepartmentDto.class))
                    .collect(Collectors.toList());
            // List<DepartmentDto> employeeEditionDataList = new
            // ArrayList<>(departmentInfoDocs.stream()
            // .map(departmentInfoDoc ->
            // gson.fromJson(departmentInfoDoc.getFsDepartmentInfo(), DepartmentDto.class))
            // .collect(Collectors.toMap(DepartmentDto::getDepartmentId, department ->
            // department, (department1, department2) -> department1))
            // .values());
            return employeeEditionDataList;
        }
        Result<List<DepartmentDto>> result = departmentBindService.fsDepartmentInfoCache(fsEa, null);
        if (result.isSuccess()) {
            List<DepartmentDto> departmentDtoList = result.getData();
            if (CollectionUtils.isNotEmpty(departmentIds)) {
                departmentDtoList = departmentDtoList.stream()
                        .filter(v -> (departmentIds.contains(v.getDepartmentId())
                                && (ObjectUtils.isEmpty(status) || status.equals(v.getStatus().getValue()))))
                        .collect(Collectors.toList());
            }
            return departmentDtoList;
        }
        return new LinkedList<>();
    }

    @Override
    public Result<List<EmployeeDto>> fsEmployeeInfoCache(String fsEa, EmployeeDto employeeDto) {
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        List<EmployeeDto> allEmployees = null;
        // 判断是否初次同步
        Long count = fsUserInfoManager.countDocuments(fsEa);
        LogUtils.info("EmployeeBindServiceImpl.FsEmployeeInfoCache,count={}", count);
        if (ObjectUtils.isEmpty(employeeDto) || (count == null || count == 0)) {
            RLock rLock = redissonClient.getLock(FS_EMPLOYEE_DATA_CACHE_KEY + fsEa);
            // 分布式锁
            try {
                boolean isGetLock = rLock.tryLock(60 * 5, 60 * 5, TimeUnit.SECONDS);
                LogUtils.info("EmployeeBindServiceImpl.FsEmployeeInfoCache,Asynchronous threading,isGetLock={}",
                        isGetLock);
                if (isGetLock) {
                    try {
                        // 同步
                        com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> allEmployeesResult = fsEmployeeServiceProxy
                                .getAllEmployees(ei);
                        if (!allEmployeesResult.isSuccess() || CollectionUtils.isEmpty(allEmployeesResult.getData())) {
                            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
                        }
                        allEmployees = allEmployeesResult.getData();
                        // 插入
                        List<FsUserInfoDoc> docs = new LinkedList<>();
                        for (EmployeeDto dto : allEmployees) {
                            FsUserInfoDoc doc = new FsUserInfoDoc();
                            doc.setFsEa(fsEa);
                            doc.setFsUserId(dto.getEmployeeId());
                            doc.setStatus(dto.getStatus().getValue());
                            doc.setFsUserInfo(new Gson().toJson(dto));
                            doc.setCreateTime(System.currentTimeMillis());
                            doc.setUpdateTime(System.currentTimeMillis());
                            docs.add(doc);
                        }
                        BulkWriteResult bulkWriteResult = fsUserInfoManager.batchReplace(docs);
                        LogUtils.info("EmployeeBindServiceImpl.FsEmployeeInfoCache,batchReplace,bulkWriteResult={}",
                                bulkWriteResult);
                    } finally {
                        rLock.unlock();
                    }
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return Result.newSuccess(allEmployees);
        }
        // 更新数据
        allEmployees = new LinkedList<>();
        if (!employeeDto.getStatus().equals(EmployeeEntityStatus.DELETE) && (StringUtils.isEmpty(employeeDto.getName())
                || StringUtils.isEmpty(employeeDto.getMobile()) || ObjectUtils.isEmpty(employeeDto.getStatus())
                || CollectionUtils.isEmpty(employeeDto.getMainDepartmentIds()))) {
            com.facishare.open.order.contacts.proxy.api.result.Result<List<EmployeeDto>> listResult = fsEmployeeServiceProxy
                    .batchGetEmployeeDto(ei, Lists.newArrayList(employeeDto.getEmployeeId()));
            employeeDto = listResult.getData().get(0);
        }
        FsUserInfoDoc doc = new FsUserInfoDoc();
        doc.setFsEa(fsEa);
        doc.setFsUserId(employeeDto.getEmployeeId());
        doc.setStatus(employeeDto.getStatus().getValue());
        doc.setFsUserInfo(new Gson().toJson(employeeDto));
        doc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult = fsUserInfoManager.batchReplace(Lists.newArrayList(doc));
        LogUtils.info("EmployeeBindServiceImpl.FsEmployeeInfoCache,replace,bulkWriteResult={}", bulkWriteResult);
        allEmployees.add(employeeDto);

        EmployeeDto finalEmployeeDto = employeeDto;
        ThreadPoolHelper.autoBindThreadPool.submit(() -> {
            // 是否需要自动绑定
            OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                    .channel(ChannelEnum.feishu).bindStatus(BindStatusEnum.normal).build();
            List<OuterOaEnterpriseBindEntity> entityList = outerOaEnterpriseBindManager
                    .getEntities(outerOaEnterpriseBindParams);
            if (CollectionUtils.isNotEmpty(entityList)) {
                for (OuterOaEnterpriseBindEntity entity : entityList) {
                    if (ObjectUtils.isNotEmpty(entity.getConnectInfo())) {
                        FeishuEnterpriseConnectParams feishuEnterpriseConnectParams = ChannelEnum.feishu
                                .getConnectParam(entity.getConnectInfo());
                        if (feishuEnterpriseConnectParams.getIsAutoBind() == 2) {
                            String autoField = feishuEnterpriseConnectParams.getAutoField();
                            autoBindEmpByCrmEmployeeNumber(fsEa, entity.getOutEa(),
                                    Lists.newArrayList(finalEmployeeDto), autoField);
                        }

                    }
                }
            }
        });
        return Result.newSuccess(allEmployees);
    }

    @Override
    public Result<Void> autoBindEmpByCrmEmployeeNumber(String fsEa, String outEa, List<EmployeeDto> employeeDtoList,
            String autoField) {
        if (CollectionUtils.isEmpty(employeeDtoList)) {
            List<EmployeeBindEntity> bindEntityList = employeeBindManager.getEntityList(fsEa, outEa);

            List<Integer> fsUserList = bindEntityList.stream().map(item -> Integer.valueOf(item.getFsUserId()))
                    .collect(Collectors.toList());

            employeeDtoList = getEmployeeDtoList(fsEa, 1, null, fsUserList);
            if (CollectionUtils.isEmpty(employeeDtoList)) {
                LogUtils.info(
                        "EmployeeBindServiceImpl.autoBindEmpByCrmEmployeeNumber,fsEa={}, employee is not auto bind",
                        fsEa);
                return Result.newSuccess();
            }
        }

        // 查询人员
        // EnterpriseBindEntity enterpriseBindEntity =
        // enterpriseBindManager.getEntity(fsEa);
        // String outEa = enterpriseBindEntity.getOutEa();
        List<OaConnectorOutUserInfoDoc> userInfoDocs = oaConnectorOutUserInfoManager.queryUserInfos(ChannelEnum.feishu,
                outEa);
        LogUtils.info("EmployeeBindServiceImpl.autoBindEmpByCrmEmployeeNumber,outEa={},userInfoDocs.size={}", outEa,
                userInfoDocs.size());

        Gson gson = new Gson();
        List<UserData.User> users = userInfoDocs.stream()
                .map(userInfoDoc -> gson.fromJson(userInfoDoc.getOutUserInfo(), UserData.User.class))
                .collect(Collectors.toCollection(LinkedList::new));

        for (EmployeeDto employeeDto : employeeDtoList) {
            LogUtils.info("EmployeeBindServiceImpl.autoBindEmpByCrmEmployeeNumber,fsEa={},userId={}", fsEa,
                    employeeDto.getEmployeeId());
            EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(fsEa, outEa,
                    String.valueOf(employeeDto.getEmployeeId()));
            if (ObjectUtils.isNotEmpty(employeeBindEntity)) {
                continue;
            }

            for (UserData.User user : users) {
                // 筛选两边的字段
                if (filterByCrmAutoField(user, employeeDto, autoField)) {
                    EmployeeBindEntity entity = EmployeeBindEntity.builder().channel(ChannelEnum.feishu).fsEa(fsEa)
                            .fsUserId(String.valueOf(employeeDto.getEmployeeId())).outEa(outEa)
                            .outUserId(user.getOpenId()).outLeaderUserId(user.getLeader_user_id())
                            .bindStatus(BindStatusEnum.normal).bindType(BindTypeEnum.manual).build();
                    EmployeeBindEntity employeeBindManagerEntity = employeeBindManager.getEntity(outEa,
                            user.getOpenId(), fsEa);
                    if (ObjectUtils.isNotEmpty(employeeBindManagerEntity)) {
                        LogUtils.info(
                                "EmployeeBindServiceImpl.autoBindEmpByCrmEmployeeNumber,select employee mapping,fsEa={},outEa={},user",
                                fsEa, outEa, user);
                        break;
                    }
                    int insert = employeeBindManager.insert(entity);
                    LogUtils.info(
                            "EmployeeBindServiceImpl.autoBindEmpByCrmEmployeeNumber,fsEa={},fsUserId={},insert={}",
                            fsEa, employeeDto.getEmployeeId(), insert);
                    break;
                }
            }
        }
        return Result.newSuccess();
    }

    // 匹配两边字段
    private boolean filterByCrmAutoField(UserData.User user, EmployeeDto employeeDto, String autoField) {
        String fsEmpAutoValue = employeeDto.getEmpNum();
        String feishuAutoValue = user.getEmployee_no();
        if (Constant.AUTO_BIND_FIELD_EMAIL.equals(autoField)) {
            fsEmpAutoValue = employeeDto.getEmail();
            feishuAutoValue = user.getEmail();
        }
        if (StringUtils.isAnyBlank(fsEmpAutoValue, feishuAutoValue)) {
            return false;
        }
        if (fsEmpAutoValue.equals(feishuAutoValue)) {
            return true;
        }
        return false;
    }

    @Override
    public Result<Void> autoBindBySettingUnique(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity, String appId,
            ContactScopeModel contactScopeModel) {
        OuterOaConfigInfoEntity entityByAutoFieldDeFault = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());
        if(ObjectUtils.isEmpty(entityByAutoFieldDeFault)||StringUtils.isBlank(entityByAutoFieldDeFault.getConfigInfo())){
            return Result.newSuccess();
        }
        SettingAccountRulesModel settingAccountRulesModel=JSONObject.parseObject(entityByAutoFieldDeFault.getConfigInfo(), SettingAccountRulesModel.class);
        if(settingAccountRulesModel.getBindTypeEnum()==BindTypeEnum.manual||settingAccountRulesModel.getSyncTypeEnum()==EnterpriseConfigAccountSyncTypeEnum.accountSync){
            LogUtils.info("enterprise autoBindBySettingUnique  not support bind：{}", settingAccountRulesModel);
            return Result.newSuccess();
        }
        String fsEa = outerOaEnterpriseBindEntity.getFsEa();
        String outEa = outerOaEnterpriseBindEntity.getOutEa();
        int tenantId = eieaConverter.enterpriseAccountToId(fsEa);

        // 匹配的时候，统一转成大写字母
        LogUtils.info("EmployeeBindServiceImpl.autoBindEmpByFeishuEmployeeNumber,fsEa={},user.size={}", fsEa,
                contactScopeModel.getUserList().size());
        // 去掉已绑定的员工
        // List<EmployeeBindEntity> bindEntityList =
        // employeeBindManager.getEntityList(fsEa, outEa);
        OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder().fsEa(fsEa)
                .outEa(outEa).appId(appId).build();
        List<OuterOaEmployeeBindEntity> bindEntityList = outerOaEmployeeBindManager
                .getEntities(outerOaEmployeeBindParams);
        List<String> outUserList = bindEntityList.stream().map(OuterOaEmployeeBindEntity::getOutEmpId)
                .collect(Collectors.toList());

        // 过滤掉已绑定的飞书员工
        List<UserData.User> userList = contactScopeModel.getUserList().stream()
                .filter(v -> (!outUserList.contains(v.getOpenId())))
                .peek(v -> v.setEmployee_no(v.getEmployee_no().toUpperCase())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userList)) {
            return Result.newSuccess();
        }
        // 自动绑定只绑定状态正常的
        OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, outerOaEnterpriseBindEntity.getId());
        SystemFieldMappingResult systemFieldMappingResult = JSONObject.parseObject(entityByDataCenterId.getConfigInfo(),
                SystemFieldMappingResult.class);
        // 替换之前的灰度配置自动绑定
        if (ObjectUtils.isEmpty(entityByDataCenterId)) {
            return Result.newSuccess();
        }
        SystemFieldMappingResult.ItemFieldMapping itemFieldMapping = systemFieldMappingResult.getItemFieldMappings().stream().filter(item -> item.getMatchUnique()).findFirst().orElse(null);
        if(ObjectUtils.isEmpty(itemFieldMapping)){
            return Result.newSuccess();
        }
        String crmFieldApiName = itemFieldMapping.getCrmFieldApiName();
        String outerOAField = itemFieldMapping.getOuterOAFieldApiName();
        // TODO 之前employeenumber需要uppsercase
        List<String> crmQueryField = new ArrayList<>();
        Map<String, String> outerValueToIds = Maps.newHashMap();
        userList.forEach(item -> {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(item), JSONObject.class);
            String employeeValue = jsonObject.getString(outerOAField);
            if(StringUtils.isNotEmpty(employeeValue)){
                crmQueryField.add(employeeValue);
                outerValueToIds.put(employeeValue, item.getOpenId());
            }
        });
        // 需要分割成10个一次，避免底层查询数据量过大
        List<List<String>> crmQueryFieldList = Lists.partition(crmQueryField, 10);
        Map<String, String> crmQueryValueToIds = Maps.newHashMap();
        for (List<String> itemList : crmQueryFieldList) {
            crmQueryValueToIds.putAll(batchQueryCrmData(tenantId, "PersonnelObj", itemList, crmFieldApiName));
        }
        List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = Lists.newArrayList();
        for (String uniqueValue : outerValueToIds.keySet()) {
            String crmUserId=crmQueryValueToIds.get(uniqueValue);
            // 根据OA的数据，查询出对应的CRM数据
            if (ObjectUtils.isNotEmpty(crmUserId)) {
                //需要判断CRM的这个数据是否已经绑定了飞书员工
                OuterOaEmployeeBindEntity crmBindEmployeeBindEntity = outerOaEmployeeBindManager.getEntitiesByDcId(outerOaEnterpriseBindEntity.getId(), crmQueryValueToIds.get(uniqueValue), null);
                if(crmBindEmployeeBindEntity!=null){
                    LogUtils.info("auto unique crmData bind :{}",crmBindEmployeeBindEntity);
                    continue;
                }
                OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = OuterOaEmployeeBindEntity.builder()
                        .fsEa(outerOaEnterpriseBindEntity.getFsEa()).outEa(outerOaEnterpriseBindEntity.getOutEa())
                        .channel(outerOaEnterpriseBindEntity.getChannel()).appId(outerOaEnterpriseBindEntity.getAppId())
                        .dcId(outerOaEnterpriseBindEntity.getId()).bindStatus(BindStatusEnum.normal)
                        .fsEmpId(crmUserId).outEmpId(outerValueToIds.get(uniqueValue))
                        .createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis())
                        .id(IdGenerator.get()).build();
                outerOaEmployeeBindEntities.add(outerOaEmployeeBindEntity);
            }
        }
        if(CollectionUtils.isNotEmpty(outerOaEmployeeBindEntities)){
            outerOaEmployeeBindManager.batchBindEmployeeList(outerOaEmployeeBindEntities);
        }
        return Result.newSuccess();
    }

    private Map<String, String> batchQueryCrmData(Integer tenantId, String objectApiName, List<String> dataValues,
            String objectField) {
        Map<String, String> queryValueToIds = Maps.newHashMap();
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objectApiName);
        findV3Arg.setIncludeInvalid(true);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        searchQueryInfo.addFilter(objectField, dataValues, FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(JSONObject.toJSONString(searchQueryInfo));
        if(CollectionUtils.isEmpty(dataValues)){
            return new HashMap<>();
        }

        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataQueryListResult> listResult = objectDataServiceV3
                .queryList(headerObj, findV3Arg);
        if (!listResult.isSuccess()) {
            LogUtils.warn("batch query data error,{}", listResult.getMessage());
        }
        LogUtils.info("query crmdata user data:{}",listResult);
        List<ObjectData> dataList = listResult.getData().getQueryResult().getDataList();
        dataList.forEach(objectData -> {
            queryValueToIds.put(objectData.getString(objectField), objectData.get(CRMEmployeeFiledEnum.USER_ID.getCode()).toString());
        });
        return queryValueToIds;
    }

    @Override
    public Result<Integer> insertEmployeeBind(String channel, InsertEmployeeBindArg arg) {
        String fsEa = arg.getFsEa();
        String outEa = arg.getOutEa();
        String outUserId = arg.getOutUserId();
        String fsUserId = arg.getFsUserId();

        // 根据channel去获取不同连接器的授权信息
        if (channel.equals(ChannelEnum.feishu.name())) {
            OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                    .outEa(outEa).build();
            List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager
                    .getEntities(outerOaEnterpriseBindParams);
            if (CollectionUtils.isEmpty(entities)) {
                return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
            }

            // 查看是否已绑定
            EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa, outUserId, fsEa);
            if (ObjectUtils.isNotEmpty(employeeBindEntity)) {
                return Result.newError(ResultCodeEnum.EMPLOYEE_HAS_BIND);
            }

            EmployeeBindEntity entity = EmployeeBindEntity.builder().channel(ChannelEnum.feishu).fsEa(fsEa).outEa(outEa)
                    .fsUserId(fsUserId).outUserId(outUserId).bindStatus(BindStatusEnum.normal)
                    .bindType(BindTypeEnum.manual).build();
            int insert = employeeBindManager.insert(entity);
            return Result.newSuccess(insert);
        }
        return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
    }

    @Override
    public Result<Integer> updateEmployeeBind(String channel, UpdateEmployeeBindArg arg) {
        String fsEa = arg.getFsEa();
        String outEa = arg.getOutEa();
        String outUserId = arg.getOutUserId();
        String fsUserId = arg.getFsUserId();

        // 根据channel去获取不同连接器的授权信息
        if (channel.equals(ChannelEnum.feishu.name())) {
            OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                    .outEa(outEa).build();
            List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager
                    .getEntities(outerOaEnterpriseBindParams);
            if (CollectionUtils.isEmpty(entities)) {
                return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
            }
            OuterOaEnterpriseBindEntity enterpriseBindEntity = entities.get(0);
            // 查看是否已绑定
            EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa, outUserId, fsEa);
            if (ObjectUtils.isEmpty(employeeBindEntity)) {
                return Result.newError(ResultCodeEnum.EMPLOYEE_NO_BIND_INFO);
            }

            employeeBindEntity.setFsUserId(fsUserId);
            int update = employeeBindManager.updateById(employeeBindEntity);
            return Result.newSuccess(update);
        }
        return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
    }

    @Override
    public Result<EmployeeBindInfo> queryEmployeeBind(String channel, QueryEmployeeBindArg arg) {
        String fsEa = arg.getFsEa();
        String outEa = arg.getOutEa();
        String outUserId = arg.getOutUserId();
        String fsUserId = arg.getFsUserId();
        ChannelEnum channelEnum = ChannelEnum.valueOf(channel);
        // 根据channel去获取不同连接器的授权信息
        OuterOaEnterpriseBindEntity enterpriseBindEntity = new OuterOaEnterpriseBindEntity();
        enterpriseBindEntity.setChannel(channelEnum);
        enterpriseBindEntity.setFsEa(fsEa);
        enterpriseBindEntity.setOutEa(outEa);
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                .outEa(outEa).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        // 查看是否已绑定
        OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder().fsEa(fsEa)
                .outEa(outEa).fsEmpId(fsUserId).outEmpId(outUserId).channel(channelEnum).build();
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager
                .getEntities(outerOaEmployeeBindParams);
        if (ObjectUtils.isEmpty(employeeBindEntities)) {
            return Result.newSuccess();
        }
        OuterOaEmployeeBindEntity employeeBindEntity = employeeBindEntities.get(0);
        EmployeeBindInfo employeeBindInfo = EmployeeBindInfo.builder().fsEa(employeeBindEntity.getFsEa())
                .outEa(employeeBindEntity.getOutEa()).fsUserId(employeeBindEntity.getFsEmpId())
                .outUserId(employeeBindEntity.getOutEmpId()).build();
        return Result.newSuccess(employeeBindInfo);
    }

    @Override
    public Result<EmployeeBindEntity> queryEmpData(ChannelEnum channel, String fsEa, String fsUserId, String outEa,
            String outUserId) {
        return new Result<>(employeeBindManager.getEntity(channel, fsEa, fsUserId, outEa, outUserId));
    }

    @Override
    public Result<Integer> insertEmpData(EmployeeBindEntity entity) {
        return new Result<>(employeeBindManager.insert(entity));
    }

    @Override
    public Result<Void> createEmployeeInfo(String outEa, String appId, String outUserId, String fsEa) {
        // 检查是否绑定了
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa)
                .outEa(outEa).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager
                .getEntities(outerOaEnterpriseBindParams);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }

        EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa, outUserId, fsEa);
        if (ObjectUtils.isNotEmpty(employeeBindEntity)) {
            return Result.newSuccess();
        }

        // 创建员工
        Result<UserData.User> userInfo = feishuUserService.getUserInfo(appId, outEa, outUserId);
        if (!userInfo.isSuccess()) {
            return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(userInfo.getCode()),
                    ResultCodeEnum.CRM_USER_ACCOUNT_CREATE_ERROR.getCode()), userInfo.getMsg());
        }
        return contactsService.addUser(appId, outEa, userInfo.getData());
    }

    /**
     * 这个接口是获取外部人员的所有绑定记录
     * 
     * @param channel
     * @param outEa
     * @param outUserId
     * @return
     */
    @Override
    public Result<List<EmployeeBindInfo>> queryEmployeeBindListByOutData(ChannelEnum channel, String outEa,
            String outUserId) {

        OuterOaEmployeeBindParams outerOaEmployeeBindParams = OuterOaEmployeeBindParams.builder().channel(channel)
                .outEa(outEa).outEmpId(outUserId).build();
        List<OuterOaEmployeeBindEntity> entityListByOutData = outerOaEmployeeBindManager
                .getEntities(outerOaEmployeeBindParams);

        List<EmployeeBindInfo> employeeBindInfos = entityListByOutData.stream().map(entity -> {
            EmployeeBindInfo info = new EmployeeBindInfo();
            info.setFsEa(entity.getFsEa());
            info.setFsUserId(entity.getFsEmpId());
            info.setOutEa(entity.getOutEa());
            info.setOutUserId(entity.getOutEmpId());
            return info;
        }).collect(Collectors.toList());
        return new Result<>(employeeBindInfos);
    }

    @Override
    public Result<Map<String, String>> filterEmployeeAutoBind(Integer tenantId, String objectApiName, List<String> dataValues, String objectField) {
        Map<String, String> crmUserIdMap = batchQueryCrmData(tenantId, objectApiName, dataValues, objectField);
       return Result.newSuccess(crmUserIdMap);
    }

    private List<EmployeeDto> filterAutoField(List<EmployeeDto> employeeDtoList, String autoField,
            Map<String, EmployeeDto> employeeEditionDataMap) {
        // 应该用对象接口，这样字段getField比较方便
        List<EmployeeDto> employeeDtos = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(employeeDtoList)) {
            for (EmployeeDto employeeDto : employeeDtoList) {
                String employeeKey = null;
                if (Constant.AUTO_BIND_FIELD_EMAIL.equals(autoField) && employeeDto.getEmail() != null) {
                    employeeDtos.add(employeeDto);
                    employeeKey = employeeDto.getEmail();
                } else {
                    if (employeeDto.getEmpNum() != null) {
                        employeeDto.setEmpNum(employeeDto.getEmpNum().toUpperCase());
                        employeeDtos.add(employeeDto);
                        employeeKey = employeeDto.getEmpNum();

                    }
                }
                if (StringUtils.isNotEmpty(employeeKey)) {
                    employeeEditionDataMap.put(employeeKey, employeeDto);
                }
            }

        }
        return employeeDtos;
    }

    public static void main(String[] args) {
        List<Integer> list = Lists.newArrayList(1, 2, 3);
        System.out.println(list.hashCode());

    }
}
