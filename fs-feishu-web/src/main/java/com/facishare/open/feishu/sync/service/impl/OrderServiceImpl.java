package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.sync.model.FeishuOrderOtherFields;
import com.facishare.open.feishu.syncapi.arg.CreateCustomerAndUpdateMappingArg;
import com.facishare.open.feishu.syncapi.arg.CreateOrderArg;
import com.facishare.open.feishu.syncapi.arg.SendTextNoticeArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.enums.OrderFromEnum;
import com.facishare.open.feishu.syncapi.enums.PricePlanTypeEnum;
import com.facishare.open.feishu.syncapi.model.CrmOrderProductVo;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderMultipleProductArg;
import com.facishare.open.order.contacts.proxy.api.enums.EnterpriseEvnEnum;
import com.facishare.open.order.contacts.proxy.api.enums.EnterpriseInitLanguageEnum;
import com.facishare.open.order.contacts.proxy.api.enums.EnterpriseLanguageEnum;
import com.facishare.open.outer.oa.connector.common.api.admin.FeiShuConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.AppInfoEntity;
import com.facishare.open.feishu.syncapi.enums.*;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseExtendModel;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel;
import com.facishare.open.feishu.syncapi.model.config.VersionModel;
import com.facishare.open.feishu.syncapi.model.connect.FeishuEnterpriseConnectParams;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.model.info.EnterpriseTrialInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.*;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.enums.CrmOrderTypeEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.EnterpriseUtils;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;

// IgnoreI18nFile
@Service("orderService")
public class OrderServiceImpl implements OrderService {
    @Resource
    private FsOrderServiceProxy fsOrderServiceProxy;
    @Resource
    private OrderInfoManager orderInfoManager;
    @Autowired
    private OuterOaOrderInfoManager outerOaOrderInfoManager;
    @Resource
    private CorpInfoManager corpInfoManager;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private AppInfoManager appInfoManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private ContactsService contactsService;
    @Resource
    private CorpService corpService;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private FeishuMessageService feishuMessageService;
    @Resource
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;
    @Resource
    private NotificationService notificationService;
    @Autowired
    private FeishuTenantService feishuTenantService;

    private ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(10);
    private static Map<String,Integer> retryMap = new ConcurrentHashMap<>();
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Override
    public Result<List<OuterOaEnterpriseBindEntity>> getEnterpriseBindList(String outEa) {
    
        OuterOaEnterpriseBindParams params = OuterOaEnterpriseBindParams.builder()
                .outEa(outEa)
                .bindStatus(BindStatusEnum.normal)
                .build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager.getEntities(params);
        LogUtils.info("OrderServiceImpl.getEnterpriseBindList,enterpriseBindList={}", enterpriseBindList);
        return Result.newSuccess(enterpriseBindList);
    }

    @Override
    public Result<OuterOaOrderInfoEntity> saveOrder(FeishuOrderPaidEvent event) {
        Long orderStartTime = event.getPayTime();
        Long orderEndTime = event.getPayTime();
        FeishuCrmEditionEnum crmEditionEnum = null;
        Integer orderDays = null;
        ChannelEnum channelEnum=ChannelEnum.valueOf(ConfigCenter.CURRENT_CHANNEL_ISV_CHANNEL);
        //先确认下订单类型是否是定向方案
        VersionModel versionModel = ConfigCenter.getFirstVersionProductId(event.getAppId(),event.getPricePlanId());
        if(ObjectUtils.isNotEmpty(versionModel)) {
            crmEditionEnum = FeishuCrmEditionEnum.valueOf(versionModel.getVersion());
            if(ObjectUtils.isEmpty(crmEditionEnum)) {
                LogUtils.info("OrderServiceImpl.saveOrder,price plan id not support={}",event.getPricePlanId());
                return Result.newError(-1,FAIL);
            }
        } else {
            crmEditionEnum = FeishuCrmEditionEnum.FEISHU_SPECIFIC;
        }

        //试用版seats的值为0，我们默认把它改成10000，对客户来说，人数不会超过这个数，也就是人数不受限制
        //定向版本直接取seats的值，可能为0
        Integer userCount = 0;

        //正常订单属于客户下单
        //定向方案属于服务商下单
        OrderFromEnum orderFrom = null;

        if(crmEditionEnum != FeishuCrmEditionEnum.FEISHU_SPECIFIC) {
            switch (event.getPricePlanType()) {
                case trial:
                    //试用期15天
                    orderDays = GlobalValue.ALLOW_TRIAL_DAYS;
                    orderEndTime += orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    break;
                case per_seat_per_year:
                    //一年按365天计算
                    orderDays = GlobalValue.DAYS_OF_ONE_YEAR;
                    if(event.getBuyType()==BuyTypeEnum.renew) {
                        OuterOaOrderInfoEntity latestOrder = outerOaOrderInfoManager.getLatestOrder(event.getTenantKey());
                        orderStartTime = latestOrder.getEndTime();
                        orderEndTime = orderStartTime + orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    } else if(event.getBuyType()==BuyTypeEnum.upgrade) {
                        //升级包括增购升级和版本升级，升级不改变原始订单的开始日期和结束日期
                        OuterOaOrderInfoEntity srcOrder = outerOaOrderInfoManager.getEntityByOrderId(event.getSrcOrderId());
                        orderStartTime = srcOrder.getBeginTime();
                        orderEndTime = srcOrder.getEndTime();
                    } else {
                        orderEndTime += orderDays * GlobalValue.TOTAL_MS_IN_ONE_DAY;
                    }
                    break;
                case active_end_date:
                    //定向方案
                    //由于定向方案没有返回过期时间或者有效时间，所以订单开始时间和结束时间都为支付时间，订单日期为0
                    orderDays = 0;
                    break;
                default:
                    LogUtils.info("OrderServiceImpl.onOrderPaid,order type not support={}",event.getPricePlanType());
                    return Result.newError(-1,FAIL);
            }
            userCount = event.getSeats()==0 ? ConfigCenter.createCrmAccount: event.getSeats();
            orderFrom = OrderFromEnum.customer;
        } else {
            //定向方案统一不设置过期时间，原因是飞书不返回此信息
            orderDays = 0;
            userCount = event.getSeats();
            orderFrom = OrderFromEnum.isv;
        }

        //飞书订单表，用tenantKey当corpId
        String corpId = event.getTenantKey();
        FeishuOrderOtherFields feishuOrderOtherFields= FeishuOrderOtherFields.builder().orderFrom(orderFrom)
                .editionId(event.getPricePlanId())
                .userCount(userCount)
                .srcOrderId(event.getSrcOrderId())
                .pricePlanType(event.getPricePlanType())
                .orderFrom(orderFrom)
                .payPrice(event.getOrderPayPrice())
                .price(event.getOrderPayPrice())
                .build();
        OuterOaOrderInfoEntity outerOaOrderInfoEntity= OuterOaOrderInfoEntity.builder()
                .appId(event.getAppId())
                .orderId(event.getOrderId())
                .channel(channelEnum)
                .orderType(FeishuOrderPaidEvent.convertToOuterOaOrderInfoTypeEnum(event.getBuyType()))
                .paidOutEa(corpId)
                .beginTime(orderStartTime)
                .endTime(orderEndTime)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .orderInfo(JSONObject.toJSONString(feishuOrderOtherFields))
                .build();


        LogUtils.info("OrderServiceImpl.saveOrder,entity={}", JSONObject.toJSONString(outerOaOrderInfoEntity));
        int count = outerOaOrderInfoManager.insertOrUpdateOrderInfo(outerOaOrderInfoEntity);
        LogUtils.info("OrderServiceImpl.saveOrder,count={}",count);
        if(crmEditionEnum == FeishuCrmEditionEnum.FEISHU_SPECIFIC) {
            LogUtils.info("OrderServiceImpl.saveOrder,crm edition is FEISHU_SPECIFIC");
            return Result.newError(-1,SUCCESS);
        }
        String traceId = TraceUtils.getTraceId();
        if(StringUtils.isEmpty(traceId)) {
            traceId = UUID.randomUUID().toString();
            TraceUtils.initTraceId(traceId);
        }
        final String traceId2 = traceId;
        new Thread(() -> {
            TraceUtils.initTraceId(traceId2);
            enterpriseOpenMonitor(corpId, event.getAppId());
        }).start();
        return new Result<>(outerOaOrderInfoEntity);
    }

    @Override
    public Result<Void> createOrder(CreateOrderArg arg) {
        LogUtils.info("OrderServiceImpl.createOrder,arg={}", arg);
        OuterOaOrderInfoEntity entity = outerOaOrderInfoManager.getEntityByOrderId(arg.getOrderId());
        // 修改为使用 OuterOaOrderInfoMapper 查询
        LogUtils.info("OrderServiceImpl.createOrder,entity={}", entity);
        CreateCrmOrderMultipleProductArg createCrmOrderArg = buildCreateCrmOrderArg(entity, arg.getFsEa());
        LogUtils.info("OrderServiceImpl.createOrder,createCrmOrderArg={}", createCrmOrderArg);
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrderMultipleProduct(createCrmOrderArg);
        LogUtils.info("OrderServiceImpl.createOrder,result={}", result);
        return Result.newError(result.getCode(),result.getMsg());
    }


    @Override
    public Result<QueryTenantInfoData> getFeishuEnterpriseInfo(String appId, String outEa) {
        Result<QueryTenantInfoData> result = feishuTenantService.queryTenantInfo(appId, outEa);
        return result;
    }

    @Override
    public Result<UserData.User> getAppInstallerInfo(String appId, String outEa) {
        return employeeBindService.getAppInstallerInfo(appId, outEa);
    }

    @Override
    public Result<String> genFsEa(String enterpriseName) {
        String fsEa = EnterpriseUtils.genEA(enterpriseName,"feishu");
        LogUtils.info("OrderServiceImpl.genFsEa,fsEa={}", fsEa);
        return new Result<>(fsEa);
    }

    @Override
    public Result<Void> createCustomerAndUpdateMapping(CreateCustomerAndUpdateMappingArg arg) {
        LogUtils.info("OrderServiceImpl.createCustomerAndUpdateMapping,arg={}",arg);
        String fsEa = arg.getFsEa();
        String channelName = Optional.ofNullable(ConfigCenter.FEISHU_LARK_APP_ID_CHANNEL)
                .map(map -> map.get(arg.getAppId()))
                .orElse(ChannelEnum.feishu.name()); //新购订单，开通全新的纷享企业
        ChannelEnum channelEnum= ChannelEnum.valueOf(channelName);
        CreateCustomerArg customerArg = CreateCustomerArg.builder()
                .outEid(arg.getOutEid())
                .managerName(arg.getInstallerName())
                .managerMobile(null)//通过飞书商店应用获取不到手机号
                .enterpriseName(arg.getEnterpriseName())
                .channel(channelName)
                .enterpriseAccount(fsEa)
                .build();
        if(!channelName.equals(ChannelEnum.feishu.name())){
            //使用新的开通
            customerArg.setIsNew(true);
            customerArg.setEnterpriseEnv(EnterpriseEvnEnum.forsharecrm_public_prod);
            customerArg.setLanguage(EnterpriseLanguageEnum.english);
            customerArg.setInitLanguage(EnterpriseInitLanguageEnum.english);
        }
        com.facishare.open.order.contacts.proxy.api.result.Result<String> customer2 = fsOrderServiceProxy.createCustomer2(customerArg);
        LogUtils.info("OrderServiceImpl.createCustomerAndUpdateMapping,result={}",customer2);
        if(!customer2.isSuccess()) {
            return Result.newError(customer2.getCode(), customer2.getMsg());
        }
//        FeishuEnterpriseConnectParams feishuEnterpriseConnectParams = FeishuEnterpriseConnectParams.builder().baseUrl(ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(arg.getAppId()))
//                .encryKey(ConfigCenter.feishuEncryptKey)
//                .build();

        FeiShuConnectorVo feiShuConnectorVo=new FeiShuConnectorVo();
        feiShuConnectorVo.setConnectorName(channelName);
        feiShuConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
        feiShuConnectorVo.setAlertConfig(true);
        feiShuConnectorVo.setCorpId(arg.getOutEid());
        feiShuConnectorVo.setAlertTypes(Lists.newArrayList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_NOTIFICATION));
        feiShuConnectorVo.setAuthType(AuthTypeEnum.OAUTH2.getCode());
        feiShuConnectorVo.setAuthType(arg.getAppId());
        feiShuConnectorVo.setDataCenterName(arg.getEnterpriseName());
        feiShuConnectorVo.setBaseUrl(ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(arg.getAppId()));
        feiShuConnectorVo.setEnterpriseName(ConfigCenter.feishuEncryptKey);
        feiShuConnectorVo.setIsFirstLand(true);
        feiShuConnectorVo.setIsRetainInformation(false);
        //保存企业绑定关系
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity=OuterOaEnterpriseBindEntity.builder()
                .id(IdGenerator.get())
                .appId(arg.getAppId())
                .fsEa(fsEa)
                .outEa(arg.getOutEa())
                .channel(channelEnum)
                .connectInfo(JSONObject.toJSONString(feiShuConnectorVo))
                .bindType(BindTypeEnum.auto)
                .bindStatus(BindStatusEnum.create)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .build();
        int insertEnt= outerOaEnterpriseBindManager.batchUpsert(Lists.newArrayList(outerOaEnterpriseBindEntity));
        //需要设置规则
        OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, outerOaEnterpriseBindEntity.getId());

        if(entityByDataCenterId.getConfigInfo()!=null){
            SettingAccountRulesModel settingAccountRulesModel= JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            settingAccountRulesModel.setSyncTypeEnum(EnterpriseConfigAccountSyncTypeEnum.accountSync);
            settingAccountRulesModel.setBindTypeEnum(BindTypeEnum.auto);
            entityByDataCenterId.setConfigInfo(JSON.toJSONString(settingAccountRulesModel));
            LogUtils.info("upsert enterprise model :{}",entityByDataCenterId);
            outerOaConfigInfoManager.updateById(entityByDataCenterId);
        }
        //保存管理员绑定关系
        OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = OuterOaEmployeeBindEntity.builder()
                .id(IdGenerator.get())
                .dcId(outerOaEnterpriseBindEntity.getId())
                .appId(arg.getAppId())
                .fsEa(fsEa)
                .outEa(arg.getOutEa())
                .fsEmpId(GlobalValue.FS_ADMIN_USER_ID + "")
                .outEmpId(arg.getInstallerUserId())
                .channel(ChannelEnum.valueOf(channelName))
                .bindStatus(BindStatusEnum.create).build();
        int insertEmp= outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(outerOaEmployeeBindEntity));
        LogUtils.info("OrderServiceImpl.createCustomerAndUpdateMapping,insertEnt={},insertEmp={}",insertEnt,insertEmp);

        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> isEnterpriseBind(String ea) {
        List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesByOuterEaChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), ea
                , null, BindStatusEnum.create);
        LogUtils.info("FsEventServiceImpl.isEnterpriseBind,entity={}", entitiesByFsEaChanelEnums);
        return Result.newSuccess(entitiesByFsEaChanelEnums!=null);
    }

    @Override
    public Result<Void> updateEnterpriseAndAdminMapping(String ea, String adminUserId) {
        enterpriseBindManager.updateBindStatus(ea, null, BindStatusEnum.normal);
        Integer count = outerOaEnterpriseBindManager.updateByEaNormal(ea);
        outerOaEmployeeBindManager.updateEmpStatusByCrmUserId(adminUserId,BindStatusEnum.normal,ea);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendWelcomeMsg(String fsEa) {
        OuterOaEnterpriseBindParams params = OuterOaEnterpriseBindParams.builder()
                .fsEa(fsEa)
                .bindStatus(BindStatusEnum.normal)
                .build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager.getEntities(params);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.BIND_ENTERPRISE_ERROR);
        }
        String outEa=enterpriseBindList.get(0).getOutEa();
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity=enterpriseBindList.get(0);
//        AppInfoEntity appInfoEntity = appInfoManager.getEntity(outEa);
        OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(outerOaEnterpriseBindEntity.getChannel(), outerOaEnterpriseBindEntity.getOutEa(), outerOaEnterpriseBindEntity.getAppId());
        //发送欢迎消息给应用安装人员
        OuterOaEmployeeBindEntity userEntity = outerOaEmployeeBindManager.getEntitiesByDcId(outerOaEnterpriseBindEntity.getId(), "1000", null);
        feishuMessageService.sendWelcomeMsg(appInfoEntity.getAppId(),appInfoEntity.getOutEa(),userEntity.getOutEmpId());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initEnterpriseContacts(String ea) {
        com.facishare.open.feishu.syncapi.result.Result<Void> result = contactsService.initContactsAsync(ea);
        return Result.newError(result.getCode(),result.getMsg());
    }

//    @Override
//    public Result<String> onOrderPaid(FeishuOrderPaidEvent event) {
//        String traceId = TraceUtils.getTraceId();
//        if(StringUtils.isEmpty(traceId)) {
//            traceId = UUID.randomUUID().toString();
//            TraceUtils.initTraceId(traceId);
//        }
//        //飞书订单表，用tenantKey当corpId
//        String corpId = event.getTenantKey();
//
//        Result<OrderInfoEntity> result = saveOrder(event);
//        LogUtils.info("OrderServiceImpl.onOrderPaid,result={}",result);
//        if(!result.isSuccess()) {
//            return Result.newError(result.getCode(),result.getMsg());
//        }
//
//        if(result.isSuccess()) {
//            OrderInfoEntity entity = result.getData();
//
//            //非首次下单，不需要获取人员信息
//            List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(corpId);
//            Result<UserData.User> singleUserInfo = null;
//            final String traceId2 = TraceUtils.getTraceId();
//            if(CollectionUtils.isEmpty(enterpriseBindList)) {
//                //创建一个线程，一分钟后检查企业是否创建成功
//                new Thread(() -> {
//                    TraceUtils.initTraceId(traceId2);
//                    enterpriseOpenMonitor(corpId, event.getAppId());
//                }).start();
//                singleUserInfo = employeeBindService.getAppInstallerInfo(event.getAppId(),event.getTenantKey());
//                LogUtils.info("OrderServiceImpl.onOrderPaid,singleUserInfo={}", singleUserInfo);
//                if (!singleUserInfo.isSuccess()) {
//                    int retryCount = 0;
//                    if(retryMap.containsKey(event.getTenantKey())) {
//                        retryCount = retryMap.get(event.getTenantKey());
//                    }
//                    //客户首次安装应用并且下试用单，有可能无法立即获取到飞书的管理员，这里做一下重试，正常情况下，重试一次即可成功
//                    if (retryCount < 10) {
//                        retryCount++;
//                        retryMap.put(event.getTenantKey(),retryCount);
//                        scheduledExecutorService.schedule(()->{
//                            TraceUtils.initTraceId(traceId2);
//                            LogUtils.info("OrderServiceImpl.onOrderPaid,begin retry onOrderPaid");
//                            Result<String> retryResult = onOrderPaid(event);
//                            LogUtils.info("OrderServiceImpl.onOrderPaid,end retry onOrderPaid,retryResult={}",retryResult);
//                        },2000, TimeUnit.MILLISECONDS);
//                    } else {
//                        LogUtils.info("OrderServiceImpl.onOrderPaid,retry 10 times, cannot get app installer info,cannot run openEnterprise func, event={}",event);
//                    }
//
//                    return Result.newSuccess(FAIL);
//                }
//            }
//            Result<Void> openEnterprise = openEnterprise(entity,
//                    ObjectUtils.isNotEmpty(singleUserInfo) ? singleUserInfo.getData().getOpenId() : null,
//                    ObjectUtils.isNotEmpty(singleUserInfo) ? singleUserInfo.getData().getName() : null);
//            LogUtils.info("OrderServiceImpl.onOrderPaid,openEnterprise={}",openEnterprise);
//        }
//        return Result.newSuccess(SUCCESS);
//    }

    private CreateCrmOrderMultipleProductArg buildCreateCrmOrderArg(OuterOaOrderInfoEntity entity, String fsEa) {
        FeishuOrderOtherFields feishuOrderOtherFields = JSONObject.parseObject(entity.getOrderInfo(), FeishuOrderOtherFields.class);

        int userCount =  feishuOrderOtherFields.getUserCount();
        LogUtils.info("OrderServiceImpl.openEnterprise,userCount={}", userCount);
        if(entity.getOrderType()== OuterOaOrderInfoTypeEnum.upgrade) {
            //增购或版本升级，飞书返回的是总的用户数，我们应该减去升级前的用户数

            OuterOaOrderInfoEntity entityByOrderId = outerOaOrderInfoManager.getEntityByOrderId(feishuOrderOtherFields.getSrcOrderId());
            FeishuOrderOtherFields srcOrderOtherFields = JSONObject.parseObject(entityByOrderId.getOrderInfo(), FeishuOrderOtherFields.class);
            userCount = userCount - srcOrderOtherFields.getUserCount();
        } else if(entity.getOrderType() == OuterOaOrderInfoTypeEnum.renew) {
            //续订人数不变，只增加年限
            userCount = 0;
        }
        LogUtils.info("OrderServiceImpl.openEnterprise,userCount2={}", userCount);

        VersionModel versionModel = ConfigCenter.getFirstVersionProductId(entity.getAppId(), feishuOrderOtherFields.getEditionId());
        CrmOrderTypeEnum crmOrderType= feishuOrderOtherFields.getPricePlanType() == PricePlanTypeEnum.trial ? CrmOrderTypeEnum.TRY : CrmOrderTypeEnum.BUY;
        EnterpriseLanguageEnum enterpriseLanguageEnum=ChannelEnum.lark.equals(entity.getChannel())?EnterpriseLanguageEnum.english:EnterpriseLanguageEnum.chinese;
        CreateCrmOrderMultipleProductArg.CrmOrderDetailInfo orderDetailInfo = CreateCrmOrderMultipleProductArg.CrmOrderDetailInfo.builder()
                .enterpriseAccount(fsEa)
                .orderId(entity.getOrderId())
                .orderTime(entity.getBeginTime())
                .crmOrderType(crmOrderType)
                .language(enterpriseLanguageEnum)
                .build();

        //订单金额单位是 分，需要转换成 元
        BigDecimal orderAmount = BigDecimal.valueOf(feishuOrderOtherFields.getPayPrice() / 100.0);
        CreateCrmOrderMultipleProductArg.CrmOrderProductInfo orderProductInfo = CreateCrmOrderMultipleProductArg.CrmOrderProductInfo.builder()
                .beginTime(entity.getBeginTime())
                .endTime(entity.getEndTime())
                .quantity(userCount)
                .allResourceCount(userCount)
                .orderAmount(orderAmount + "")
                .productId(versionModel.getProductId())
                .build();
        List<CreateCrmOrderMultipleProductArg.CrmOrderProductInfo> productInfos= new ArrayList<>();
        productInfos.add(orderProductInfo);

        //需要根据应用配置，确定是不是需要补充其他产品
        List<CrmOrderProductVo> productIds = ConfigCenter.app_other_product_map.get(entity.getAppId());
        if(ObjectUtils.isNotEmpty(productIds)){
            for (CrmOrderProductVo productVo : productIds) {
                CreateCrmOrderMultipleProductArg.CrmOrderProductInfo productInfo = CreateCrmOrderMultipleProductArg.CrmOrderProductInfo.builder()
                        .beginTime(entity.getBeginTime())
                        .endTime(entity.getEndTime())
                        .quantity(1)
                        .allResourceCount(productVo.getSourceAccount())
                        .orderAmount(orderAmount + "")
                        .productId(productVo.getProductId())
                        .build();
                productInfos.add(productInfo);
            }
        }
        CreateCrmOrderMultipleProductArg orderArg = CreateCrmOrderMultipleProductArg.builder()
                .crmOrderDetailInfo(orderDetailInfo)
                .crmOrderProductInfo(productInfos)
                .build();

        return orderArg;
    }

//    @Override
//    public Result<Void> openEnterprise(OrderInfoEntity entity,String installUserId,String installerName) {
//        CorpInfoEntity corpInfoEntity = getCorpInfoEntity(entity.getAppId(),entity.getPaidCorpId()).getData();
//        CreateCrmOrderArg orderArg = buildCreateCrmOrderArg(entity,corpInfoEntity);
//
//        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(corpInfoEntity.getTenantKey());
//        LogUtils.info("OrderServiceImpl.openEnterprise,enterpriseBindList={}", enterpriseBindList);
//        if(CollectionUtils.isEmpty(enterpriseBindList)) {
//            String fsEa = orderArg.getCrmOrderDetailInfo().getEnterpriseAccount();
//
//            //新购订单，开通全新的纷享企业
//            CreateCustomerArg customerArg = CreateCustomerArg.builder()
//                    .source(CustomerSourceEnum.FEISHU.getSource())
//                    .outEid(corpInfoEntity.getDisplayId())
//                    .managerName(installerName)
//                    .managerMobile(null)//通过飞书商店应用获取不到手机号
//                    .enterpriseName(corpInfoEntity.getTenantName())
//                    .enterpriseAccount(fsEa)
//                    .build();
//
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.openEnterprise(customerArg, orderArg);
//            if(!result.isSuccess()) {
//                return Result.newError(result.getCode(), result.getMsg());
//            }
//
//            //保存企业绑定关系
//            enterpriseBindManager.insert(entity.getChannel(),
//                    fsEa,
//                    entity.getPaidCorpId(),
//                    ConfigCenter.crm_domain,
//                    BindTypeEnum.auto,
//                    BindStatusEnum.create);
//
//            //保存管理员绑定关系
//            employeeBindManager.insert(entity.getChannel(),
//                    fsEa,
//                    GlobalValue.FS_ADMIN_USER_ID+"",
//                    entity.getPaidCorpId(),
//                    installUserId,
//                    BindTypeEnum.auto,
//                    BindStatusEnum.create);
//        }
//        return Result.newSuccess();
//    }

    @Override
    public Result<Void> saveOrderAndAddCrmOrder(FeishuOrderPaidEvent event) {
        LogUtils.info("OrderServiceImpl.createAndSaveOrder,event={}", event);
        Result<OuterOaOrderInfoEntity> saveOrder = saveOrder(event);
        LogUtils.info("OrderServiceImpl.createAndSaveOrder,saveOrder={}", saveOrder);
        if(saveOrder.isSuccess()) {
            List<OuterOaEnterpriseBindEntity> enterpriseBindList = getEnterpriseBindList(event.getTenantKey()).getData();
            LogUtils.info("OrderServiceImpl.createAndSaveOrder,enterpriseBindList={}", enterpriseBindList);
            if(CollectionUtils.isNotEmpty(enterpriseBindList)) {
                if(enterpriseBindList.size()==1) {
                    OuterOaOrderInfoEntity entity = saveOrder.getData();
                    //自动绑定的企业，重新下单逻辑
                    if(enterpriseBindList.get(0).getBindType()== BindTypeEnum.auto) {
                        String fsEa = enterpriseBindList.get(0).getFsEa();
                        LogUtils.info("OrderServiceImpl.createAndSaveOrder,fsEa={}", fsEa);
                        CreateCrmOrderMultipleProductArg orderArg = buildCreateCrmOrderArg(entity,fsEa);
                        LogUtils.info("OrderServiceImpl.createAndSaveOrder,orderArg={}", orderArg);
                        //升级订单或续订订单，更新纷享企业
                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrderMultipleProduct(orderArg);
                        //需要根据应用配置，确定是不是需要补充其他产品
//                        List<CrmOrderProductVo> productIds = ConfigCenter.app_other_product_map.get(entity.getAppId());
//                        for (CrmOrderProductVo productVo : productIds) {
//                            buyConnectorConfigProductId(fsEa, "0", orderArg.getCrmOrderProductInfo().getBeginTime(), orderArg.getCrmOrderProductInfo().getEndTime(),productVo.getProductId(),productVo.getSourceAccount());
//                        }
                        LogUtils.info("OrderServiceImpl.createAndSaveOrder,createCrmOrder,result={}", result);
                        return Result.newInstance(result.getCode(),result.getMsg());
                    } else {
                        //手动绑定地企业，重新下单，前面保存完订单信息，到这里就结束了。
                        return Result.newSuccess();
                    }
                }
                //多对一场景，多个纷享企业对一个飞书企业
                if(enterpriseBindList.size()>1) {
                    //只有手动绑定场景，才支持多个纷享企业对一个飞书企业，重新下单，前面保存完订单信息，到这里就结束了。
                    return Result.newSuccess();
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo(String fsEa, String outEa) {
        //查询企业outEa

        List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), fsEa,outEa);

        if(CollectionUtils.isEmpty(entitiesByFsEaChanelEnums)) {
            LogUtils.warn("getEnterprise trial data null:{}", fsEa);
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = entitiesByFsEaChanelEnums.get(0);
        EnterpriseTrialInfo enterpriseTrialInfo = new EnterpriseTrialInfo();
        enterpriseTrialInfo.setOutEa(enterpriseBindEntity.getOutEa());
        enterpriseTrialInfo.setBindType(enterpriseBindEntity.getBindType());
        //反绑定的企业不赋值
        if(enterpriseBindEntity.getBindType() == BindTypeEnum.manual) {
            return Result.newSuccess(enterpriseTrialInfo);
        }
        FeiShuConnectorVo feiShuConnectorVo=JSONObject.parseObject(enterpriseBindEntity.getConnectInfo(), FeiShuConnectorVo.class);
        if(feiShuConnectorVo.getIsFirstLand().equals(Boolean.TRUE)) {
            //更新至数据库
            feiShuConnectorVo.setIsFirstLand(Boolean.FALSE);
            enterpriseBindEntity.setConnectInfo(new Gson().toJson(feiShuConnectorVo));
            enterpriseBindManager.updateExtend(fsEa, enterpriseBindEntity.getOutEa(), enterpriseBindEntity);
        }

        //查询最新的订单

        OuterOaOrderInfoEntity latestOrderEntity = outerOaOrderInfoManager.getLatestOrder(enterpriseBindEntity.getOutEa());
        if(latestOrderEntity==null||latestOrderEntity.getOrderInfo()==null) {
            return Result.newError(ResultCodeEnum.ORDER_INFO_NOT_EXISTS);
        }
        FeishuOrderOtherFields feishuOrderOtherFields=JSONObject.parseObject(latestOrderEntity.getOrderInfo(), FeishuOrderOtherFields.class);
        //判断该订单是不是客户下单并且是不是试用订单
        if(feishuOrderOtherFields.getOrderFrom() == OrderFromEnum.customer && feishuOrderOtherFields.getPricePlanType() == PricePlanTypeEnum.trial) {
            enterpriseTrialInfo.setIsTrial(Boolean.TRUE);
            enterpriseTrialInfo.setBeginTime(new Timestamp(latestOrderEntity.getBeginTime()));
            enterpriseTrialInfo.setEndTime(new Timestamp(latestOrderEntity.getEndTime()));
        } else {
            enterpriseTrialInfo.setIsTrial(Boolean.FALSE);
        }
        return Result.newSuccess(enterpriseTrialInfo);
    }

    private void enterpriseOpenMonitor(String outEa, String appId) {
        LogUtils.info("OrderServiceImpl.enterpriseOpenMonitor,outEa={},appId={}", outEa, appId);
        //睡眠一分钟 呼呼大睡
        try {
            Thread.sleep(60 * 1000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        //查库
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams=OuterOaEnterpriseBindParams.builder().outEa(outEa).appId(appId).build();
        List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
        if(CollectionUtils.isNotEmpty(entitiesByFsEaChanelEnums)) {
            OuterOaEnterpriseBindEntity enterpriseBindEntity = entitiesByFsEaChanelEnums.get(0);
            if(enterpriseBindEntity.getBindStatus().equals(BindStatusEnum.create)) {
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .ea(enterpriseBindEntity.getFsEa())
                        .appId(appId)
                        .channelId(ChannelEnum.feishu.name())
                        .dataTypeId(DataTypeEnum.ENTERPRISE_CREATE.getDataType())
                        .corpId(outEa)
                        .errorCode("100")
                        .errorMsg("超过一分钟，该企业还未创建成功，请及时关注！") //ignorei18n
                        .build();
                oaConnectorOpenDataManager.send(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle("飞书企业开通失败告警"); //ignorei18n
                String msg = String.format("超过一分钟，该企业还未创建成功\n纷享企业ea=%s\n请及时关注！", enterpriseBindEntity.getFsEa()); //ignorei18n
                arg.setMsg(msg);
                notificationService.sendNotice(arg);
            }
        } else {
            //没有记录，自己接收告警
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(appId)
                    .channelId(ChannelEnum.feishu.name())
                    .dataTypeId(DataTypeEnum.ENTERPRISE_CREATE.getDataType())
                    .corpId(outEa)
                    .errorCode("101")
                    .errorMsg("超过一分钟，该企业没有绑定记录，请及时关注！") //ignorei18n
                    .build();
            oaConnectorOpenDataManager.send(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("飞书企业开通失败告警"); //ignorei18n
            String msg = String.format("超过一分钟，该企业没有绑定记录\n飞书企业ea=%s\n请及时关注！", outEa); //ignorei18n
            arg.setMsg(msg);
            notificationService.sendNotice(arg);
        }
    }

    @Override
    public com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnector(String fsEa,
                                                                                        String orderAmount,
                                                                                        Long beginTime,
                                                                                        Long endTime) {
        long orderTime = System.currentTimeMillis();
        if(orderTime >= endTime) {
            return com.facishare.open.order.contacts.proxy.api.result.Result.newError(com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum.PARAM_ILLEGAL);
        }
        BigDecimal amount = BigDecimal.valueOf(Double.valueOf(orderAmount));
        CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = new CreateCrmOrderArg.CrmOrderProductInfo();
        orderProductInfo.setProductId(ConfigCenter.feishuConnectorProductId);
        orderProductInfo.setQuantity(ConfigCenter.createCrmAccount);
        orderProductInfo.setOrderAmount(orderAmount);
        orderProductInfo.setAllResourceCount(ConfigCenter.createCrmAccount);
        orderProductInfo.setBeginTime(beginTime);
        orderProductInfo.setEndTime(endTime);

        CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = new CreateCrmOrderArg.CrmOrderDetailInfo();
        orderDetailInfo.setEnterpriseAccount(fsEa);
        orderDetailInfo.setOrderTime(orderTime);
        orderDetailInfo.setOrderTpye(amount.compareTo(BigDecimal.ZERO)==0 ? 3 : 1);
        orderDetailInfo.setOrderId("erpdss_feishu_connector_"+System.currentTimeMillis());

        CreateCrmOrderArg orderArg = new CreateCrmOrderArg();
        orderArg.setCrmOrderProductInfo(orderProductInfo);
        orderArg.setCrmOrderDetailInfo(orderDetailInfo);

        LogUtils.info("OrderServiceImpl.buyConnector,orderArg={}", JSONObject.toJSONString(orderArg));
        return fsOrderServiceProxy.createCrmOrder(orderArg);
    }

    @Override
    public com.facishare.open.order.contacts.proxy.api.result.Result<Void> buyConnectorConfigProductId(String fsEa, String orderAmount, Long beginTime, Long endTime, String productId,Integer quantity) {
        long orderTime = System.currentTimeMillis();
        if(orderTime >= endTime) {
            return com.facishare.open.order.contacts.proxy.api.result.Result.newError(com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum.PARAM_ILLEGAL);
        }
        BigDecimal amount = BigDecimal.valueOf(Double.valueOf(orderAmount));
        CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = new CreateCrmOrderArg.CrmOrderProductInfo();
        orderProductInfo.setProductId(productId);
        orderProductInfo.setQuantity(quantity);
        orderProductInfo.setOrderAmount(orderAmount);
        orderProductInfo.setAllResourceCount(quantity);
        orderProductInfo.setBeginTime(beginTime);
        orderProductInfo.setEndTime(endTime);

        CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = new CreateCrmOrderArg.CrmOrderDetailInfo();
        orderDetailInfo.setEnterpriseAccount(fsEa);
        orderDetailInfo.setOrderTime(orderTime);
        orderDetailInfo.setOrderTpye(amount.compareTo(BigDecimal.ZERO)==0 ? 3 : 1);
        orderDetailInfo.setOrderId("erpdss_feishu_connector_"+System.currentTimeMillis());

        CreateCrmOrderArg orderArg = new CreateCrmOrderArg();
        orderArg.setCrmOrderProductInfo(orderProductInfo);
        orderArg.setCrmOrderDetailInfo(orderDetailInfo);

        LogUtils.info("OrderServiceImpl.buyConnector,orderArg={}", JSONObject.toJSONString(orderArg));
        return fsOrderServiceProxy.createCrmOrder(orderArg);
    }
}
