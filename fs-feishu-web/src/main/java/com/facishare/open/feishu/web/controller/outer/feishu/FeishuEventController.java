package com.facishare.open.feishu.web.controller.outer.feishu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.model.event.ExternalDealTodoEvent;
import com.facishare.open.feishu.syncapi.model.event.FeishuEncryptMsgModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.Result3;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService;
import com.facishare.open.feishu.web.handler.FeishuEventHandler;
import com.facishare.open.feishu.web.template.FeishuOuterEventHandlerTemplate;
import com.facishare.open.feishu.web.utils.FeishuDecrypt;
import com.facishare.open.feishu.web.excel.FileManager;
import com.facishare.open.feishu.web.excel.BaseListener;
import com.facishare.open.feishu.web.excel.ReadExcel;
import com.facishare.open.feishu.web.excel.vo.EmployeeBindExcelVo;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 飞书商店应用和自建应用事件回调接口
 * <AUTHOR>
 * @date 2022-05-26
 */
@RestController
@RequestMapping(value="/feishu/event")
//IgnoreI18nFile
public class FeishuEventController {
    @Resource
    private ExternalTodoMsgService externalTodoMsgService;
    @Resource
    private FeishuOuterEventHandlerTemplate feishuOuterEventHandlerTemplate;
    @Resource
    private FileManager fileManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private ObjectDataManager objectDataManager;


    /**
     * 飞书商店应用和自建应用事件回调
     * @param body
     * @return
     */
    @RequestMapping(value="/push",method = RequestMethod.POST)
    @ResponseBody
    public String push(@RequestBody String body) {
        LogUtils.info("FeishuEventController.push,body={}",body);
        return feishuOuterEventHandlerTemplate.execute(body).getDataOrMsg();
    }


    /**
     * 迁移数据
     * @param outerOaEmployeeBindEntities
     * @return
     */
    @RequestMapping(value="/transferData",method = RequestMethod.POST)
    @ResponseBody
    public Result<Integer> push(@RequestBody List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities) {
       outerOaEmployeeBindManager.batchBindEmployeeList(outerOaEmployeeBindEntities);
       return Result.newSuccess(outerOaEmployeeBindEntities.size());
    }

//    /**
//     * 消息卡片请求网址
//     * 每当用户操作消息卡片时，飞书服务器会向你配置的网址发出请求
//     * @param body
//     * @return
//     */
//    @RequestMapping(value="/robot/push",method = RequestMethod.POST)
//    @ResponseBody
//    public String robotPush(@RequestBody String body) {
//        LogUtils.info("FeishuEventController.robotPush,body={}",body);
//
//        try {
//            //验证网页推送事件
//            String verifyResult = feishuOuterEventHandlerTemplate.verifyUrl(body);
//            if(!StringUtils.equalsIgnoreCase(verifyResult,FeishuEventHandler.FAIL)) {
//                return verifyResult;
//            }
//        } catch (Exception e) {
//            LogUtils.info("FeishuEventController.robotPush,exception={}",e.getMessage());
//        }
//        return FeishuEventHandler.FAIL;
//    }

    @RequestMapping(value="/approval/deal",method = RequestMethod.POST)
    @ResponseBody
    public Result3<Void> approvalDeal(@RequestBody String body,
                                      HttpServletRequest req,
                                      HttpServletResponse resp) throws ServletException, IOException {
        LogUtils.info("FeishuEventController.approvalDeal,body={}",body);
        FeishuEncryptMsgModel model = JSONObject.parseObject(body,FeishuEncryptMsgModel.class);
        LogUtils.info("FeishuEventController.approvalDeal,model={}",model);
        if(model==null || StringUtils.isEmpty(model.getEncrypt())) {
            // 设置状态码
            resp.setStatus(HttpServletResponse.SC_ACCEPTED);
            return Result3.newError(ResultCodeEnum.EXTERNAL_DEAL_TODO_ERROR);
        }
        FeishuDecrypt feishuDecrypt = new FeishuDecrypt(ConfigCenter.feishuEncryptKey);
        try {
            String plainText = feishuDecrypt.decrypt(model.getEncrypt());
            LogUtils.info("FeishuEventController.approvalDeal,plainText={}",plainText);
            if(StringUtils.isEmpty(plainText)) {
                // 设置状态码
                resp.setStatus(HttpServletResponse.SC_ACCEPTED);
                return Result3.newError(ResultCodeEnum.EXTERNAL_DEAL_TODO_ERROR);
            }

            //获取token
            JSONObject eventJsonObject = JSONObject.parseObject(plainText);
            Object tokenObject = eventJsonObject.get("token");
            if(ObjectUtils.isNotEmpty(tokenObject) && String.valueOf(tokenObject).equals(ConfigCenter.APPROVAL_ACTION_CALLBACK_TOKEN)) {
                ExternalDealTodoEvent externalDealTodoEvent = JSON.parseObject(plainText, ExternalDealTodoEvent.class);
                Result<Void> dealCrmTodoResult = externalTodoMsgService.dealCrmTodo(externalDealTodoEvent);
                if(dealCrmTodoResult.isSuccess()) {
                    return Result3.newSuccess();
                } else {
                    // 设置状态码
                    resp.setStatus(HttpServletResponse.SC_ACCEPTED);
                    return Result3.newError(dealCrmTodoResult.getCode(), dealCrmTodoResult.getMsg());
                }
            }

            // 设置状态码
            resp.setStatus(HttpServletResponse.SC_ACCEPTED);
            return Result3.newError(ResultCodeEnum.EXTERNAL_DEAL_TODO_ERROR);
        } catch (Exception e) {
            LogUtils.info("FeishuEventController.approvalDeal,exception={}",e);
            // 设置状态码
            resp.setStatus(HttpServletResponse.SC_ACCEPTED);
            return Result3.newError(ResultCodeEnum.EXTERNAL_DEAL_TODO_ERROR);
        }
    }

    /**
     * 读取本地Excel文件并转换为EmployeeBindEntity列表
     * @param filePath Excel文件路径，默认为 /Users/<USER>/Documents/h3cmagic_employee.xlsx
     * @param sheetName Excel工作表名称，可选参数
     * @param fsEa 纷享EA，用于设置转换后的实体，可选参数
     * @return EmployeeBindEntity列表
     */
    @RequestMapping(value="/readLocalExcel", method = RequestMethod.GET)
    @ResponseBody
    public Result3<List<OuterOaEmployeeBindEntity>> readLocalExcel(@RequestParam(value = "filePath", required = false) String filePath,
                                                           @RequestParam(value = "sheetName", required = false) String sheetName) {
        // 默认文件路径
        if (StringUtils.isEmpty(filePath)) {
            filePath = "/Users/<USER>/Documents/h3cmagic_employee.xlsx";
        }

        LogUtils.info("FeishuEventController.readLocalExcel, filePath={}, sheetName={}", filePath, sheetName);

        try {
            // 创建文件输入流
            FileInputStream fileInputStream = new FileInputStream(filePath);

            // 创建Excel读取监听器
            BaseListener<EmployeeBindExcelVo> listener = new BaseListener<EmployeeBindExcelVo>() {};

            // 配置Excel读取参数
            ReadExcel.Arg<EmployeeBindExcelVo> readExcelArg = new ReadExcel.Arg<>();
            readExcelArg.setExcelListener(listener);
            readExcelArg.setType(EmployeeBindExcelVo.class);
            readExcelArg.setInputStream(fileInputStream);

            // 如果指定了工作表名称，则按工作表名称读取，否则读取第一个工作表
            if (StringUtils.isNotEmpty(sheetName)) {
                readExcelArg.setSheetName(sheetName);
                fileManager.readExcelBySheetName(readExcelArg);
            } else {
                fileManager.readExcel(readExcelArg);
            }

            // 获取读取到的Excel数据
            List<EmployeeBindExcelVo> excelDataList = listener.getDataList();
            LogUtils.info("FeishuEventController.readLocalExcel, 读取到Excel数据条数: {}", excelDataList.size());

            // 转换为EmployeeBindEntity列表
            List<EmployeeBindEntity> employeeBindList = convertToEmployeeBindEntityList(excelDataList);
            List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = convertFeishuToOuterEmployeeBind(employeeBindList);
            LogUtils.info("FeishuEventController.readLocalExcel success, 转换后的EmployeeBindEntity数量: {}", employeeBindList.size());
            return Result3.newSuccess(outerOaEmployeeBindEntities);

        } catch (IOException e) {
            LogUtils.error("FeishuEventController.readLocalExcel IOException", e);
            return Result3.newError(ResultCodeEnum.UPLOAD_FILE_ERROR.getCode(), "文件读取失败: " + e.getMessage());
        } catch (Exception e) {
            LogUtils.error("FeishuEventController.readLocalExcel Exception", e);
            return Result3.newError(ResultCodeEnum.SYSTEM_ERROR.getCode(), "Excel文件处理失败: " + e.getMessage());
        }
    }

    /**
     * 飞书处理错误人员id
     */
    @RequestMapping(value="/refixEmp", method = RequestMethod.GET)
    @ResponseBody
    public Result3<List<OuterOaEmployeeBindEntity>> refixEmp() {
        List<OuterOaEnterpriseBindEntity> allByChannel = outerOaEnterpriseBindManager.getAllByChannel(ChannelEnum.feishu);
        for (OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity : allByChannel) {
            List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = outerOaEmployeeBindManager
                    .batchGetEmployeeBindEntity(outerOaEnterpriseBindEntity.getChannel(),
                            outerOaEnterpriseBindEntity.getOutEa(), outerOaEnterpriseBindEntity.getFsEa(), null, null, null);
            //需要判断crmuserId位数大于10位
            for (OuterOaEmployeeBindEntity employeeBindEntity : outerOaEmployeeBindEntities) {
                String crmUserId = employeeBindEntity.getFsEmpId();
                if (crmUserId.length() > 10) {
                    ObjectData objectData = objectDataManager.getObjectData(AccountTypeEnum.EMP_BIND.getCode(), crmUserId, outerOaEnterpriseBindEntity.getFsEa());
                    if (ObjectUtils.isNotEmpty(objectData)) {
                        String fsEmpId = objectData.getString(CRMEmployeeFiledEnum.USER_ID.getCode());
                        employeeBindEntity.setFsEmpId(fsEmpId);
                        outerOaEmployeeBindManager.updateById(employeeBindEntity);
                        LogUtils.info("refixEmp before:{},after:{}.ea:{}",crmUserId,fsEmpId,outerOaEnterpriseBindEntity.getFsEa());
                    }
                }
            }
        }

        return Result3.newSuccess();
    }


    /**
     * 将Excel数据转换为EmployeeBindEntity列表
     * @param excelDataList Excel数据列表
     * @return EmployeeBindEntity列表
     */
    private List<EmployeeBindEntity> convertToEmployeeBindEntityList(List<EmployeeBindExcelVo> excelDataList) {
        List<EmployeeBindEntity> employeeBindList = new ArrayList<>();

        for (EmployeeBindExcelVo excelVo : excelDataList) {
            try {
                EmployeeBindEntity entity = new EmployeeBindEntity();

                // 转换渠道枚举
                if (StringUtils.isNotEmpty(excelVo.getChannel())) {
                    try {
                        entity.setChannel(ChannelEnum.valueOf(excelVo.getChannel().toUpperCase()));
                    } catch (IllegalArgumentException e) {
                        LogUtils.warn("无效的渠道类型: {}, 使用默认值 feishu", excelVo.getChannel());
                        entity.setChannel(ChannelEnum.feishu);
                    }
                } else {
                    entity.setChannel(ChannelEnum.feishu); // 默认为飞书
                }

                // 设置基本字段
                entity.setFsEa(excelVo.getFsEa());
                entity.setFsUserId(excelVo.getFsUserId());
                entity.setFsLeaderUserId(excelVo.getFsLeaderUserId());
                entity.setOutEa(excelVo.getOutEa());
                entity.setOutUserId(excelVo.getOutUserId());
                entity.setOutLeaderUserId(excelVo.getOutLeaderUserId());

                // 转换绑定类型枚举
                if (StringUtils.isNotEmpty(excelVo.getBindType())) {
                    try {
                        entity.setBindType(BindTypeEnum.valueOf(excelVo.getBindType().toUpperCase()));
                    } catch (IllegalArgumentException e) {
                        LogUtils.warn("无效的绑定类型: {}, 使用默认值 MANUAL", excelVo.getBindType());
                        entity.setBindType(BindTypeEnum.manual);
                    }
                } else {
                    entity.setBindType(BindTypeEnum.manual); // 默认为手动绑定
                }

                // 转换绑定状态枚举
                if (StringUtils.isNotEmpty(excelVo.getBindStatus())) {
                    try {
                        entity.setBindStatus(BindStatusEnum.valueOf(excelVo.getBindStatus().toUpperCase()));
                    } catch (IllegalArgumentException e) {
                        LogUtils.warn("无效的绑定状态: {}, 使用默认值 BIND", excelVo.getBindStatus());
                        entity.setBindStatus(BindStatusEnum.normal);
                    }
                } else {
                    entity.setBindStatus(BindStatusEnum.normal); // 默认为已绑定
                }

                // 设置时间字段
                Date now = new Date();
                entity.setCreateTime(now);
                entity.setUpdateTime(now);

                employeeBindList.add(entity);

            } catch (Exception e) {
                LogUtils.error("转换Excel数据到EmployeeBindEntity失败, excelVo: {}", excelVo, e);
                // 继续处理下一条数据，不中断整个转换过程
            }
        }

        return employeeBindList;
    }

    private  List<OuterOaEmployeeBindEntity> convertFeishuToOuterEmployeeBind(List<EmployeeBindEntity> employeeBindEntities){
        String dataCenterId="568";
        String appId="cli_a8c0c2b42a70501c";
        List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities=new ArrayList<>();
        for (EmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            OuterOaEmployeeBindEntity outerOaEmployeeBindEntity=new OuterOaEmployeeBindEntity();
            outerOaEmployeeBindEntity.setId(String.valueOf(employeeBindEntity.getId()));
            outerOaEmployeeBindEntity.setDcId(dataCenterId);
            outerOaEmployeeBindEntity.setChannel(ChannelEnum.feishu);
            outerOaEmployeeBindEntity.setFsEmpId(employeeBindEntity.getFsUserId());
            outerOaEmployeeBindEntity.setOutEmpId(employeeBindEntity.getOutUserId());
            outerOaEmployeeBindEntity.setCreateTime(employeeBindEntity.getCreateTime().getTime());
            outerOaEmployeeBindEntity.setUpdateTime(employeeBindEntity.getUpdateTime().getTime());
            outerOaEmployeeBindEntity.setFsEa(employeeBindEntity.getFsEa());
            outerOaEmployeeBindEntity.setOutEa(employeeBindEntity.getOutEa());
            outerOaEmployeeBindEntity.setAppId(appId);
            outerOaEmployeeBindEntity.setId(IdGenerator.get());
            outerOaEmployeeBindEntity.setBindStatus(employeeBindEntity.getBindStatus());

            outerOaEmployeeBindEntities.add(outerOaEmployeeBindEntity);
        }
        return outerOaEmployeeBindEntities;
//        outerOaEmployeeBindManager.batchUpsert(outerOaEmployeeBindEntities);
    }
}
