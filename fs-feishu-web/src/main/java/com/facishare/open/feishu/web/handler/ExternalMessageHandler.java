package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.syncapi.arg.*;
import com.facishare.open.feishu.syncapi.config.OAMessageTag;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.FeiShuConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.OutOaMsgEventTypeEnum;
import com.facishare.open.feishu.syncapi.proto.OutOaMsgChangeProto;
import com.facishare.open.feishu.web.filter.EnterpriseInterconnectFilter;
import com.facishare.open.feishu.web.mq.sender.MQSender;
import com.facishare.open.feishu.web.template.inner.msg.*;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ExternalMessageHandler {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private FeishuCreateTodoTemplate feishuCreateTodoTemplate;
    @Resource
    private EnterpriseInterconnectFilter enterpriseInterconnectFilter;
    @Resource
    private MQSender mqSender;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private FeishuDealTodoTemplate feishuDealTodoTemplate;
    @Resource
    private FeishuDeleteTodoTemplate feishuDeleteTodoTemplate;
    @Resource
    private FeishuSendTextMsgTemplate feishuSendTextMsgTemplate;
    @Resource
    private FeishuSendTextCarMsgTemplate feishuSendTextCarMsgTemplate;

    public void dealCreateTodoHandler(CreateTodoArg createTodoArg) {
        Gson gson = new Gson();
        //消息分发 1、小于一亿，正常推送 2、大于一亿，互联推送，3、上游可以推送给不同的下游的人员处理
        List<CreateTodoArg> createTodoArgs = null;
        try {
            createTodoArgs = enterpriseInterconnectFilter.filterArgs(createTodoArg);
        } catch (Exception e) {
            createTodoArgs = Lists.newArrayList(createTodoArg);
        }
        if (CollectionUtils.isEmpty(createTodoArgs)) {
            return;
        }

        LogUtils.info("ExternalMessageHandler.dealCreateTodoHandler.createTodoArgs={}", createTodoArgs);

        for(CreateTodoArg arg : createTodoArgs) {
            if (!arg.getEa().equals(createTodoArg.getEa())) {
                //互联待办
                OutOaMsgChangeProto oaMsgChangeProto = new OutOaMsgChangeProto();
                oaMsgChangeProto.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                CreateTodoPushArg createTodoPushArg = new CreateTodoPushArg();
                createTodoPushArg.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                createTodoPushArg.setUpstreamEa(createTodoArg.getEa());
                createTodoPushArg.setCreateTodoArg(arg);
                oaMsgChangeProto.setData(gson.toJson(createTodoPushArg));

                mqSender.sendOutOaMsgChangeSender(OAMessageTag.CREATE_TO_DO_TAG, oaMsgChangeProto, String.valueOf(eieaConverter.enterpriseAccountToId(arg.getEa())), arg.getSourceId());

                continue;
            }

            //查询所有绑定关系
            List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), arg.getEa(),null);
            if (CollectionUtils.isEmpty(entitiesByFsEaChanelEnums)) {
                continue;
            }
            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : entitiesByFsEaChanelEnums) {
                try {
                    BaseConnectorVo baseConnectorVo = JSONObject.parseObject(enterpriseBindEntity.getConnectInfo(), enterpriseBindEntity.getChannel().getClassName());
                    if(!baseConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_TODO)){
                        LogUtils.info("enterprise not support todo:{}",enterpriseBindEntity.getFsEa());
                        continue;
                    }

                    CreateTodoPushArg createTodoPushArg = new CreateTodoPushArg();
                    createTodoPushArg.setMsgType(OutOaMsgEventTypeEnum.commonMsg.name());
                    createTodoPushArg.setEnterpriseBindEntity(enterpriseBindEntity);

                    createTodoPushArg.setCreateTodoArg(arg);
                    feishuCreateTodoTemplate.execute(createTodoPushArg);
                } catch (Exception e) {
                    LogUtils.info("ExternalMessageHandler.dealCreateTodoHandler.e={}", e.getMessage());
                }
            }
        }
    }

    public void dealDealTodoHandler(DealTodoArg dealTodoArg) {
        Gson gson = new Gson();
        //消息分发 1、小于一亿，正常推送 2、大于一亿，互联推送，3、上游可以推送给不同的下游的人员处理
        List<DealTodoArg> dealTodoArgs = null;
        try {
            dealTodoArgs = enterpriseInterconnectFilter.filterArgs(dealTodoArg);
        } catch (Exception e) {
            dealTodoArgs = Lists.newArrayList(dealTodoArg);
        }
        if (CollectionUtils.isEmpty(dealTodoArgs)) {
            return;
        }
        LogUtils.info("ExternalMessageHandler.dealDealTodoHandler.createTodoArgs={}", dealTodoArgs);

        for(DealTodoArg arg : dealTodoArgs) {
            if (!arg.getEa().equals(dealTodoArg.getEa())) {
                //互联待办
                OutOaMsgChangeProto oaMsgChangeProto = new OutOaMsgChangeProto();
                oaMsgChangeProto.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                DealTodoPushArg dealTodoPushArg = new DealTodoPushArg();
                dealTodoPushArg.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                dealTodoPushArg.setDealTodoArg(arg);
                dealTodoPushArg.setUpstreamEa(dealTodoArg.getEa());
                oaMsgChangeProto.setData(gson.toJson(dealTodoPushArg));

                mqSender.sendOutOaMsgChangeSender(OAMessageTag.DEAL_TO_DO_TAG, oaMsgChangeProto, String.valueOf(eieaConverter.enterpriseAccountToId(arg.getEa())), arg.getSourceId());

                continue;
            }

            //查询所有绑定关系
            List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), arg.getEa(),null);

            if (CollectionUtils.isEmpty(entitiesByFsEaChanelEnums)) {
                continue;
            }

            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : entitiesByFsEaChanelEnums) {
                try {
                    BaseConnectorVo baseConnectorVo = JSONObject.parseObject(enterpriseBindEntity.getConnectInfo(), enterpriseBindEntity.getChannel().getClassName());
                    if(!baseConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_TODO)){
                        LogUtils.info("enterprise not support todo:{}",enterpriseBindEntity.getFsEa());
                        continue;
                    }
                    DealTodoPushArg dealTodoPushArg = new DealTodoPushArg();
                    dealTodoPushArg.setMsgType(OutOaMsgEventTypeEnum.commonMsg.name());
                    dealTodoPushArg.setEnterpriseBindEntity(enterpriseBindEntity);
                    dealTodoPushArg.setDealTodoArg(arg);
                    feishuDealTodoTemplate.execute(dealTodoPushArg);
                } catch (Exception e) {
                    LogUtils.info("ExternalMessageHandler.dealDealTodoHandler.e={}", e.getMessage());
                }
            }
        }
    }

    public void dealDeleteTodoHandler(DeleteTodoArg deleteTodoArg) {
        Gson gson = new Gson();
        //消息分发 1、小于一亿，正常推送 2、大于一亿，互联推送，3、上游可以推送给不同的下游的人员处理
        List<DeleteTodoArg> deleteTodoArgs = null;
        try {
            deleteTodoArgs = enterpriseInterconnectFilter.filterArgs(deleteTodoArg);
        } catch (Exception e) {
            deleteTodoArgs = Lists.newArrayList(deleteTodoArg);
        }
        if (CollectionUtils.isEmpty(deleteTodoArgs)) {
            return;
        }
        LogUtils.info("ExternalMessageHandler.dealDeleteTodoHandler.deleteTodoArgs={}", deleteTodoArgs);

        for(DeleteTodoArg arg : deleteTodoArgs) {
            if (!arg.getEa().equals(deleteTodoArg.getEa())) {
                //互联待办
                OutOaMsgChangeProto oaMsgChangeProto = new OutOaMsgChangeProto();
                oaMsgChangeProto.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                DeleteTodoPushArg deleteTodoPushArg = new DeleteTodoPushArg();
                deleteTodoPushArg.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                deleteTodoPushArg.setDeleteTodoArg(arg);
                deleteTodoPushArg.setUpstreamEa(deleteTodoArg.getEa());
                oaMsgChangeProto.setData(gson.toJson(deleteTodoPushArg));

                mqSender.sendOutOaMsgChangeSender(OAMessageTag.DELETE_TO_DO, oaMsgChangeProto, String.valueOf(eieaConverter.enterpriseAccountToId(arg.getEa())), arg.getSourceId());

                continue;
            }

            //查询所有绑定关系
            List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), arg.getEa(),null);

            if (CollectionUtils.isEmpty(entitiesByFsEaChanelEnums)) {
                continue;
            }

            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : entitiesByFsEaChanelEnums) {
                try {
                    BaseConnectorVo baseConnectorVo = JSONObject.parseObject(enterpriseBindEntity.getConnectInfo(), enterpriseBindEntity.getChannel().getClassName());
                    if(!baseConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_TODO)){
                        LogUtils.info("enterprise not support todo:{}",enterpriseBindEntity.getFsEa());
                        continue;
                    }
                    DeleteTodoPushArg deleteTodoPushArg = new DeleteTodoPushArg();
                    deleteTodoPushArg.setMsgType(OutOaMsgEventTypeEnum.commonMsg.name());
                    deleteTodoPushArg.setEnterpriseBindEntity(enterpriseBindEntity);
                    deleteTodoPushArg.setDeleteTodoArg(arg);
                    feishuDeleteTodoTemplate.execute(deleteTodoPushArg);
                } catch (Exception e) {
                    LogUtils.info("ExternalMessageHandler.dealDeleteTodoHandler.e={}", e.getMessage());
                }
            }
        }
    }

    public void dealSendTextMessageHandler(SendTextMessageArg sendTextMessageArg) {
        Gson gson = new Gson();
        //消息分发 1、小于一亿，正常推送 2、大于一亿，互联推送，3、上游可以推送给不同的下游的人员处理
        List<SendTextMessageArg> sendTextMessageArgs = null;
        try {
            sendTextMessageArgs = enterpriseInterconnectFilter.filterArgs(sendTextMessageArg);
        } catch (Exception e) {
            sendTextMessageArgs = Lists.newArrayList(sendTextMessageArg);
        }
        if (CollectionUtils.isEmpty(sendTextMessageArgs)) {
            return;
        }
        LogUtils.info("ExternalMessageHandler.dealSendTextMessageHandler.sendTextMessageArgs={}", sendTextMessageArgs);

        for(SendTextMessageArg arg : sendTextMessageArgs) {
            if (!arg.getEa().equals(sendTextMessageArg.getEa())) {
                //互联待办
                OutOaMsgChangeProto oaMsgChangeProto = new OutOaMsgChangeProto();
                oaMsgChangeProto.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                SendTextMessagePushArg sendTextMessagePushArg = new SendTextMessagePushArg();
                sendTextMessagePushArg.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                sendTextMessagePushArg.setSendTextMessageArg(arg);
                sendTextMessagePushArg.setUpstreamEa(sendTextMessageArg.getEa());
                oaMsgChangeProto.setData(gson.toJson(sendTextMessagePushArg));

                mqSender.sendOutOaMsgChangeSender(OAMessageTag.TEXT_MSG_TAG, oaMsgChangeProto, String.valueOf(eieaConverter.enterpriseAccountToId(arg.getEa())), arg.getEa());

                continue;
            }

            //查询所有绑定关系
            List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), arg.getEa(),null);


            if (CollectionUtils.isEmpty(entitiesByFsEaChanelEnums)) {
                continue;
            }

            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : entitiesByFsEaChanelEnums) {
                try {
                    BaseConnectorVo baseConnectorVo = JSONObject.parseObject(enterpriseBindEntity.getConnectInfo(), enterpriseBindEntity.getChannel().getClassName());
                    if(!baseConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_NOTIFICATION)){
                        LogUtils.info("enterprise not support todo:{}",enterpriseBindEntity.getFsEa());
                        continue;
                    }
                    SendTextMessagePushArg sendTextMessagePushArg = new SendTextMessagePushArg();
                    sendTextMessagePushArg.setMsgType(OutOaMsgEventTypeEnum.commonMsg.name());
                    sendTextMessagePushArg.setEnterpriseBindEntity(enterpriseBindEntity);
                    sendTextMessagePushArg.setSendTextMessageArg(arg);
                    feishuSendTextMsgTemplate.execute(sendTextMessagePushArg);
                } catch (Exception e) {
                    LogUtils.info("ExternalMessageHandler.dealSendTextMessageHandler.e={}", e.getMessage());
                }
            }
        }
    }

    public void dealSendTextCardMessageHandler(SendTextCardMessageArg sendTextCardMessageArg) {
        Gson gson = new Gson();
        //消息分发 1、小于一亿，正常推送 2、大于一亿，互联推送，3、上游可以推送给不同的下游的人员处理
        List<SendTextCardMessageArg> sendTextCardMessageArgs = null;
        try {
            sendTextCardMessageArgs = enterpriseInterconnectFilter.filterArgs(sendTextCardMessageArg);
        } catch (Exception e) {
            sendTextCardMessageArgs = Lists.newArrayList(sendTextCardMessageArg);
        }
        if (CollectionUtils.isEmpty(sendTextCardMessageArgs)) {
            return;
        }
        LogUtils.info("ExternalMessageHandler.dealSendTextCardMessageHandler.sendTextCardMessageArgs={}", sendTextCardMessageArgs);

        for(SendTextCardMessageArg arg : sendTextCardMessageArgs) {
            if (!arg.getEa().equals(sendTextCardMessageArg.getEa())) {
                //互联待办
                OutOaMsgChangeProto oaMsgChangeProto = new OutOaMsgChangeProto();
                oaMsgChangeProto.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                SendTextCardMessagePushArg sendTextCardMessagePushArg = new SendTextCardMessagePushArg();
                sendTextCardMessagePushArg.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                sendTextCardMessagePushArg.setSendTextCardMessageArg(arg);
                sendTextCardMessagePushArg.setUpstreamEa(sendTextCardMessageArg.getEa());
                oaMsgChangeProto.setData(gson.toJson(sendTextCardMessagePushArg));

                mqSender.sendOutOaMsgChangeSender(OAMessageTag.CARD_MSG_TAG, oaMsgChangeProto, String.valueOf(eieaConverter.enterpriseAccountToId(arg.getEa())), arg.getEa());

                continue;
            }

            //查询所有绑定关系
            List<OuterOaEnterpriseBindEntity> entitiesByFsEaChanelEnums = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.feishu, ChannelEnum.lark), arg.getEa(),null);

            if (CollectionUtils.isEmpty(entitiesByFsEaChanelEnums)) {
                continue;
            }

            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : entitiesByFsEaChanelEnums) {
                try {
                    BaseConnectorVo baseConnectorVo = JSONObject.parseObject(enterpriseBindEntity.getConnectInfo(), enterpriseBindEntity.getChannel().getClassName());
                    if(!baseConnectorVo.getAlertTypes().contains(AlertTypeEnum.CRM_NOTIFICATION)){
                        LogUtils.info("enterprise not support todo:{}",enterpriseBindEntity.getFsEa());
                        continue;
                    }
                    SendTextCardMessagePushArg sendTextCardMessagePushArg = new SendTextCardMessagePushArg();
                    sendTextCardMessagePushArg.setMsgType(OutOaMsgEventTypeEnum.commonMsg.name());
                    sendTextCardMessagePushArg.setEnterpriseBindEntity(enterpriseBindEntity);
                    sendTextCardMessagePushArg.setSendTextCardMessageArg(arg);
                    feishuSendTextCarMsgTemplate.execute(sendTextCardMessagePushArg);
                } catch (Exception e) {
                    LogUtils.info("ExternalMessageHandler.dealSendTextCardMessageHandler.e={}", e.getMessage());
                }
            }
        }
    }
}
