package com.facishare.open.feishu.sync.test.service;

import com.alibaba.fastjson.JSON;
import com.facishare.open.feishu.sync.manager.ErpdssManager;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.arg.FsBindWithFeishuArg;
import com.facishare.open.feishu.syncapi.arg.QueryConnectInfoArg;
import com.facishare.open.feishu.syncapi.arg.UpdateConnectParamsArg;
import com.facishare.open.feishu.syncapi.consts.Constant;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.model.ContactScopeModel;
import com.facishare.open.feishu.syncapi.model.EnterpriseModel;
import com.facishare.open.feishu.syncapi.model.FeiShuConnectParam;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.ContactsService;
import com.facishare.open.feishu.syncapi.service.EmployeeBindService;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.feishu.web.template.outer.event.order.FeishuOrderPaidEventHandlerTemplate;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

public class EnterpriseBindServiceTest extends BaseTest {

    @Autowired
    private EnterpriseBindService enterpriseBindService;
    @Autowired
    private ErpdssManager erpdssManager;
    @Autowired
    private EmployeeBindService employeeBindService;
    @Autowired
    private ContactsService contactsService;
    @Autowired
    private FeishuOrderPaidEventHandlerTemplate feishuOrderPaidEventHandlerTemplate;


    @Test
    public void getEnterpriseBindList(){
        Result<List<OuterOaEnterpriseBindEntity>> entityList = enterpriseBindService.getEnterpriseBindList("11b1a8c266da9758",null, null);
        List<String> eaList = entityList.getData().stream().map(OuterOaEnterpriseBindEntity::getFsEa).collect(Collectors.toList());
        System.out.println(eaList);
    }

    @Test
    public void fsBindWithFeishu(){
        FsBindWithFeishuArg arg = new FsBindWithFeishuArg();
        arg.setFsEa("fszdbd2575");
        arg.setDataCenterName("飞书连接器");
        //arg.setOutEa("100d08b69448975d");
        arg.setDisplayId("F385026706");
        arg.setDataCenterId("6662db01c8424e81adb52512");
        Result<CorpInfoEntity> result = enterpriseBindService.fsBindWithFeishu(arg);
        System.out.println(result);
    }

    @Test
    public void fsUnBindWithFeishu(){
        Result<Void> result = enterpriseBindService.fsUnBindWithFeishu(null);
        System.out.println(result);
    }

    @Test
    public void fsUnBindWithFeishu2(){
        Result<Void> result = enterpriseBindService.fsUnBindWithFeishu2("F385026706");
        System.out.println(result);
    }

    @Test
    public void queryConnectInfo() {
        QueryConnectInfoArg arg = new QueryConnectInfoArg();
        arg.setDataCenterId("67aaef560d4c3e000136d0b3");
        arg.setFsEa("feishu5044934");
        Result<FeiShuConnectParam> result = enterpriseBindService.queryConnectInfo(arg);
        System.out.println(result);
    }

    @Test
    public void checkAndInitConnector() {
        Result<Void> result = enterpriseBindService.checkAndInitConnector("fszdbd2575",null,null);
        System.out.println(result);
    }

    @Test
    public void getFsEaList() {
        Result<List<EnterpriseModel>> result = enterpriseBindService.getFsEaList("106114c88acad75d", "ou_fd11d94de6281a8d14d8a0235b89aa61");
        System.out.println(result);
    }

    @Test
    public void updateEnterpriseExtend() {
        Result<Void> result = enterpriseBindService.updateEnterpriseExtend("91449",null, "isFirstLand", Boolean.FALSE);
        System.out.println(result);
    }

    @Test
    public void queryEnterprises() {
        Result<List<OuterOaEnterpriseBindEntity>> result = enterpriseBindService.queryEnterprisesByBindType(BindTypeEnum.auto);
        System.out.println(result);
    }

    @Test
    public void updateConnectParams() {
        UpdateConnectParamsArg arg = new UpdateConnectParamsArg();
        arg.setTenantId("82777");
        arg.setDataCenterId("6620bfff787b9600016bc193");
        arg.setDataCenterName("企业微信2.1");
        arg.setConnectParams("{\"fsEa\":\"82777\",\"outEa\":\"wpwx1mDAAAEO7X_Roe5s3viyg0z_c06w\",\"outDepId\":\"2\"}");
        Result<Void> result = erpdssManager.updateConnectParams(arg);
        System.out.println(result);
    }

    @Test
    public void saveAutoBind() {
        Result<Integer> result = enterpriseBindService.saveAutoBind("85903", "100d08b69448975d", 2,null);
        System.out.println(result);
    }

    @Test
    public void testEmployee() {
//     enterpriseBindService.saveAutoBind("ddqybhzyl", "146a0bcf64d7175a", 2, ChannelEnum.lark);
//        employeeBindService.autoBindEmpByCrmEmployeeNumber("ddqybhzyl", "146a0bcf64d7175a", new LinkedList<>(),Constant.AUTO_BIND_FIELD_EMPNUM);
        employeeBindService.autoBindEmpByCrmEmployeeNumber("74860", "100d08b69448975d", new LinkedList<>(),Constant.AUTO_BIND_FIELD_EMPNUM);
//        employeeBindService.autoBindEmpByCrmEmployeeNumber("100d08b69448975d", "100d08b69448975d", new LinkedList<>(),Constant.AUTO_BIND_FIELD_EMPNUM);

    }

    @Test
    public void queryAutoBind() {
        Result<Integer> result = enterpriseBindService.queryAutoBind("85903", "100d08b69448975d");
        System.out.println(result);
    }

    @Test
    public void testData(){
        String outEa="146a0bcf64d7175a";
        String fsEa="ddqybhzyl";

        Result<ContactScopeModel> scopeData = contactsService.getContactScopeData("cli_a75815139bbc500e", "142f6d8a511b975e", ChannelEnum.feishu);
        employeeBindService.autoBindBySettingUnique(null,null,scopeData.getData());
    }

    @Test
    public void testCreateOrder(){
        String data="{\n" +
                "        \"app_id\":\"cli_a7fcf7b29af85003\",\n" +
                "        \"buy_count\":1,\n" +
                "        \"buy_type\":\"buy\",\n" +
                "        \"create_time\":\"1659943372\",\n" +
                "        \"order_id\":\"7129402494660526092\",\n" +
                "        \"order_pay_price\":0,\n" +
                "        \"pay_time\":\"1659943371\",\n" +
                "        \"price_plan_id\":\"price_a25f5f67a172d00e\",\n" +
                "        \"price_plan_type\":\"trial\",\n" +
                "        \"seats\":0,\n" +
                "        \"src_order_id\":\"\",\n" +
                "        \"tenant_key\":\"14cf7e093e96575a\",\n" +
                "        \"type\":\"order_paid\"\n" +
                "    }";
        FeishuOrderPaidEvent event = JSON.parseObject(data, FeishuOrderPaidEvent.class);

        feishuOrderPaidEventHandlerTemplate.execute(event);
    }
}
