package com.facishare.open.order.contacts.proxy.api.network;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.limiter.SpeedLimiter;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/18
 **/
@Slf4j
@Component
public class ProxyHttpClient {
    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    @Resource(name = "okHttpSupport")
    private OkHttpSupport okHttpSupport;

    @Autowired
    private SpeedLimiter speedLimiter;

    private RequestBody createJsonBody(Object params, MediaType mediaType) {
        return params instanceof String
                ? RequestBody.create(params.toString(), ObjectUtils.isNotEmpty(mediaType) ? mediaType : JSON_TYPE)
                : RequestBody.create(JSON.toJSONString(params), ObjectUtils.isNotEmpty(mediaType) ? mediaType : JSON_TYPE);
    }

    public <T> T postUrl(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JSON.toJSONString(params);
        return postUrlByJson(url,paramsJson,headerMap,typeReference);
    }

    public <T> T postUrlSerialNull(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JSONObject.toJSONString(params);
        return postUrlByJson(url,paramsJson,headerMap,typeReference);
    }

    public <T> T postUrlByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        MediaType mediaType = null;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
            if(headerMap.containsKey(GlobalValue.contentType)) {
                mediaType = MediaType.parse(headerMap.get(GlobalValue.contentType));
            }
        }
        if(!isAllow) {
            return null;
        }
        RequestBody requestBody = this.createJsonBody(paramsJson, mediaType);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        T result = null;
        try {
            result = this.okHttpSupport.parseObject(request, typeReference);
        } catch (Exception e) {
            log.error("post url failed, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        }
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, paramsJson, headerMap, result);
        return result;
    }

    public <T> T deleteByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("deleteByJson, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        MediaType mediaType = null;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
            if(headerMap.containsKey(GlobalValue.contentType)) {
                mediaType = MediaType.parse(headerMap.get(GlobalValue.contentType));
            }
        }
        if(!isAllow) {
            return null;
        }
        RequestBody requestBody = this.createJsonBody(paramsJson, mediaType);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).delete(requestBody).build();
        T result = null;
        try {
            result = this.okHttpSupport.parseObject(request, typeReference);
        } catch (Exception e) {
            log.error("deleteByJson failed, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        }
        log.debug("deleteByJson result, url={}, params={}, headerMap={}, result={}", url, paramsJson, headerMap, result);
        return result;
    }

    public <T> T getUrl(String url, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("getUrl, url={}, headerMap={}", url, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
        }
        if(!isAllow) {
            return null;
        }
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        T result = this.okHttpSupport.parseObject(request, typeReference);
        log.debug("getUrl result, url={}, headerMap={}, result={}", url, headerMap, result);
        return result;
    }

    public String postUrl(String url, Object params, Map<String, String> headerMap) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, params, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        MediaType mediaType = null;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
            if(headerMap.containsKey(GlobalValue.contentType)) {
                mediaType = MediaType.parse(headerMap.get(GlobalValue.contentType));
            }
        }
        if(!isAllow) {
            return null;
        }
        RequestBody requestBody = this.createJsonBody(params, mediaType);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        Object result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, params, headerMap, result);
        return result.toString();
    }

    public String postUrlSerialNull(String url, Object params, Map<String, String> headerMap) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, params, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
        }
        if(!isAllow) {
            return null;
        }
        RequestBody requestBody = params instanceof String
                ? RequestBody.create(JSON_TYPE, params.toString())
                : RequestBody.create(JSON_TYPE, JSONObject.toJSONString(params));
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        Object result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, requestBody, headerMap, result);
        return result.toString();
    }


    public String getUrl(String url, Map<String, String> headerMap) {
        log.debug("getUrl, url={}, headerMap={}", url, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
        }
        if(!isAllow) {
            return null;
        }
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        Object result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
        log.debug("getUrl result, url={}, headerMap={}, result={}", url, headerMap, result);
        return result.toString();
    }

    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    public static class ProxySyncCallback extends SyncCallback {

        @Override
        public Object response(Response response) throws Exception {
            //获取响应头
            if(ObjectUtils.isNotEmpty(response) && ObjectUtils.isNotEmpty(response.headers())) {
                Headers responseHeaders = response.headers();
                log.info("ProxySyncCallback,response={}", responseHeaders);
            }
            return response.body() != null ? response.body().string() : "null";
        }

    }

    public <T> T patchUrl(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JSON.toJSONString(params);
        return patchUrlByJson(url,paramsJson,headerMap,typeReference);
    }

    public <T> T patchUrlByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("patchUrlByJson, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        MediaType mediaType = null;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
            if(headerMap.containsKey(GlobalValue.contentType)) {
                mediaType = MediaType.parse(headerMap.get(GlobalValue.contentType));
            }
        }
        if(!isAllow) {
            return null;
        }
        RequestBody requestBody = this.createJsonBody(paramsJson, mediaType);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).patch(requestBody).build();
        T result = null;
        try {
            result = this.okHttpSupport.parseObject(request, typeReference);
        } catch (Exception e) {
            log.error("patchUrlByJson url failed, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        }
        log.debug("patchUrlByJson result, url={}, params={}, headerMap={}, result={}", url, paramsJson, headerMap, result);
        return result;
    }
}
