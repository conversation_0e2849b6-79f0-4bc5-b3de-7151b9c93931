package com.facishare.open.qywx.accountsync.model;

import com.facishare.common.fsi.ProtoBase;
import io.protostuff.Tag;
import lombok.Data;

@Data
public class ExternalContactEvent extends ProtoBase {
    @Tag(1)
    private String appId;

    @Tag(2)
    private String corpId;

    @Tag(3)
    private String fsEa;

    @Tag(4)
    private String changeType;

    @Tag(5)
    private String userId;

    @Tag(6)
    private String fsUserId;

    @Tag(7)
    private String externalUserId;//外部联系人的userid，注意不是企业成员的帐号

    @Tag(8)
    private String externalContactDetail; //从企业微信外部联系人详情接口拿回来的原始JSON数据
}
