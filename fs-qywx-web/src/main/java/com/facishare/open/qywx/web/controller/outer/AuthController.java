package com.facishare.open.qywx.web.controller.outer;

import com.alibaba.dubbo.common.URL;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.oa.base.dbproxy.utils.SecurityUtil;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.login.UserTicketModel;
import com.facishare.open.outer.oa.connector.common.api.params.QyweixinAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.qywx.web.manager.OANewBaseManager;
import com.facishare.open.qywx.web.manager.QYWeixinManager;
import com.facishare.open.qywx.web.template.model.GenFsTicketModel;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.core.enums.QYWXDataTypeEnum;
import com.facishare.open.qywx.accountsync.model.EnterpriseModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.template.inner.login.QyweixinLoginTemplate;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringManager;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18nUtils;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 企业微信auth授权相关接口集合
 * <AUTHOR>
 * @date ********
 */
@RestController
@Slf4j
@RequestMapping("/qyweixin")
public class AuthController {
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayService;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Resource(name = "qywxI18NStringManager")
    private I18NStringManager i18NStringManager;

    @ReloadableProperty("domainPrefix")
    private String domainPrefix;
    @ReloadableProperty("appLoginAuthScope")
    private String appLoginAuthScope;

    @Autowired
    private RedisDataSource redisDataSource;

    @Resource
    private QyweixinLoginTemplate qyweixinLoginTemplate;

    /**
     * 固定值：/hcrm/wechat/function/redirect?ticket={ticket}
     */
    @ReloadableProperty("hcrm.wechat.function.redirect.url")
    private String hcrmWechatFunctionRedirectUrl;

    private static final String STATE = "neeJw0w3Yw32Qhi6";

    private static final String VER = "V1_";

    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Resource
    private OANewBaseManager oANewBaseManager;

    @ReloadableProperty("qywx_ip_url")
    private String qywxIpUrl;

    @Resource
    private QYWeixinManager qyweixinManager;

    /**
     * 企业微信通用auth授权接口，可用于企微侧边栏等需要通用授权的场景，原生支持一对多
     * @param appId CRM应用的appId
     * @param redirectUrl 默认采用base64编码
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/doAuth", method = RequestMethod.GET)
    public void doAuth(@RequestParam(required = false) String appId,
                       @RequestHeader(value = "user-agent") String userAgent,
                       @RequestParam String redirectUrl,
                       HttpServletResponse response) throws IOException {
        log.info("AuthController.doAuth,appId={},redirectUrl={}", appId,redirectUrl);

        log.info("AuthController.doAuth,userAgent={}", userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("AuthController.doAuth,lang={}", lang);

        if (StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        String callbackUrl = domainPrefix + "/qyweixin/callback/auth?appId=" + appId +"&lang=" + lang + "&redirectUrl=" + redirectUrl;
        callbackUrl = URL.encode(callbackUrl);
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + appId + "&redirect_uri=" + callbackUrl + "&response_type=code&scope=" + appLoginAuthScope + "&state=" + STATE + "#wechat_redirect";
        log.info("AuthController.doAuth,url={},callbackUrl={}", url, callbackUrl);
        response.sendRedirect(url);
    }

    /**
     * 企业微信通用auth授权接口，可用于企微侧边栏等需要通用授权的场景，原生支持一对多
     * @param appId CRM应用的appId
     * @param redirectUrl 默认采用base64编码
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/doRepAuth", method = RequestMethod.GET)
    public void doRepAuth(@RequestParam String corpId,
                          @RequestParam(required = false) String appId,
                          @RequestHeader(value = "user-agent") String userAgent,
                          @RequestParam String redirectUrl,
                          HttpServletRequest request,
                          HttpServletResponse response) throws Exception {
        log.info("AuthController.doRepAuth,appId={},redirectUrl={}", appId, redirectUrl);

        log.info("AuthController.doRepAuth,userAgent={}", userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("AuthController.doRepAuth,lang={}", lang);

        if (StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        String callbackUrl = domainPrefix + "/qyweixin/callback/auth?appId=" + appId + "&lang=" + lang + "&redirectUrl=" + redirectUrl;
        callbackUrl = URL.encode(callbackUrl);

        corpId = qyweixinManager.corpId2OpenCorpId(corpId).getData();

        //不会拦截器做路由，在这里判断加重定向到新服务
        if(!oANewBaseManager.canRunInNewBaseByOutEa(corpId)) {
            return;
        }

        String state = appId + "@" + corpId;
        OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
        log.info("ControllerQYWeixin.doRepAppLogin,appInfoEntity={}", appInfoEntity);

        if(ObjectUtils.isEmpty(appInfoEntity)) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s117,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s118,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);

        String agentId = String.valueOf(qyweixinAppInfoParams.getAuthAppInfo().getAgentId());

        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+corpId+"&redirect_uri="+callbackUrl+"&response_type=code&scope=snsapi_privateinfo&state="+state+"&agentid=" +agentId+"#wechat_redirect";
        log.info("trace doRepAppLogin, get url:{} ", url);
        response.sendRedirect(url);
    }

    /**
     * 企业微信oauth授权回调接口，支持一对多场景
     * @param code 企微授权码
     * @param state
     * @param appId
     * @param redirectUrl 默认采用base64编码
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/callback/auth", method = RequestMethod.GET)
    public void callbackAuth(@RequestParam(required = false) String code,
                             @RequestParam String state,
                             @RequestParam String appId,
                             @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                             @RequestParam String redirectUrl,
                             @RequestParam(required = false) String userInfoStr,
                             HttpServletResponse response,
                             HttpServletRequest request) throws Exception {
        log.info("AuthController.callbackAuth,code={},state={},appid={},redirectUrl={}", code, state, appId, redirectUrl);

        if (StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }

        String outEa = null;
        if(StringUtils.startsWithIgnoreCase(appId,"dk")) {
            List<String> items = Splitter.on("@").splitToList(state);
            appId = items.get(0);
            outEa = items.get(1);
        }

        if(StringUtils.isEmpty(lang)) {
            lang = TraceUtil.getLocale();
        }
        log.info("callbackAuth,lang={}",lang);
        i18NStringManager.setDefaultRequestScope(request,lang);

        Object userInfo = null;

        if (StringUtils.isNotEmpty(code) && StringUtils.isEmpty(userInfoStr)) {
            //1.用code换企业微信用户信息
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("code", code);
            dataMap.put("appId", appId);
            dataMap.put("outEa", outEa);
            dataMap.put("isIsvCode", Boolean.TRUE);
            MethodContext context = MethodContext.newInstance(dataMap);

            qyweixinLoginTemplate.getOutUserInfoByCode(context);

            Result<Object> result = (Result<Object>) context.getResult().getData();

//        Result<Object> result = qyweixinGatewayService.code2AppLoginUserInfo(code, appId, outEa);
            log.info("AuthController.callbackAuth,result={}",result);
            if(!result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
                request.setAttribute("errorCode", result.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(result.getErrorMsg()) ? result.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s119,lang,null));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s120,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }
            userInfo = result.getData();
        } else {
            String pramUrl = new String(Base64.decodeBase64(userInfoStr.getBytes()));
            JSONObject jsonObject = JSON.parseObject(pramUrl);
            String type = jsonObject.getString("type"); // 获取类型信息
            Class<?> clazz = Class.forName(type); // 加载类
            userInfo = jsonObject.getObject("data", clazz);
//            String pramUrl = new String(org.apache.commons.codec.binary.Base64.decodeBase64(userInfoStr.getBytes()));
//            userInfo = JSONObject.parseObject(pramUrl, Object.class);
        }

        String outUserId = null;
        if (userInfo instanceof QyweixinUserDetailInfoRsp) {
            QyweixinUserDetailInfoRsp userDetailInfoRsp = (QyweixinUserDetailInfoRsp) userInfo;
            outEa = userDetailInfoRsp.getCorpid();
            outUserId = userDetailInfoRsp.getUserid();
        } else if(userInfo instanceof QyweixinUserSimpleInfoRsp) {
            QyweixinUserSimpleInfoRsp userSimpleInfoRsp = (QyweixinUserSimpleInfoRsp) userInfo;
            outEa = userSimpleInfoRsp.getCorpId();
            outUserId = userSimpleInfoRsp.getUserId();
        }
        log.info("AuthController.callbackAuth,outEa={},outUserId={}",outEa,outUserId);

        outEa = qyweixinManager.corpId2OpenCorpId(outEa).getData();

        //不会拦截器做路由，在这里判断加重定向到新服务
        if(!oANewBaseManager.canRunInNewBaseByOutEa(outEa)) {
            return;
        }

        //有跨云逻辑不能直接查询人员绑定关系作为开始
        //优先查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).build());

//        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> enterpriseMappingResult = qyweixinAccountBindService.selectAllEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), outEa);
//        if(!enterpriseMappingResult.isSuccess()) {
//            request.setAttribute("errorCode", "s320050002");
//            request.setAttribute("errorMsg", StringUtils.isNotEmpty(enterpriseMappingResult.getErrorMsg()) ? enterpriseMappingResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s121,lang,null));
//            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s122,lang,null));
//            request.getRequestDispatcher("/index.jsp").forward(request, response);
//            return;
//        }
//
//        List<QyweixinAccountEnterpriseMapping> enterpriseMappings = enterpriseMappingResult.getData();
        log.info("AuthController.callbackAuth,enterpriseBindEntities={}",enterpriseBindEntities);
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(appId)
                    .channelId(ChannelEnum.qywx.name())
                    .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                    .corpId(outEa)
                    .outUserId(outUserId)
                    .errorCode("100")
                    .errorMsg(i18NStringManager.get(I18NStringEnum.s123,lang,null))
                    .build();
            qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
            String msg = String.format(I18NStringEnum.s125.getI18nValue(), outEa, outUserId, TraceUtil.get());
            arg.setMsg(i18NStringManager.get2(I18NStringEnum.s125.getI18nKey(),lang,null,msg,Lists.newArrayList(
                    outEa, outUserId, TraceUtil.get()
            )));
            notificationService.sendQYWXNotice(arg);

            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s126.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s126.getI18nValue(),outEa,outUserId),
                    Lists.newArrayList(
                            outEa, outUserId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s127,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        String domain = domainPrefix;

        //获取正常状态下的企业列表
        List<OuterOaEnterpriseBindEntity> normalEnterpriseBindEntities = enterpriseBindEntities.stream()
                .filter(v -> v.getBindStatus() == BindStatusEnum.normal)
                .collect(Collectors.toList());

        boolean hasMore = Boolean.FALSE;
        List<OuterOaEmployeeBindEntity> employeeBindEntities = null;

        if(normalEnterpriseBindEntities.size() > 1) {
            //有跨云的企业不用
            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : normalEnterpriseBindEntities) {
                QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
                if (StringUtils.isNotEmpty(qywxConnectorVo.getDomain()) && !qywxConnectorVo.getDomain().equals(domainPrefix)) {
                    hasMore = Boolean.TRUE;
                    break;
                }
            }

            if (!hasMore) {
                //兼容老逻辑，看下人员绑定关系
                employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).outEmpId(outUserId).build());
                log.info("AuthController.callbackAuth,employeeBindEntities={}", employeeBindEntities);

                if (employeeBindEntities.size() > 1) {
                    hasMore = Boolean.TRUE;
                }
            }
        }
        log.info("AuthController.callbackAuth,hasMore={}",hasMore);

        //一对多
        if(normalEnterpriseBindEntities.size() > 1 && hasMore) {
            //在纷享环境处理
            response.sendRedirect(domain + "/pc-login/build/select_enterprise.html?channel=qywx&outEa="+ URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outUserId), "utf-8")+"&redirectUrl="+redirectUrl);
        } else if(CollectionUtils.isEmpty(normalEnterpriseBindEntities)) {
            //没有正常的
            List<OuterOaEnterpriseBindEntity> createEnterpriseBindEntityList = enterpriseBindEntities.stream()
                    .filter(v -> v.getBindStatus() == BindStatusEnum.create)
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(createEnterpriseBindEntityList)) {
                //只有停用状态的企业
                throw new RuntimeException(i18NStringManager.get(I18NStringEnum.s129,lang,null));
            }
            log.info("AuthController.callbackAuth,createEnterpriseBindEntityList={}",createEnterpriseBindEntityList);
            long time = System.currentTimeMillis() - createEnterpriseBindEntityList.get(0).getCreateTime();
            log.info("AuthController.callbackAuth,time={}",time);
            if(time > 15 * 1000) {
                //开通中的
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .appId(appId)
                        .channelId(ChannelEnum.qywx.name())
                        .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                        .ea(createEnterpriseBindEntityList.get(0).getFsEa())
                        .corpId(outEa)
                        .outUserId(outUserId)
                        .errorCode("101")
                        .errorMsg(i18NStringManager.get(I18NStringEnum.s123,lang,null))
                        .build();
                qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
                String msg = String.format(I18NStringEnum.s143.getI18nValue(), outEa, outUserId, createEnterpriseBindEntityList.get(0).getFsEa(), TraceUtil.get());
                arg.setMsg(i18NStringManager.get2(I18NStringEnum.s143.getI18nKey(),lang,null,msg,Lists.newArrayList(
                        outEa, outUserId, createEnterpriseBindEntityList.get(0).getFsEa(), TraceUtil.get()
                )));
                notificationService.sendQYWXNotice(arg);
            }

            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s149.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s149.getI18nValue(),outEa,outUserId),
                    Lists.newArrayList(
                            outEa, outUserId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s144,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
        } else {
            //只有一对一，特殊逻辑，还需要查询
            OuterOaEnterpriseBindEntity enterpriseBindEntity = normalEnterpriseBindEntities.get(0);
            if(normalEnterpriseBindEntities.size() > 1 && CollectionUtils.isNotEmpty(employeeBindEntities)) {
                log.info("AuthController.callbackAuth, hasMore={}, employeeBindEntities={}", hasMore, employeeBindEntities);
                String fsAccount = employeeBindEntities.get(0).getFsEa();

                // 使用 Stream API 查找匹配项
                Optional<OuterOaEnterpriseBindEntity> matchingMapping = normalEnterpriseBindEntities.stream()
                        .filter(mapping -> mapping.getFsEa().equals(fsAccount))
                        .findFirst();

                if(matchingMapping.isPresent()) {
                    enterpriseBindEntity = matchingMapping.get();
                }
            }
            log.info("AuthController.callbackAuth,enterpriseMapping={}", enterpriseBindEntity);
            QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
            domain = qywxConnectorVo.getDomain();
            if(ObjectUtils.isNotEmpty(domain) && !domain.equals(domainPrefix)) {
                //转到对应的环境处理
                String redirectUri = domain + "/qyweixin/loginCloudAuth?channel=qywx&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outUserId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+enterpriseBindEntity.getFsEa()), "utf-8")+"&redirectUrl="+URLEncoder.encode(redirectUrl, "utf-8");
                response.sendRedirect(redirectUri);
                return;
            }
            //查询人员是否正常
            OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, enterpriseBindEntity.getFsEa(), appId, outUserId);
            log.info("AuthController.callbackAuth,employeeBindEntity={}",employeeBindEntity);
            if(ObjectUtils.isEmpty(employeeBindEntity)) {
                if(enterpriseBindEntity.getBindType() == BindTypeEnum.auto) {
                    //上报
                    OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                            .appId(appId)
                            .channelId(ChannelEnum.qywx.name())
                            .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                            .ea(enterpriseBindEntity.getFsEa())
                            .corpId(outEa)
                            .outUserId(outUserId)
                            .errorCode("102")
                            .errorMsg(i18NStringManager.get(I18NStringEnum.s130,lang,null))
                            .build();
                    qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
                    //告警
                    SendTextNoticeArg arg = new SendTextNoticeArg();
                    arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                    List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                    arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                    arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
                    String msg = String.format(I18NStringEnum.s145.getI18nValue(), outEa, outUserId, enterpriseBindEntity.getFsEa(), TraceUtil.get());
                    arg.setMsg(i18NStringManager.get2(I18NStringEnum.s145.getI18nKey(),lang,null,msg,Lists.newArrayList(
                            outEa, outUserId, enterpriseBindEntity.getFsEa(), TraceUtil.get()
                    )));
                    notificationService.sendQYWXNotice(arg);

                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                            lang,
                            null,
                            String.format(I18NStringEnum.s147.getI18nValue(), outEa, outUserId),
                            Lists.newArrayList(
                                    outEa, outUserId
                            )));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s131,lang,null));
                    request.setAttribute("errorPageOutEa", outEa);
                    request.setAttribute("errorPageFsEa", enterpriseBindEntity.getFsEa());
                    request.setAttribute("errorPageOutUserId", outUserId);
                    request.setAttribute("errorPageToken", ConfigCenter.ERROR_PAGE_TOKEN);
                    request.setAttribute("errorPageAppId", appId);
                    request.setAttribute("createEmployee", Boolean.TRUE);
                    request.getRequestDispatcher("/index.jsp").forward(request, response);
                    return;
                }
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s147.getI18nValue(), outEa, outUserId),
                        Lists.newArrayList(
                                outEa, outUserId
                        )));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s132,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            if(employeeBindEntity.getBindStatus() == BindStatusEnum.stop) {
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s150.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s150.getI18nValue(),outEa,outUserId),
                        Lists.newArrayList(
                                outEa, outUserId
                        )));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s146,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }
            //查询人员在crm的状态
            Result<List<EmployeeDto>> fsEmpUserResult = qyweixinAccountSyncService.getFsEmpUser(enterpriseBindEntity.getFsEa(), 1000, Lists.newArrayList(Integer.parseInt(employeeBindEntity.getFsEmpId())));
            if(!fsEmpUserResult.isSuccess() || ObjectUtils.isEmpty(fsEmpUserResult.getData())) {
                request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s133,lang,null));
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s151.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s151.getI18nValue(),enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()),
                    Lists.newArrayList(
                            enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()
                    )));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            } else if(fsEmpUserResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
                request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s134,lang,null));
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s152.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s152.getI18nValue(),enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()),
                    Lists.newArrayList(
                            enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()
                    )));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            //正常登陆
            //4.如果一个企微企业只关联一个CRM企业，直接重定向到CRM登录页面
            Result<CorpTicketResult> ticket = qyweixinGatewayService.appAuth(userInfo, appId,employeeBindEntity);
            log.info("AuthController.callbackAuth,ticket={}",ticket);
            if(!ticket.isSuccess()) {
                request.setAttribute("errorCode", ticket.getErrorCode());
                request.setAttribute("errorMsg", ticket.getErrorMsg());
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s135,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            //为兼容专属云老企业，还不能直接去掉
            GetEnterpriseDataResult enterpriseInfo = this.getEnterpriseInfo(employeeBindEntity.getFsEa());
            if(ObjectUtils.isNotEmpty(enterpriseInfo) && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData()) && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
                domain = enterpriseInfo.getEnterpriseData().getDomain();
            }
            log.info("AuthController.callbackAuth,domain1={}",domain);


            String loginUrl = domain + hcrmWechatFunctionRedirectUrl.replace("{ticket}",ticket.getData().getTicket()) + "&redirectUrl="+redirectUrl;
            log.info("AuthController.callbackAuth,loginUrl={}",loginUrl);
            response.sendRedirect(loginUrl);
        }
    }

    private String decodeData(String data) {
        Pattern pattern = Pattern.compile(VER+"(.*)");
        Matcher matcher = pattern.matcher(SecurityUtil.decryptStr(data));
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }

    /**
     * 跨云免登
     * @param outEa 外部企业id
     * @param appId 应用id
     * @param userId 用户id
     * @param fsEa 纷享ea
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/loginCloudAuth", method = RequestMethod.GET)
    public void loginCloudAuth(@RequestParam String channel,
                               @RequestParam String appId,
                               @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                               @RequestParam(required = false) String fsEa,
                               @RequestParam String outEa,
                               @RequestParam String userId,
                               @RequestParam(required = false) String redirectUrl,
                               @RequestParam(required = false) String url, //待办直接推送的
                               HttpServletResponse response,
                               HttpServletRequest request) throws Exception {
        log.info("AuthController.loginCloudAuth,channel={},appId={},fsEa={},outEa={},userId={}",channel,appId,fsEa,outEa,userId);

        appId = decodeData(appId);
        if(StringUtils.isEmpty(appId)) {
            return;
        }

        if(StringUtils.isNotEmpty(fsEa)) {
            fsEa = decodeData(fsEa);
            if(StringUtils.isEmpty(fsEa)) {
                return;
            }
        }

        outEa = decodeData(outEa);
        if(StringUtils.isEmpty(outEa)) {
            return;
        }

        userId = decodeData(userId);
        if(StringUtils.isEmpty(userId)) {
            return;
        }

        if(StringUtils.isEmpty(lang)) {
            lang = TraceUtil.getLocale();
        }
        log.info("loginCloudAuth,lang={}",lang);
        i18NStringManager.setDefaultRequestScope(request,lang);

        //查询企业绑定
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.qywx, fsEa, outEa, appId);

        if(ObjectUtils.isEmpty(enterpriseBindEntity)) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s153.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s153.getI18nValue(),outEa,userId),
                    Lists.newArrayList(
                            outEa,userId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s136,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }
        //查询人员绑定
        OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, userId);
        log.info("AuthController.loginCloudAuth,employeeBindEntity={}",employeeBindEntity);

        if(ObjectUtils.isEmpty(employeeBindEntity)) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s147.getI18nValue(), outEa, userId),
                    Lists.newArrayList(
                            outEa, userId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s132,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        //正常登陆

        UserTicketModel ticketModel = new UserTicketModel();
        ticketModel.setAppId(appId);
        ticketModel.setCorpId(outEa);
        ticketModel.setFsEa(fsEa);
        ticketModel.setFsUserId(userId);
        ticketModel.setOuterUserId(employeeBindEntity.getOutEmpId());
        ticketModel.setTimestamp(System.currentTimeMillis());

        MethodContext context2 = MethodContext.newInstance(ticketModel);
        qyweixinLoginTemplate.genFsTicket(context2);
        Result<String> ticket = context2.getResultData();

//        Result<String> ticket = qyweixinGatewayService.genFsTicket(appId, outEa, userId, fsEa);
        log.info("AuthController.loginCloudAuth,ticket={}",ticket);
        if(!ticket.isSuccess()) {
            request.setAttribute("errorCode", ticket.getErrorCode());
            request.setAttribute("errorMsg", ticket.getErrorMsg());
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s135,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }
        //老企业兼容
        String domain = getEnterpriseInfo(fsEa).getEnterpriseData().getDomain();
        String loginUrl;
        if(StringUtils.isNotEmpty(url)) {
            url = url + (url.contains("?") ? "&" : "?") + "ticket="+ticket.getData();
            loginUrl = url.replace("https://www.fxiaoke.com",domain);
        } else if(StringUtils.isNotEmpty(redirectUrl)) {
            loginUrl = domain + hcrmWechatFunctionRedirectUrl.replace("{ticket}",ticket.getData()) + "&redirectUrl="+redirectUrl;
        } else {
            //跳转到正常的CRM主页
            loginUrl = qyweixinGatewayService.getRedirectLoginUrl(ticket.getData(), appId,outEa,domain, fsEa);
        }
        log.info("AuthController.loginCloudAuth,loginUrl={}",loginUrl);
        response.sendRedirect(loginUrl);
    }

    /**
     * 获取和企业微信绑定的CRM企业EA列表
     * @param outEa
     * @return
     */
    @RequestMapping(value="/getFsEaList",method = RequestMethod.GET)
    @ResponseBody
    public Result<List<EnterpriseModel>> getFsEaList(@RequestParam String outEa,
                                                     @RequestParam String outUserId,
                                                     @RequestParam(required = false) String appId) throws Exception {
        log.info("ControllerQYWeixin.getFsEaList,outEa={},outUserId={},appId={}",outEa, outUserId, appId);
        outEa = decodeData(URLDecoder.decode(outEa, "utf-8"));
        if(StringUtils.isEmpty(outEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        outUserId = decodeData(URLDecoder.decode(outUserId, "utf-8"));
        if(StringUtils.isEmpty(outUserId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        Result<List<EnterpriseModel>> result = qyweixinAccountBindInnerService.getFsEaList(outEa, outUserId, appId);
        log.info("ControllerQYWeixin.getFsEaList,result={}",result);

        if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            for(EnterpriseModel model : result.getData()) {
                model.setEa(URLEncoder.encode(SecurityUtil.encryptStr(VER + model.getEa()), "utf-8"));
            }
        }
        return result;
    }

    /**
     * CRM登录授权，前端在选择CRM企业列表页面使用
     * @param outEa
     * @param appId
     * @param userId
     * @param fsEa 用于一个企微企业对多个CRM的场景
     * @param redirectUrl 需要重定向到的业务URL
     * @throws IOException
     */
    @RequestMapping(value="/loginByFsEa",method = RequestMethod.GET)
    @ResponseBody
    public Result<String> loginByFsEa(@RequestParam String outEa,
                                      @RequestParam String appId,
                                      @RequestParam String userId,
                                      @RequestParam String fsEa,
                                      @RequestParam(required = false) String redirectUrl,
                                      @RequestHeader(value = "user-agent") String userAgent,
                                      HttpServletResponse response,
                                      HttpServletRequest request) throws Exception {
        log.info("ControllerQYWeixin.loginByFsEa,outEa={},appId={},userId={},fsEa={}",outEa,appId,userId,fsEa);

        log.info("AuthController.loginByFsEa,userAgent={}", userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("AuthController.loginByFsEa,lang={}", lang);
        i18NStringManager.setDefaultRequestScope(request,lang);

        outEa = decodeData(URLDecoder.decode(outEa, "utf-8"));
        if(StringUtils.isEmpty(outEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        appId = decodeData(URLDecoder.decode(appId, "utf-8"));
        if(StringUtils.isEmpty(appId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        userId = decodeData(URLDecoder.decode(userId, "utf-8"));
        if(StringUtils.isEmpty(userId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        fsEa = decodeData(URLDecoder.decode(fsEa, "utf-8"));
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        //查看企业所处环境
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.qywx, fsEa, outEa, appId);
        log.info("ControllerOpenQYWeixin.loginByFsEa,enterpriseBindEntity={}", enterpriseBindEntity);
        QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
        String domain = qywxConnectorVo.getDomain();
        if(StringUtils.isNotEmpty(domain) && !domain.equals(domainPrefix)) {
            //转到对应的环境处理
            String redirectUri = domain + "/qyweixin/loginCloudAuth?channel=qywx&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+fsEa), "utf-8")+"&redirectUrl="+URLEncoder.encode(redirectUrl, "utf-8");
            return new Result<>(redirectUri);
        }

        //1.生成CRM ticket
        //查询人员在crm的状态
        OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, userId);
        if(ObjectUtils.isEmpty(employeeBindEntity)) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s147.getI18nValue(), outEa, userId),
                    Lists.newArrayList(
                            outEa, userId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s132,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return null;
        }
        UserTicketModel ticketModel = new UserTicketModel();
        ticketModel.setAppId(appId);
        ticketModel.setCorpId(outEa);
        ticketModel.setFsEa(fsEa);
        ticketModel.setFsUserId(employeeBindEntity.getFsEmpId());
        ticketModel.setOuterUserId(employeeBindEntity.getOutEmpId());

        MethodContext context2 = MethodContext.newInstance(ticketModel);
        qyweixinLoginTemplate.genFsTicket(context2);
        Result<String> ticket = context2.getResultData();

//        Result<String> ticket = qyweixinGatewayService.genFsTicket(appId,outEa,userId,fsEa);
        if(!ticket.isSuccess() || StringUtils.isEmpty(ticket.getData())) {
            request.setAttribute("errorCode", ticket.getErrorCode());
            request.setAttribute("errorMsg", ticket.getErrorMsg());
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s135,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return null;
        }



        Result<List<EmployeeDto>> fsEmpUserResult = qyweixinAccountSyncService.getFsEmpUser(fsEa, 1000, Lists.newArrayList(Integer.parseInt(employeeBindEntity.getFsEmpId())));
        if(!fsEmpUserResult.isSuccess() || ObjectUtils.isEmpty(fsEmpUserResult.getData())) {
            request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s133,lang,null));
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s151.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s151.getI18nValue(),fsEa,employeeBindEntity.getFsEmpId()),
                    Lists.newArrayList(
                            fsEa,employeeBindEntity.getFsEmpId()
                    )));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return null;
        } else if(fsEmpUserResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
            request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s134,lang,null));
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s152.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s152.getI18nValue(),fsEa,employeeBindEntity.getFsEmpId()),
                    Lists.newArrayList(
                            fsEa,employeeBindEntity.getFsEmpId()
                    )));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return null;
        }
        log.info("ControllerQYWeixin.loginByFsEa,ticket={}",ticket);

        //2.生成重定向uri
        String loginUrl = null;
        //需要直接透传redirectUrl
        //为兼容专属云老企业，还不能直接去掉
        GetEnterpriseDataResult enterpriseInfo = this.getEnterpriseInfo(fsEa);
        domain = domainPrefix;
        if(ObjectUtils.isNotEmpty(enterpriseInfo) && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData()) && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
            domain = enterpriseInfo.getEnterpriseData().getDomain();
        }
        log.info("AuthController.loginByFsEa,domain={}",domain);
        if(StringUtils.isNotEmpty(redirectUrl)) {
            loginUrl = domain + hcrmWechatFunctionRedirectUrl.replace("{ticket}",ticket.getData()) + "&redirectUrl="+redirectUrl;
        } else {
            //跳转到正常的CRM主页
            loginUrl = qyweixinGatewayService.getRedirectLoginUrl(ticket.getData(), appId,outEa,null, fsEa);
        }
        log.info("AuthController.loginByFsEa,loginUrl={}",loginUrl);

        return new Result<>(loginUrl);
    }

    /**
     * 企微连接器auth授权回调URL
     * 场景：
     * 1.从集成平台->企微连接器设置页面->扫企微的码->回调这个URL
     * @param code
     * @param state 前端需要传 fsEa_dataCenterId
     * @param appId
     * @throws Exception
     */
    @RequestMapping(value = "/callback/auth2")
    public void callbackAuth2(@RequestParam(required = false) String code,
                              @RequestParam(required = false) String state,
                              @RequestParam(required = false) String appId,
                              @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                              HttpServletResponse response,
                              HttpServletRequest request) throws Exception {
        log.info("callbackAuth2,code={},state={},appId={}",code,state,appId);
        log.info("AuthController.callbackAuth2,lang={}", lang);
        //这个是特殊逻辑了，由于授权连接的时候，不知道用哪个应用的，只能从库里找一条正常的使用
        Result<QyweixinUserInfo3rdRsp> result = qyweixinGatewayService.getUserInfoByLoginAuthApp(code, appId);
        log.info("callbackAuth2,result={}",result);

        i18NStringManager.setDefaultRequestScope(request,lang);
        if(result.isSuccess() && result.getData()!=null) {
            if(StringUtils.isNotEmpty(result.getData().getCorpid())) {
                String authUserId = result.getData().getUserid();
                String authCorpId = result.getData().getCorpid();

                String mainCrmAppId = appId;
                OuterOaAppInfoEntity oaAppInfoEntity = null;
                if (appId.equals("wx867ad65506013329")) {
                    List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(authCorpId).status(OuterOaAppInfoStatusEnum.normal).build());
                    if (CollectionUtils.isNotEmpty(appInfoEntities)) {
                        oaAppInfoEntity = appInfoEntities.get(0);
                        mainCrmAppId = oaAppInfoEntity.getAppId();
                    }
                }

                log.info("callbackAuth2,oaAppInfoEntity={}",oaAppInfoEntity);
//                if(corpBindBoResult.getData()==null || corpBindBoResult.getData().getStatus()!=0) {
//                    corpBindBoResult = qyweixinGatewayService.getCorpBindInfo(authCorpId,
//                            appId);
//                    log.info("callbackAuth2,corpBindBoResult.2={}",corpBindBoResult);
//                }
                if(oaAppInfoEntity==null) {
                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s138,lang,null));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s138,lang,null));
                    request.getRequestDispatcher("/index.jsp").forward(request, response);
                    return;
                }

                //代开发应用不支持调用获取企微应用管理员接口
                if(!StringUtils.startsWithIgnoreCase(mainCrmAppId,"dk")) {
                    QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
                    Integer agentId = qyweixinAppInfoParams.getAuthAppInfo().getAgentId();
                    Result<QyweixinGetAdminListRsp> adminList = qyweixinAccountSyncService.getAdminList(oaAppInfoEntity.getOutEa(),
                            mainCrmAppId,
                            agentId);
                    log.info("callbackAuth2,adminList={}",adminList);
                    if(StringUtils.equalsIgnoreCase(adminList.getErrorCode(),"48004")) {
                        request.setAttribute("errorCode", "s320050002");
                        request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s139,lang,null));
                        request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s140,lang,null));
                        request.getRequestDispatcher("/index.jsp").forward(request, response);
                        return;
                    }
                    if(adminList.getData()==null) {
                        request.setAttribute("errorCode", "s320050002");
                        request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s141,lang,null));
                        request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s142,lang,null));
                        request.getRequestDispatcher("/index.jsp").forward(request, response);
                        return;
                    }
                    if(adminList.getData()!=null && CollectionUtils.isNotEmpty(adminList.getData().getAdmin())) {
                        boolean isAdmin = false;
                        for(QyweixinGetAdminListRsp.AdminModel adminModel : adminList.getData().getAdmin()) {
                            if(StringUtils.equalsIgnoreCase(adminModel.getUserid(),authUserId)) {
                                isAdmin = true;
                                break;
                            }
                        }
                        if(!isAdmin) {
                            request.setAttribute("errorCode", "s320050002");
                            request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s141,lang,null));
                            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s142,lang,null));
                            request.getRequestDispatcher("/index.jsp").forward(request, response);
                            return;
                        }
                    }
                }
            }
        } else {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s138,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s138,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }
        String json = JSONObject.toJSONString(result.getData());
        String url = ConfigCenter.qywx_scan_code_auth_success_redirect_url+"?lang="+lang+"&code="+URLEncoder.encode(json);
        response.sendRedirect(url);
    }

    /**
     * 查询飞书连接器连接信息
     *
     */
    @RequestMapping(value = "/queryScanCodeAuthUrl", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> queryScanCodeAuthUrl() {
        String lang = TraceUtil.getLocale();
        String fsLang = lang;
        log.info("queryScanCodeAuthUrl,lang={}",lang);
        if(StringUtils.isNotEmpty(lang) && StringUtils.containsIgnoreCase(lang,"zh")) {
            lang = "zh";
        } else {
            lang = "en";
        }
        log.info("queryScanCodeAuthUrl,lang2={}",lang);
        //企微登录授权专用suiteId，这个值不会变
        String appId = "wx867ad65506013329";
        String scanCodeAuthUrl = "https://login.work.weixin.qq.com/wwlogin/sso/login?login_type=ServiceApp&appid={appid}&redirect_uri={redirect_uri}&state={state}&lang={lang}";
        scanCodeAuthUrl = scanCodeAuthUrl.replace("{appid}", appId)
                .replace("{redirect_uri}", URLEncoder.encode(ConfigCenter.qywx_scan_code_auth_redirect_url)+"?lang="+fsLang)
                .replace("{state}", ConfigCenter.crmAppId)
                .replace("{lang}", lang);
        return new Result<>(scanCodeAuthUrl);
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        Map<String, String> cloudDomainEa = new Gson().fromJson(ConfigCenter.CLOUD_DOMAIN_EA, new TypeToken<Map<String, String>>() {
        });
        if(ObjectUtils.isNotEmpty(result) && cloudDomainEa.containsKey(fsEa)) {
            log.info("AuthController.getEnterpriseInfo,old={},new={}", result.getEnterpriseData().getDomain(), cloudDomainEa.get(fsEa));
            result.getEnterpriseData().setDomain(cloudDomainEa.get(fsEa));
        }
        log.info("AuthController.getEnterpriseInfo,result={}",result);
        return result;
    }
}
