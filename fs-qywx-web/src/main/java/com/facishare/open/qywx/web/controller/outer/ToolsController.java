package com.facishare.open.qywx.web.controller.outer;

import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountinner.model.FsAccountModel;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.arg.BatchCreateQywxConnectorArg;
import com.facishare.open.qywx.accountsync.excel.BaseListener;
import com.facishare.open.qywx.accountsync.excel.FileManager;
import com.facishare.open.qywx.accountsync.excel.ReadExcel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinIdToOpenidBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.accountsync.service.SuperAdminService;
import com.facishare.open.qywx.web.arg.MigrateEnterpriseArg;
import com.facishare.open.qywx.web.arg.SelectSqlArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.enums.UserContextSingleton;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.service.SaveMessageService;
import com.facishare.open.qywx.save.vo.QywxMessageVo;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping("/qyweixin/admin/tools/")
// IgnoreI18nFile
public class ToolsController {
    @Autowired
    private ToolsService toolsService;
    @Autowired
    private SaveMessageService saveMessageService;
    @Autowired
    private ContactBindInnerService contactBindInnerService;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;
    @Autowired
    private MessageGeneratingService messageGeneratingService;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private SuperAdminService superAdminService;
    @Resource
    private ProxyHttpClient proxyHttpClient;
    @ReloadableProperty("repAppId")
    private String repAppId;

    @Autowired
    private FsOrderServiceProxy fsOrderServiceProxy;



    private static final String ADMIN_TOOLS_TOKEN = "erpdss_qywx_";
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @RequestMapping(value = "/getAppLicenseInfo")
    public Result<AppLicenseInfo> getAppLicenseInfo(@RequestParam("corpId") String corpId,
                                    @RequestParam(value = "appId",required = false) String appId) {
        return toolsService.getAppLicenseInfo(corpId, appId);
    }

    @RequestMapping(value = "/getUserListInAppVisibleRange")
    public void getUserListInAppVisibleRange(@RequestParam("corpId") String corpId,
                                             @RequestParam(value = "appId",required = false) String appId,
                                             HttpServletResponse response) {
        Result<List<String>> result = toolsService.getUserListInAppVisibleRange(corpId, appId);

        StringBuilder sb = new StringBuilder();
        if(CollectionUtils.isNotEmpty(result.getData())) {
            for(String userId : result.getData()) {
                sb.append(userId+"\r\n");
            }
        }

        //return new Result<>(sb.toString());

        response.setContentType("application/octet-stream");
        response.setHeader("Content-disposition", "attachment;filename=userId.csv");
        try {
            OutputStream os = response.getOutputStream();
            os.write(sb.toString().getBytes(Charset.forName("UTF-8")));
            os.flush();
        } catch (Exception e) {
            log.info("ToolsController.getUserListInAppVisibleRange,exception={}",e.getMessage());
        }
    }

    /**
     * 获取企业微信密文userId
     * @param corpId
     * @param userId
     * @return
     */
    @RequestMapping(value = "/getOpenUserId",method = RequestMethod.GET)
    @ResponseBody
    public Result<String> getOpenUserId(@RequestParam("corpId") String corpId,
                                        @RequestParam(value = "appId",required = false) String appId,
                                        @RequestParam("userId") String userId) {
        return toolsService.userId2OpenUserId2(corpId,appId,userId);
    }

    /**
     * 获取企业微信密文corpId
     * @param corpId
     * @return
     */
    @RequestMapping(value = "/getOpenCorpId",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> getOpenCorpId(@RequestParam("corpId") String corpId) {
        return toolsService.corpId2OpenCorpId(corpId);
    }

//    /**
//     * 刷新员工表
//     * @param corpId
//     * @return
//     */
//    @RequestMapping(value = "/refreshEmployeeTable",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> refreshEmployeeTable(@RequestParam("corpId") String corpId) {
//        return toolsService.refreshEmployeeTable(corpId);
//    }
//
//    /**
//     * 刷新企业绑定表
//     * @param corpId
//     * @return
//     */
//    @RequestMapping(value = "/refreshEnterpriseTable",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> refreshEnterpriseTable(@RequestParam("corpId") String corpId) {
//        return toolsService.refreshEnterpriseTable(corpId);
//    }
//
//    /**
//     * 刷新企业应用绑定表
//     * @param corpId
//     * @return
//     */
//    @RequestMapping(value = "/refreshApplicationTable",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> refreshApplicationTable(@RequestParam("corpId") String corpId) {
//        return toolsService.refreshApplicationTable(corpId);
//    }
//
//    /**
//     * 迁移企业账号，此后企业都是密文的账号
//     * 1.userid与corpid只能同时设置为升级模式，external_userid可以单独升级。
//     * 2.此接口还可为第三方应用设置迁移完成状态，若只传入正确corpid，未传入代开发应用agentid，则视为：更新该企业下同服务商所有第三方应用的对应升级状态
//     * @param corpId 企业的corpId
//     * @param agentId 企业代开发应用id
//     * @return
//     */
//    @RequestMapping(value = "/refreshEnterpriseAccount",method =RequestMethod.GET)
//    @ResponseBody
//    public Result refreshEnterpriseAccount(@RequestParam("corpId") String corpId, @RequestParam("agentId") String agentId) {
//        return toolsService.refreshEnterpriseAccount(corpId, agentId);
//    }
//
//    /**
//     * 刷新部门绑定表
//     * @param corpId
//     * @return
//     */
//    @RequestMapping(value = "/refreshDepartmentTable",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> refreshDepartmentTable(@RequestParam("corpId") String corpId) {
//        return toolsService.refreshDepartmentTable(corpId);
//    }

//    /**
//     * 刷新企业应用信息绑定表
//     * @param corpId
//     * @return
//     */
//    @RequestMapping(value = "/refreshApplicationInfoTable",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> refreshApplicationInfoTable(@RequestParam("corpId") String corpId) {
//        return toolsService.refreshApplicationInfoTable(corpId);
//    }

//    /**
//     * 刷新企业订单绑定表
//     * @param corpId
//     * @return
//     */
//    @RequestMapping(value = "/refreshOrderTable",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> refreshOrderTable(@RequestParam("corpId") String corpId) {
//        return toolsService.refreshOrderTable(corpId);
//    }

    /**
     * 查询企业迁移状态
     * @param corpId
     * @return
     */
    @RequestMapping(value = "/getEnterpriseAccountMigration",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> getEnterpriseAccountMigration(@RequestParam("corpId") String corpId,
                                                      @RequestParam("appId") String appId) {
        return toolsService.getEnterpriseAccountMigration(corpId, appId);
    }

//    /**
//     * 迁移企业账号和刷新企业所有的信息
//     * @param corpIds
//     * @return
//     */
//    @RequestMapping(value = "/refreshAllEnterpriseAccount",method =RequestMethod.POST)
//    @ResponseBody
//    public Result<Map<String, Object>> refreshAllEnterpriseAccount(@RequestBody List<String> corpIds) {
//        return toolsService.refreshAllEnterpriseAccount(corpIds);
//    }

//    /**
//     * 删除外部联系人缓存表
//     * @param corpId
//     * @return
//     */
//    @RequestMapping(value = "/deleteExternalContactTable",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> deleteExternalContactTable(@RequestParam("corpId") String corpId) {
//        return toolsService.deleteExternalContactTable(corpId);
//    }

//    /**
//     * 删除用户缓存表
//     * @param corpId
//     * @return
//     */
//    @RequestMapping(value = "/deleteUserTable",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> deleteUserTable(@RequestParam("corpId") String corpId) {
//        return toolsService.deleteUserTable(corpId);
//    }

//    /**
//     * 服务商刷员工绑定表
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/updateServiceProviderEmployeeBind",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<List<QyweixinAccountEmployeeMapping>> updateServiceProviderEmployeeBind(@RequestParam("fsEa") String fsEa,
//                                                                                          @RequestParam("appId") String appId,
//                                                                                          @RequestParam(value = "outEa", required = false) String outEa) {
//        return toolsService.updateServiceProviderEmployeeBind(fsEa, appId, outEa);
//    }

    /**
     * 会话存档创建预设对象
     * @param
     * @return
     */
    @RequestMapping(value = "/preserveWeChatConversionObj",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.save.result.Result<Boolean> preserveWeChatConversionObj(@RequestParam("ei") Integer ei) {
        return saveMessageService.preserveWeChatConversionObj(ei);
    }

    /**
     * 会话存档创建预设对象
     * @param
     * @return
     */
        @RequestMapping(value = "/getVIPAccountInfoList",method =RequestMethod.GET)
        @ResponseBody
        public Result<List<FsAccountModel>> getVIPAccountInfoList(@RequestParam("bindType") Integer bindType,
                                                                  @RequestParam("accountLevelList") List<String> accountLevelList) {
        return toolsService.getAccountInfo(bindType, accountLevelList);
    }

    /**
     * 迁移mongo库
     * @param
     * @return
     */
    @RequestMapping(value = "/saveMessageToMongo",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.save.result.Result<Void> saveMessageToMongo(@RequestParam("ea") String ea,
                                                                               @RequestParam("startSeq") Long startSeq,
                                                                               @RequestParam("endSeq") Long endSeq) {
        return saveMessageService.saveMessageToMongo(ea, startSeq, endSeq);
    }

    /**
     * 全量迁移mongo库
     * @param
     * @return
     */
    @RequestMapping(value = "/saveAllMessageToMongo",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.save.result.Result<Void> saveAllMessageToMongo() {
        return saveMessageService.saveAllMessageToMongo();
    }

    /**
     * 迁移完mongo，可以把save表里已有的明文转密文成功的账号进行保存
     * @param
     * @return
     */
    @RequestMapping(value = "/saveMessageAllIds",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.save.result.Result<Void> saveMessageAllIds() {
        return saveMessageService.saveMessageAllIds();
    }

    /**
     * 删除mongo表的所有数据，请谨慎使用
     * @param
     * @return
     */
    @RequestMapping(value = "/deleteMongoTableData",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.save.result.Result<Long> deleteMongoTableData(@RequestParam("ea") String ea) {
        return saveMessageService.deleteMongoTableData(ea);
    }

    /**
     * 自动绑定账号
     * @param
     * @return
     */
    @RequestMapping(value = "/autoBindEmpAccount",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> autoBindEmpAccount(@RequestParam("ea") String ea,
                                           @RequestParam(value = "corpId") String corpId,
                                           @RequestParam(value = "appId") String appId) {
        return contactBindInnerService.autoBindEmpAccount(ea, corpId, appId);
    }

    /**
     * 根据id查询mongo数据
     * @param
     * @return
     */
    @RequestMapping(value = "/getMongoDataById",method =RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.qywx.save.result.Result<QywxMessageVo> getMongoDataById(@RequestParam("ea") String ea, @RequestParam("id") String id) {
        return saveMessageService.getMongoDataById(ea, id);
    }

    /**
     * 更新mongo数据
     * @param
     * @return
     */
    @RequestMapping(value = "/updateMongoData",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.save.result.Result<Integer> updateMongoData(@RequestParam("ea") String ea, @RequestParam("id") String id, @RequestBody QywxMessageVo vo) {
        return saveMessageService.updateMongoData(ea, id, vo);
    }

    /**
     * 插入mongo数据
     * @param
     * @return
     */
    @RequestMapping(value = "/insertMongoData",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.save.result.Result<Integer> insertMongoData(@RequestParam("ea") String ea, @RequestBody QywxMessageVo vo) {
        return saveMessageService.insertMongoData(ea, vo);
    }

//    /**
//     * 查询全部状态下的员工绑定关系
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/getUserBindInfo",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<List<QyweixinAccountEmployeeMapping>> getUserBindInfo(@RequestParam("corpId") String corpId, @RequestParam("outUserId") String outUserId,
//                                                                        @RequestParam(value = "fsEa", required = false) String fsEa) {
//        if(StringUtils.isEmpty(corpId) || StringUtils.isEmpty(outUserId)) {
//            return Result.newInstance(ErrorRefer.REQUIRED_PARAMETER);
//        }
//        return qyweixinAccountBindInnerService.getEmployeeMapping(corpId,
//                outUserId, -1, fsEa);
//    }

    /**
     * 检查CRM用户是否停用
     * @param
     * @return
     */
    @RequestMapping(value = "/getCRMUserStatus",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> getCRMUserStatus(@RequestParam("fsEa") String fsEa, @RequestParam("fsUserId") Integer fsUserId) {
        if(StringUtils.isEmpty(fsEa) || fsUserId == null ) {
            return Result.newInstance(ErrorRefer.REQUIRED_PARAMETER);
        }
        Result<List<EmployeeDto>> fsEmpUserResult = qyweixinAccountSyncService.getFsEmpUser(fsEa, 1000, Lists.newArrayList(fsUserId));
        if(!fsEmpUserResult.isSuccess() || ObjectUtils.isEmpty(fsEmpUserResult.getData())) {
            return Result.newInstance(ErrorRefer.QUERRY_EMPTY);
        }
        return new Result<>(fsEmpUserResult.getData().get(0).getStatus().toString());
    }

    /**
     * 检查企业微信应用绑定信息
     * 1、查库
     * 2、查询应用信息
     * @param
     * @return
     */
    @RequestMapping(value = "/getQYWXAppBindInfo",method =RequestMethod.GET)
    @ResponseBody
    public Result<QyweixinAppStatusInfo> getQYWXAppBindInfo(@RequestParam("corpId") String corpId, @RequestParam("appType") Integer appType) {
        if(StringUtils.isEmpty(corpId) || appType == null) {
            return Result.newInstance(ErrorRefer.REQUIRED_PARAMETER);
        }
        String appId;
        if(appType == 1) {
            appId = ConfigCenter.crmAppId;
        } else {
            appId = repAppId;
        }
        return toolsService.getQYWXAppBindInfo(corpId, appId);
    }

    /**
     * 检查企业微信代开发应用授权信息
     * @param
     * @return
     */
    @RequestMapping(value = "/getQYWXRepAppAuthorityInfo",method =RequestMethod.GET)
    @ResponseBody
    public Result<QyweixinAppAuthorityInfo> getQYWXRepAppAuthorityInfo(@RequestParam("corpId") String corpId) {
        if(StringUtils.isEmpty(corpId)) {
            return Result.newInstance(ErrorRefer.REQUIRED_PARAMETER);
        }
        return toolsService.getQYWXRepAppAuthorityInfo(corpId);
    }

    /**
     * 看crm客户是否已开通
     * @param
     * @return
     */
    @RequestMapping(value = "/queryCrmAccountObjStatus",method =RequestMethod.GET)
    @ResponseBody
    public Result<AccountObjInfo> queryCrmAccountObjStatus(@RequestParam("corpId") String corpId) {
        if(StringUtils.isEmpty(corpId)) {
            return Result.newInstance(ErrorRefer.REQUIRED_PARAMETER);
        }
        return toolsService.queryCrmAccountObjStatus(corpId);
    }

    /**
     * 查看下crm订单是否成功
     * @param
     * @return
     */
    @RequestMapping(value = "/queryCrmSalesOrderObjStatus",method =RequestMethod.GET)
    @ResponseBody
    public Result<SalesOrderObjInfo> queryCrmSalesOrderObjStatus(@RequestParam("orderId") String orderId) {
        if(StringUtils.isEmpty(orderId)) {
            return Result.newInstance(ErrorRefer.REQUIRED_PARAMETER);
        }
        return toolsService.queryCrmSalesOrderObjStatus(orderId);
    }

    /**
     * 查看企业状态
     * @param
     * @return
     */
    @RequestMapping(value = "/getEnterpriseRunStatus",method =RequestMethod.GET)
    @ResponseBody
    public Result<Integer> getEnterpriseRunStatus(@RequestParam("fsEa") String fsEa) {
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.REQUIRED_PARAMETER);
        }
        return toolsService.getEnterpriseRunStatus(fsEa);
    }

    /**
     * 查看企微绑定的纷享ea详情
     * @param
     * @return
     */
    @RequestMapping(value = "/getFsEaList",method =RequestMethod.GET)
    @ResponseBody
    public Result<List<String>> getFsEaList(@RequestParam("corpId") String corpId) {
        if(StringUtils.isEmpty(corpId)) {
            return Result.newInstance(ErrorRefer.REQUIRED_PARAMETER);
        }
        return qyweixinAccountBindInnerService.getFsEaList(corpId);
    }

    /**
     * 企微和crm解绑
     * @param
     * @return
     */
    @RequestMapping(value = "/unbind",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> unbind(@RequestParam(value = "corpId") String corpId,
                                       @RequestParam(value = "fsEa") String fsEa,
                                       @RequestParam(value = "appId") String appId) {
        if(StringUtils.isEmpty(corpId) && StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.REQUIRED_PARAMETER);
        }
        return toolsService.unbind(corpId, fsEa, appId);
    }

    /**
     * 会话信息刷库
     * @param
     * @return
     */
    @RequestMapping(value = "/updateCorpSetting",method =RequestMethod.POST)
    @ResponseBody
    public com.facishare.open.qywx.save.result.Result<Void> updateCorpSetting() {
        return messageGeneratingService.updateCorpSetting();
    }

//    /**
//     * 应用信息刷库
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/updateAllCorpBind",method =RequestMethod.POST)
//    @ResponseBody
//    public Result<Void> updateAllCorpBind() {
//        return toolsService.updateAllCorpBind();
//    }

//    @RequestMapping(value = "/updateOpenIds", method = RequestMethod.POST)
//    public Result<Void> updateOpenIds(@RequestParam(value = "sheetName", required = false) String sheetName, MultipartFile file) {
//        byte[] bytes = new byte[0];
//        try {
//            bytes = file.getBytes();
//            InputStream inputStream = new ByteArrayInputStream(bytes);
//            ReadExcel.Arg<Map<Integer, String>> arg = new ReadExcel.Arg<>();
//            BaseListener<Map<Integer, String>> listen = new BaseListener<Map<Integer, String>>() {
//            };
//            arg.setExcelListener(listen);
//            arg.setInputStream(inputStream);
//            if(StringUtils.isNotEmpty(sheetName)) {
//                arg.setSheetName(sheetName);
//            }
//            fileManager.readExcelBySheetName(arg);
//            if (CollectionUtils.isEmpty(listen.getDataList())) {
//                log.info("ToolsController.updateOpenIds,listen is emp.");
//                return new Result<>();
//            }
//            List<QyweixinIdToOpenidBo> qyweixinIdToOpenidBos = listen.getDataList().stream().map(v -> {
//                QyweixinIdToOpenidBo qyweixinIdToOpenidBo = new QyweixinIdToOpenidBo();
//                qyweixinIdToOpenidBo.setCorpId(v.get(0));
//                qyweixinIdToOpenidBo.setOpenid(v.get(1));
//                qyweixinIdToOpenidBo.setPlaintextId(v.get(2));
//                return qyweixinIdToOpenidBo;
//            }).collect(Collectors.toList());
//            log.info("ToolsController.updateOpenIds,qyweixinIdToOpenidBos={}.", qyweixinIdToOpenidBos);
//            toolsService.updateOpenIds(qyweixinIdToOpenidBos);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return new Result<>();
//    }

//    /**
//     * 应用信息刷库
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/updateAllCorpBindToCopy",method =RequestMethod.POST)
//    @ResponseBody
//    public Result<Void> updateAllCorpBindToCopy(@RequestParam Integer copyDirection) {
//        return toolsService.updateAllCorpBindToCopy(copyDirection);
//    }

    @RequestMapping(value = "/deleteUserInfo",method =RequestMethod.GET)
    @ResponseBody
    public Result<Long> deleteUserInfo(@RequestParam String outEa,
                                       @RequestParam Boolean isDelete) {
        return toolsService.deleteUserInfo(outEa, isDelete);
    }

    @RequestMapping(value = "/updateUserAndDepartmentInfo",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> updateUserAndDepartmentInfo(@RequestParam String outEa, @RequestParam String appId) {
        return qyweixinAccountSyncService.updateUserAndDepartmentInfo(outEa, appId);
    }

    /**
     * 企微创建纷享企业情况
     * @param
     * @return
     */
    @RequestMapping(value = "/queryFsEnterpriseOpen",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryFsEnterpriseOpen(@RequestParam String outEa) {
        return toolsService.queryFsEnterpriseOpen(outEa);
    }

    /**
     * 企微创建纷享人员情况
     * @param
     * @return
     */
    @RequestMapping(value = "/queryFsEmployeeOpen",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryFsEmployeeOpen(@RequestParam String outEa, @RequestParam String outUserId) {
        return toolsService.queryFsEmployeeOpen(outEa, outUserId);
    }

    /**
     * 查询企业的绑定类型
     * @param
     * @return
     */
    @RequestMapping(value = "/queryEnterpriseBindType",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryEnterpriseBindType(@RequestParam String fsEa,
                                                  @RequestParam(value = "outEa", required = false) String outEa) {
        return toolsService.queryEnterpriseBindType(fsEa,outEa);
    }

    /**
     * 查询纷享人员当前的状态
     * @param
     * @return
     */
    @RequestMapping(value = "/queryFsEmployeeStatus",method =RequestMethod.GET)
    @ResponseBody
    public Result<String> queryFsEmployeeStatus(@RequestParam String outEa, @RequestParam String outUserId) {
        return toolsService.queryFsEmployeeStatus(outEa, outUserId);
    }

//    /**
//     * 删除重复人员
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/dealRepeatEmployees",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Void> dealRepeatEmployees() {
//        return toolsService.dealRepeatEmployees();
//    }

//    /**
//     * 查询重复人员
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/queryRepeatEmployees",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Void> queryRepeatEmployees() {
//        return toolsService.queryRepeatEmployees();
//    }

//    /**
//     * 删除重复人员
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/stopEmployee",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Void> stopEmployee(@RequestParam Integer ei,
//                                     @RequestParam String userId,
//                                     @RequestParam(required = false) String outEa) {
//        return toolsService.stopEmployee(ei, userId, outEa);
//    }

//    /**
//     * 处理重复人员
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/dealEmpData",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<String> dealEmpData(@RequestParam String ei,
//                                      @RequestParam String userId,
//                                      @RequestParam String newUserId,
//                                      @RequestParam(required = false) String outEa) {
//        toolsService.dealEmpData(Integer.valueOf(ei), userId, newUserId, outEa);
//        return new Result<>("执行中，请留意企信通知");
//    }

//    /**
//     * 部门关系
//     * @param
//     * @return
//     */
//    @RequestMapping(value = "/updateDeptBindStatus",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> updateDeptBindStatus(@RequestParam String fsEa, @RequestParam String fsDeptId, @RequestParam Integer status, @RequestParam String appId) {
//        return toolsService.updateDeptBindStatus(fsEa, fsDeptId, status, appId);
//    }


//    @RequestMapping(value = "/pushCorpBindData2Cloud",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Void> pushCorpBindData2Cloud(@RequestParam String domain) {
//        return toolsService.pushCorpBindData2Cloud(domain);
//    }

//    @RequestMapping(value = "/pushEnterpriseData2Cloud",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Void> pushEnterpriseData2Cloud(@RequestParam String fsEa, @RequestParam String domain) {
//        return toolsService.pushEnterpriseData2Cloud(fsEa, domain);
//    }

//    @RequestMapping(value = "/updateEnterpriseDomain",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Integer> updateEnterpriseDomain(@RequestParam String fsEa, @RequestParam String domain) {
//        return toolsService.updateEnterpriseDomain(fsEa, domain);
//    }

//    @RequestMapping(value = "/pushCorpInfoData2Cloud",method =RequestMethod.GET)
//    @ResponseBody
//    public Result<Void> pushCorpInfoData2Cloud(@RequestParam String domain) {
//        return toolsService.pushCorpInfoData2Cloud(domain);
//    }

    @RequestMapping(value = "/selectSql",method =RequestMethod.POST)
    @ResponseBody
    public Result<List<Map<String, Object>>> selectSql(@RequestBody SelectSqlArg arg) {
        Result<Void> result = verifyUser(arg);
        if(!result.isSuccess()) {
            return new Result<>(result.getErrorCode(),result.getErrorMsg(),null);
        }

        return superAdminService.superQuerySql(arg.getSql());
    }

    @RequestMapping(value = "/updateSql",method =RequestMethod.POST)
    @ResponseBody
    public Result<Integer> updateSql(@RequestBody SelectSqlArg arg) {
        Result<Void> result = verifyUser(arg);
        if(!result.isSuccess()) {
            return new Result<>(result.getErrorCode(),result.getErrorMsg(),null);
        }

        return new Result<>(outerOaEnterpriseBindManager.superUpdateData(arg.getSql()));
    }

    @RequestMapping(value = "/updateMySql",method =RequestMethod.POST)
    @ResponseBody
    public Result<Integer> updateMySql(@RequestBody SelectSqlArg arg) {
        Result<Void> result = verifyUser(arg);
        if(!result.isSuccess()) {
            return new Result<>(result.getErrorCode(),result.getErrorMsg(),null);
        }

        return superAdminService.superUpdateSql(arg.getSql());
    }

    @RequestMapping(value = "/batchCreateQywxConnector",method =RequestMethod.POST)
    @ResponseBody
    public Result<String> batchCreateQywxConnector(@RequestBody BatchCreateQywxConnectorArg arg,
                                                   HttpServletRequest request) {
        log.info("batchCreateQywxConnector,arg={}",arg);
        if(arg==null || CollectionUtils.isEmpty(arg.getBindList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.batchCreateQywxConnector(arg);
    }
    /**
     * 触发企业微信订单
     * @param
     * @return
     */
    @RequestMapping(value = "/completeOrder",method =RequestMethod.GET)
    @ResponseBody
    public Result<Integer> completeOrder(@RequestParam String orderId) {
        return superAdminService.completeOrder(orderId);
    }
    private Result<Void> verifyUser(SelectSqlArg arg) {
        if(UserContextSingleton.INSTANCE.getUserContext()==null) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        if(arg==null || StringUtils.isEmpty(arg.getToken()) || StringUtils.isEmpty(arg.getSql())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        if(!StringUtils.startsWith(arg.getToken(),ADMIN_TOOLS_TOKEN)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String timestamp = StringUtils.replaceIgnoreCase(arg.getToken(),ADMIN_TOOLS_TOKEN,"");
        long startTime = 0;
        try {
            startTime = Long.valueOf(timestamp);
        } catch (Exception e) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        long offset = System.currentTimeMillis() - startTime;
        if(offset > 5 * 60 * 1000) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return new Result<>();
    }

    @RequestMapping(value = "/createCrmCustomer2",method =RequestMethod.POST)
    @ResponseBody
    public Result<Void> createCrmCustomer2(@RequestBody CreateCustomerArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<String> result = fsOrderServiceProxy.createCustomer2(arg);
        return new Result<>();
    }

    @RequestMapping(value = "/createCrmOrder2",method =RequestMethod.POST)
    @ResponseBody
    public Result<Void> createCrmOrder2(@RequestBody CreateCrmOrderArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder2(arg);
        return new Result<>();
    }

    @RequestMapping(value = "/createCrmCustomer",method =RequestMethod.POST)
    @ResponseBody
    public Result<Void> createCrmCustomer(@RequestBody CreateCustomerArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCustomer(arg);
        return new Result<>();
    }

    @RequestMapping(value = "/createCrmOrder",method =RequestMethod.POST)
    @ResponseBody
    public Result<Void> createCrmOrder(@RequestBody CreateCrmOrderArg arg) {
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder(arg);
        return new Result<>();
    }

    /**
     * 迁移订单信息
     * @return 结果
     */
    @RequestMapping(value = "/migrateOrderInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateOrderInfo(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        if (CollectionUtils.isEmpty(migrateEnterpriseArg.getOutEaList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.migrateOrderInfo(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 迁移业务信息绑定
     * @return 结果
     */
    @RequestMapping(value = "/migrateBusinessInfoBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateBusinessInfoBind(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        if (CollectionUtils.isEmpty(migrateEnterpriseArg.getOutEaList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.migrateBusinessInfoBind(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 迁移企业微信ID到OpenID
     * @return 结果
     */
    @RequestMapping(value = "/migrateQyweixinIdToOpenid", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateQyweixinIdToOpenid(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        if (CollectionUtils.isEmpty(migrateEnterpriseArg.getOutEaList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.migrateQyweixinIdToOpenid(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 迁移文件信息
     * @return 结果
     */
    @RequestMapping(value = "/migrateFileInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateFileInfo(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        if (CollectionUtils.isEmpty(migrateEnterpriseArg.getOutEaList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.migrateFileInfo(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 迁移外部联系人
     * @return 结果
     */
    @RequestMapping(value = "/migrateExternalContacts", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateExternalContacts(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        if (CollectionUtils.isEmpty(migrateEnterpriseArg.getOutEaList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.migrateExternalContacts(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 迁移应用绑定信息
     * @return 结果
     */
    @RequestMapping(value = "/migrateAppBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateAppBind(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        if (CollectionUtils.isEmpty(migrateEnterpriseArg.getOutEaList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.migrateAppBind(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 迁移员工绑定信息
     * @return 结果
     */
    @RequestMapping(value = "/migrateEmpBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateEmpBind(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        if (CollectionUtils.isEmpty(migrateEnterpriseArg.getOutEaList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.migrateEmpBind(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 迁移部门绑定信息
     * @return 结果
     */
    @RequestMapping(value = "/migrateDeptBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateDeptBind(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        if (CollectionUtils.isEmpty(migrateEnterpriseArg.getOutEaList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.migrateDeptBind(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 迁移配置信息
     * @return 结果
     */
    @RequestMapping(value = "/migrateConfigInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateConfigInfo(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        if (CollectionUtils.isEmpty(migrateEnterpriseArg.getOutEaList())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return toolsService.migrateConfigInfo(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 迁移企业绑定信息
     * @return 结果
     */
    @RequestMapping(value = "/migrateEnterpriseBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateEnterpriseBind(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        return toolsService.migrateEnterpriseBind(migrateEnterpriseArg.getOutEaList(), migrateEnterpriseArg.getStartTime(), migrateEnterpriseArg.getEndTime(), migrateEnterpriseArg.getFinalAppId());
    }

    //通过fsEa和outEa删除绑定关系
    @RequestMapping(value = "/deleteQYWXAccountBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Integer> deleteQYWXAccountBind(@RequestParam String outEa, @RequestParam String fsEa) {

        return toolsService.deleteQYWXAccountBind(outEa, fsEa);
    }

    /**
     * 迁移企业绑定信息
     * @return 结果
     */
    @RequestMapping(value = "/migrateOnlyEnterpriseBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> migrateOnlyEnterpriseBind(@RequestBody MigrateEnterpriseArg migrateEnterpriseArg) {
        return toolsService.migrateOnlyEnterpriseBind(migrateEnterpriseArg.getOutEaList());
    }

    /**
     * 删除企业绑定信息
     * @return 结果
     */
    @RequestMapping(value = "/cleanNewEnterpriseBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> cleanNewEnterpriseBind(@RequestParam("fsEa") String fsEa,
                                               @RequestParam("outEa") String outEa,
                                               @RequestParam("appId") String appId) {
        return toolsService.cleanNewEnterpriseBind(outEa, appId, fsEa);
    }

    /**
     * 删除企业绑定信息
     * @return 结果
     */
    @RequestMapping(value = "/fixEnterpriseManualBindInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> fixEnterpriseManualBindInfo() {
        return toolsService.fixEnterpriseManualBindInfo();
    }
}
