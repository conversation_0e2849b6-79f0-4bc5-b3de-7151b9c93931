package com.facishare.open.qywx.web.controller.inner;

import com.alibaba.fastjson.JSON;
import com.facishare.enterprise.event.EnterpriseAddEvent;
import com.facishare.enterprise.event.EnterpriseEventType;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.ProtoUtil;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.web.arg.BatchGetEmployeeInfoArg;
import com.facishare.open.qywx.web.arg.BatchGetEmployeeInfoArg2;
import com.facishare.open.qywx.web.arg.BatchGetOpenIdArg;
import com.facishare.open.qywx.web.arg.GetDepartmentInfoArg;
import com.facishare.open.qywx.web.model.result.Result;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ControllerInnerRestProxyTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private ControllerInnerRestProxy controllerInnerRestProxy;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ExternalMsgServiceImpl.getEnterpriseInfo,result={}",result);
        return result;
    }

    @Test
    public void outAccountToFsAccountBatchTest() throws Exception {
        GetEnterpriseDataResult enterpriseInfo = getEnterpriseInfo("84883");
        log.info("ControllerInnerRestProxyTest.outAccountToFsAccountBatchTest,enterpriseInfo={}", JSON.toJSONString(enterpriseInfo));

        Result<Map<String, String>> mapResult = controllerInnerRestProxy.outAccountToFsAccountBatch("lgg6737", "wx88a141937dd6f838", Lists.newArrayList("wowx1mDAAADaR4zU0tv72vm-358jDT1w", "wowx1mDAAASctW3aJu0c8S1nwzqdmrfw"));
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(mapResult));
    }

    @Test
    public void fsAccountToOutAccountBatchTest() throws Exception {
        Result<Map<String, String>> mapResult = controllerInnerRestProxy.fsAccountToOutAccountBatch( "wx88a141937dd6f838", Lists.newArrayList("E.lgg6737.1000", "E.lgg6737.1004"));
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(mapResult));
    }

    @Test
    public void fsAccountToOutAccountBatch2Test() throws Exception {
        Result<List<EmployeeAccountMatchResult>> mapResult = controllerInnerRestProxy.fsAccountToOutAccountBatch2( "wx88a141937dd6f838", Lists.newArrayList("E.lgg6737.1000", "E.lgg6737.1004"), "wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q");
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(mapResult));
    }

    @Test
    public void outEaToFsEaTest() throws Exception {
        Result<String> mapResult = controllerInnerRestProxy.outEaToFsEa("wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q");
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(mapResult));
    }

    @Test
    public void fsEaToOutEaTest() throws Exception {
        Result<String> mapResult = controllerInnerRestProxy.fsEaToOutEa("lgg6737");
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(mapResult));
    }

    @Test
    public void fsEaToOutEaListTest() throws Exception {
        Result<List<String>> mapResult = controllerInnerRestProxy.fsEaToOutEaList("lgg6737");
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(mapResult));
    }

    @Test
    public void getQywxUserAccountTest() throws Exception {
        Result<QyweixinAccountEmployeeMapping> mapResult = controllerInnerRestProxy.getQywxUserAccount("lgg6737", "1000", null);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(mapResult));
    }

    @Test
    public void getExternalUserIdTest() throws Exception {
        com.facishare.open.qywx.accountsync.result.Result<String> externalUserId = controllerInnerRestProxy.getExternalUserId("wmwx1mDAAAULGU7ppBrfLmfn8dqwt7Ww");
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void externalContactTransferCustomerTest() throws Exception {
        QyweixinTransferCustomerInfo qyweixinTransferCustomerInfo = new QyweixinTransferCustomerInfo();
        qyweixinTransferCustomerInfo.setEa("lgg6737");
        qyweixinTransferCustomerInfo.setExternalUserId(Lists.newArrayList("wowx1mDAAADaR4zU0tv72vm-358jDT1w"));
        qyweixinTransferCustomerInfo.setHandoverUserId("1000");
        qyweixinTransferCustomerInfo.setTakeoverUserId("1004");
        qyweixinTransferCustomerInfo.setTransferSuccessMsg("转接成功");
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinTransferCustomerResult>> listResult = controllerInnerRestProxy.externalContactTransferCustomer(qyweixinTransferCustomerInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(listResult));
    }

    @Test
    public void externalContactTransferResultTest() throws Exception {
        QyweixinTransferCustomerStatusInfo qyweixinTransferCustomerStatusInfo = new QyweixinTransferCustomerStatusInfo();
        qyweixinTransferCustomerStatusInfo.setEa("lgg6737");
        qyweixinTransferCustomerStatusInfo.setHandoverUserId("1000");
        qyweixinTransferCustomerStatusInfo.setTakeoverUserId("1004");
        qyweixinTransferCustomerStatusInfo.setCursor("wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q@wx88a141937dd6f838@");
        com.facishare.open.qywx.accountsync.result.Result<QyweixinTransferCustomerStatusResult> externalUserId = controllerInnerRestProxy.externalContactTransferResult(qyweixinTransferCustomerStatusInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void externalContactTransferGroupChatTest() throws Exception {
        QyweixinTransferGroupChatInfo qyweixinTransferGroupChatInfo = new QyweixinTransferGroupChatInfo();
        qyweixinTransferGroupChatInfo.setEa("lgg6737");
        qyweixinTransferGroupChatInfo.setChatIdList(Lists.newArrayList("wowx1mDAAADaR4zU0tv72vm-358jDT1w"));
        qyweixinTransferGroupChatInfo.setNewOwner("1004");
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinTransferGroupChatResult>> externalUserId = controllerInnerRestProxy.externalContactTransferGroupChat(qyweixinTransferGroupChatInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void unassignedExternalContactTest() throws Exception {
        QyweixinUnassignedExternalContactInfo qyweixinUnassignedExternalContactInfo = new QyweixinUnassignedExternalContactInfo();
        qyweixinUnassignedExternalContactInfo.setEa("lgg6737");
        com.facishare.open.qywx.accountsync.result.Result<QyweixinUnassignedExternalContactResult> externalUserId = controllerInnerRestProxy.unassignedExternalContact(qyweixinUnassignedExternalContactInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void resignedTransferCustomerTest() throws Exception {
        QyweixinTransferCustomerInfo qyweixinUnassignedExternalContactInfo = new QyweixinTransferCustomerInfo();
        qyweixinUnassignedExternalContactInfo.setEa("lgg6737");
        qyweixinUnassignedExternalContactInfo.setExternalUserId(Lists.newArrayList("wowx1mDAAADaR4zU0tv72vm-358jDT1w"));
        qyweixinUnassignedExternalContactInfo.setHandoverUserId("1000");
        qyweixinUnassignedExternalContactInfo.setTakeoverUserId("1004");
        qyweixinUnassignedExternalContactInfo.setTransferSuccessMsg("转接成功");
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinTransferCustomerResult>> externalUserId = controllerInnerRestProxy.resignedTransferCustomer(qyweixinUnassignedExternalContactInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void resignedTransferResultTest() throws Exception {
        QyweixinTransferCustomerStatusInfo qyweixinUnassignedExternalContactInfo = new QyweixinTransferCustomerStatusInfo();
        qyweixinUnassignedExternalContactInfo.setEa("lgg6737");
        qyweixinUnassignedExternalContactInfo.setHandoverUserId("1000");
        qyweixinUnassignedExternalContactInfo.setTakeoverUserId("1004");
        com.facishare.open.qywx.accountsync.result.Result<QyweixinTransferCustomerStatusResult> externalUserId = controllerInnerRestProxy.resignedTransferResult(qyweixinUnassignedExternalContactInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void resignedTransferGroupChatTest() throws Exception {
        QyweixinTransferGroupChatInfo qyweixinUnassignedExternalContactInfo = new QyweixinTransferGroupChatInfo();
        qyweixinUnassignedExternalContactInfo.setEa("lgg6737");
        qyweixinUnassignedExternalContactInfo.setChatIdList(Lists.newArrayList("wowx1mDAAADaR4zU0tv72vm-358jDT1w"));
        qyweixinUnassignedExternalContactInfo.setNewOwner("1004");
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinTransferGroupChatResult>> externalUserId = controllerInnerRestProxy.resignedTransferGroupChat(qyweixinUnassignedExternalContactInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void GetGroupChatTest() throws Exception {
        QyweixinGroupChatInfo qyweixinGroupChatInfo = new QyweixinGroupChatInfo();
        qyweixinGroupChatInfo.setEa("84883");
        qyweixinGroupChatInfo.setStatusFilter(0);
        QyweixinGroupChatInfo.QyweixinOwnerFilter qyweixinOwnerFilter = new QyweixinGroupChatInfo.QyweixinOwnerFilter();
//        qyweixinOwnerFilter.setUserIdList(Lists.newArrayList("1000"));
        qyweixinGroupChatInfo.setOwnerFilter(qyweixinOwnerFilter);
        qyweixinGroupChatInfo.setCursor("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA@wx88a141937dd6f838@lNS13ngnXbHv-NjlkiFlcak7gqr43eSPzONbpZx9U0E");
        com.facishare.open.qywx.accountsync.result.Result<QyweixinGroupChatResult> externalUserId = controllerInnerRestProxy.GetGroupChat(qyweixinGroupChatInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void GetGroupChatListTest() throws Exception {
        QyweixinGroupChatInfo qyweixinGroupChatInfo = new QyweixinGroupChatInfo();
        qyweixinGroupChatInfo.setEa("lgg6737");
        qyweixinGroupChatInfo.setStatusFilter(0);
        QyweixinGroupChatInfo.QyweixinOwnerFilter qyweixinOwnerFilter = new QyweixinGroupChatInfo.QyweixinOwnerFilter();
//        qyweixinOwnerFilter.setUserIdList(Lists.newArrayList("1000"));
        qyweixinGroupChatInfo.setOwnerFilter(qyweixinOwnerFilter);
        com.facishare.open.qywx.accountsync.result.Result<GroupChatListResult> externalUserId = controllerInnerRestProxy.GetGroupChatList(qyweixinGroupChatInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void batchGetEmployeeInfoTest() throws Exception {
        BatchGetEmployeeInfoArg qyweixinGroupChatInfo = new BatchGetEmployeeInfoArg();
        qyweixinGroupChatInfo.setCorpId("wpwx1mDAAAOvtbJxYM8CScaAVqKJlC-Q");
        qyweixinGroupChatInfo.setUserIdList(Lists.newArrayList("wowx1mDAAADaR4zU0tv72vm-358jDT1w"));
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfo>> externalUserId = controllerInnerRestProxy.batchGetEmployeeInfo(qyweixinGroupChatInfo);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(externalUserId));
    }

    @Test
    public void batchGetEmployeeMappingTest() throws Exception {
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result = controllerInnerRestProxy.batchGetEmployeeMapping("lgg6737", null, null);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(result));
    }

    @Test
    public void getOpenIdsTest() throws Exception {
        BatchGetOpenIdArg batchGetOpenIdArg = new BatchGetOpenIdArg();
        batchGetOpenIdArg.setFsEa("lgg6737");
        batchGetOpenIdArg.setOpenids(Lists.newArrayList("2311"));
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinGetOpenIdResult>> openIds = controllerInnerRestProxy.getOpenIds(batchGetOpenIdArg);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(openIds));
    }

    @Test
    public void batchGetEmployeeInfo2Test() throws Exception {
        BatchGetEmployeeInfoArg2 batchGetOpenIdArg = new BatchGetEmployeeInfoArg2();
        batchGetOpenIdArg.setFsEa("lgg6737");
        batchGetOpenIdArg.setUserIds(Lists.newArrayList("1000"));
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinEmployeeInfoResult>> openIds = controllerInnerRestProxy.batchGetEmployeeInfo2(batchGetOpenIdArg);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(openIds));
    }

    @Test
    public void getDepartmentInfoTest() throws Exception {
        GetDepartmentInfoArg batchGetOpenIdArg = new GetDepartmentInfoArg();
        batchGetOpenIdArg.setFsEa("lgg6737");
        batchGetOpenIdArg.setDepartmentId("1");
        com.facishare.open.qywx.accountsync.result.Result<QyweixinDepartmentInfo> openIds = controllerInnerRestProxy.getDepartmentInfo(batchGetOpenIdArg);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(openIds));
    }

    @Test
    public void getExternalDetailTest() throws Exception {
        com.facishare.open.qywx.accountsync.result.Result<QyweixinExternalContactRsp> openIds = controllerInnerRestProxy.getExternalDetail("84883", "wmwx1mDAAAY8GeONLnAsySl13hkQ_E5A", null);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(openIds));
    }

    @Test
    public void getExternalChatTest() throws Exception {
        com.facishare.open.qywx.accountsync.result.Result<QyweixinGroupChatDetail> openIds = controllerInnerRestProxy.getExternalChat("84883", "wrQZ1uJQAAeB0vd1_g3uaTB4JvuTs_xA", null);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(openIds));
    }

    @Test
    public void getExternalContactListTest() throws Exception {
        com.facishare.open.qywx.accountsync.result.Result<List<String>> openIds = controllerInnerRestProxy.getExternalContactList("lgg6737", "1000", null);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(openIds));
    }

    @Test
    public void getExternalContactList2Test() throws Exception {
        com.facishare.open.qywx.accountsync.result.Result<List<String>> openIds = controllerInnerRestProxy.getExternalContactList2("84883", "wowx1mDAAAfPMspC9iKzWLx16d_zjcRA", null);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(openIds));
    }

    @Test
    public void switchExternalContactEmployeeIdTest() throws Exception {
        QyweixinOldExternalUserIdInfo qyweixinOldExternalUserIdInfos = new QyweixinOldExternalUserIdInfo();
        qyweixinOldExternalUserIdInfos.setEa("lgg6737");
        qyweixinOldExternalUserIdInfos.setExternalUserIds(Lists.newArrayList("wmwx1mDAAAULGU7ppBrfLmfn8dqwt7Ww"));
        com.facishare.open.qywx.accountsync.result.Result<List<QyweixinExternalUserIdInfo>> openIds = controllerInnerRestProxy.switchExternalContactEmployeeId(qyweixinOldExternalUserIdInfos);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(openIds));
    }

    @Test
    public void insertQywxAppInfoTest() throws Exception {
        List<QyweixinBusinessBindInfo> qyweixinBusinessBindInfos = Lists.newArrayList();
        QyweixinBusinessBindInfo qyweixinBusinessBindInfo = new QyweixinBusinessBindInfo();
        qyweixinBusinessBindInfo.setAppId("wx0f0f0f0f0f0f0f0f");
        qyweixinBusinessBindInfo.setBusinessType("1");
        qyweixinBusinessBindInfo.setFsEa("lgg6737");
        qyweixinBusinessBindInfo.setOutEa("wowx1mDAAADaR4zU0tv72vm-358jDT1w");
        qyweixinBusinessBindInfo.setStatus(1);
        qyweixinBusinessBindInfos.add(qyweixinBusinessBindInfo);
        com.facishare.open.qywx.accountsync.result.Result<Void> openIds = controllerInnerRestProxy.insertQywxAppInfo(qyweixinBusinessBindInfos);
        log.info("outAccountToFsAccountBatchTest result:{}", JSON.toJSONString(openIds));
    }
}
