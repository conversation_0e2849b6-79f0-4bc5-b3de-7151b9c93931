package com.facishare.open.qywx.web.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.job.AutoGetUserAndDepartmentInfoHandler;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ToolsServiceTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private ToolsService toolsService;

    @Test
    public void test() throws Exception {
        toolsService.migrateEnterpriseBind(Lists.newArrayList("ww8a8f8c29ecf9970f"), null, null, "dktestnnnnkkkkddd");
    }
    @Test
    public void test1() throws Exception {
        toolsService.migrateEmpBind(Lists.newArrayList("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"));
    }

    @Test
    public void test2() throws Exception {
        toolsService.migrateDeptBind(Lists.newArrayList("wpwx1mDAAAuAKnjk69jwNHKb2mjOi1Xg"));
    }

    @Test
    public void test3() throws Exception {
        toolsService.migrateOrderInfo(Lists.newArrayList("ww9ef25deeac77aa51"));
    }

    @Test
    public void test4() throws Exception {
        toolsService.migrateBusinessInfoBind(Lists.newArrayList("ww9ef25deeac77aa51"));
    }

    @Test
    public void test5() throws Exception {
        toolsService.migrateQyweixinIdToOpenid(Lists.newArrayList("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"));
    }

    @Test
    public void test6() throws Exception {
        toolsService.migrateFileInfo(Lists.newArrayList("wpwx1mDAAAEMVuMDFDSOv6T1A-bVj7Dw"));
    }

    @Test
    public void test7() throws Exception {
        toolsService.migrateExternalContacts(Lists.newArrayList("wpwx1mDAAAEMVuMDFDSOv6T1A-bVj7Dw"));
    }

    @Test
    public void test8() throws Exception {
        toolsService.migrateAppBind(Lists.newArrayList("wpwx1mDAAAuG1v2t0E7AmVx4Gyh9WO_w"));
    }

    @Test
    public void test9() throws Exception {
        toolsService.migrateConfigInfo(Lists.newArrayList("wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA"));
    }

    @Test
    public void test10() throws Exception {
//        OuterOaEnterpriseBindEntity userInfo = new OuterOaEnterpriseBindEntity();
//        userInfo.setId("123");
//        String userInfo2 = new String(Base64.encodeBase64(JSON.toJSONBytes(userInfo)));
//        log.info("userInfo2={}",userInfo2);
        String userInfo2 = "eyJjb3JwSWQiOiJ3cHd4MW1EQUFBYWFMbEtXUTh1R20yM3RqX0tNOGdJQSIsImRldmljZUlkIjoiZmFmMjZkMjktMTA3OC00MTIxLWExMzQtYjVlZWIyZTA5ZGIzIiwiZXJyY29kZSI6MCwiZXJybXNnIjoib2siLCJleHBpcmVzX2luIjoxODAwLCJzdWNjZXNzIjp0cnVlLCJ1c2VySWQiOiJ3b3d4MW1EQUFBc3d0dzYweFJvbU9DUWV4ZUxYZFUzdyIsInVzZXJfdGlja2V0IjoiTW5tZHhSeWVYZy1VdHNXRmhzN01lS09UR2RtZDJ4QWRsSUx1VEp3YlA2RmpkSFNzVERXQ1BmWUF3b0RoT0xwR0dWODJOaHhzMGxnNXhEM1daWEgyRlNIbEdnSXA1R015TVNEV1JsTmtWM2sifQ==";
        String pramUrl = new String(Base64.decodeBase64(userInfo2.getBytes()));
        Object userInfo = JSONObject.parseObject(pramUrl, Object.class);
        log.info("userInfo={}",userInfo);
    }

    @Test
    public void test11() throws Exception {
        toolsService.deleteQYWXAccountBind("wpwx1mDAAAuAKnjk69jwNHKb2mjOi11Xg", "dds71012");
    }

    @Test
    public void migrateOnlyEnterpriseBind() throws Exception {
        /**
         * INSERT INTO `open_qywx`.`enterprise_account_bind` (`id`, `source`, `fs_ea`, `out_ea`, `dep_id`, `isv_out_ea`, `aut_retention`, `open_authorization`, `aut_bind`, `status`, `bind_type`, `domain`, `gmt_modified`, `gmt_create`, `account_sync_config`, `account_sync_config_time`, `leads_sync_config_time`, `contact_sync_config_time`, `extend`, `deployment_type`) VALUES (28, 'qywx', '74431', 'ww1f243fc2eba76730', '1', NULL, 0, 0, 0, 0, 0, 'https://crm.ceshi112.com', '2018-09-10 17:23:47', '2018-09-10 17:23:47', NULL, NULL, NULL, NULL, NULL, 0);
         */
        toolsService.migrateOnlyEnterpriseBind(Lists.newArrayList("ww1f243fc2eba76730"));
    }

    @Test
    public void cleanNewEnterpriseBind() throws Exception {
        toolsService.cleanNewEnterpriseBind("wwbd02a00abf156207", "wx345135b869590c26", "74410");
    }

    @Test
    public void fixEnterpriseManualBindInfo() throws Exception {
        toolsService.fixEnterpriseManualBindInfo();
    }

    @Test
    public void testSortEnterpriseBind() throws Exception {
        List<QyweixinCorpBindBo> corpBindBoList = new ArrayList<>();
        QyweixinCorpBindBo corpBindBo = new QyweixinCorpBindBo();
        corpBindBo.setAppId("wxdfgdger");
        corpBindBoList.add(corpBindBo);
        QyweixinCorpBindBo corpBindBo1 = new QyweixinCorpBindBo();
        corpBindBo1.setAppId("wxdjhtraeafdsfesf");
        corpBindBoList.add(corpBindBo1);
        QyweixinCorpBindBo corpBindBo2 = new QyweixinCorpBindBo();
        corpBindBo2.setAppId("dkdfgdgerfess");
        corpBindBoList.add(corpBindBo2);
        QyweixinCorpBindBo corpBindBo3 = new QyweixinCorpBindBo();
        corpBindBo3.setAppId(ConfigCenter.crmAppId);
        corpBindBoList.add(corpBindBo3);
        QyweixinCorpBindBo corpBindBo4 = new QyweixinCorpBindBo();
        corpBindBo4.setAppId(ConfigCenter.repAppId);
        corpBindBoList.add(corpBindBo4);


        List<QyweixinCorpBindBo> filteredWxList = corpBindBoList.stream()
                .filter(v -> v.getAppId().startsWith("wx"))
                .sorted(Comparator.comparing(v -> !v.getAppId().equals(ConfigCenter.crmAppId)))
                .collect(Collectors.toList());

        System.out.println(filteredWxList);
        System.out.println(filteredWxList.get(0));
    }
}
