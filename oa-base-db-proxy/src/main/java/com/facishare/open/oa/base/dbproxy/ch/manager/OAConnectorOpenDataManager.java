package com.facishare.open.oa.base.dbproxy.ch.manager;
import com.facishare.open.oa.base.dbproxy.ch.entity.BizLogOaconnectoropendataDistBo;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.oa.base.dbproxy.utils.DateUtils;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.DataTypeEnum;
import com.fxiaoke.common.IpUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.OAConnectorOpenDataLog;
import com.fxiaoke.log.dto.OAConnectorOpenDataLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Component
public class OAConnectorOpenDataManager {
    @Resource
    private BizLogOaconnectoropendataDistManager bizLogOaconnectoropendataDistManager;

    @Resource
    private OAConnectorBizLogTask oaConnectorBizLogTask;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    public void send(OAConnectorOpenDataModel model) {
        if(model.getCreateTime() == null) {
            model.setCreateTime(System.currentTimeMillis());
        }
        LogUtils.info("OAConnectorOpenDataManager.send,model={}",model);

        if(StringUtils.isNotEmpty(model.getDataTypeId())
                && model.getDataTypeId().equals(DataTypeEnum.EMPLOYEE_LOGIN.getDataType())) {
            oaConnectorBizLogTask.add(model);
        } else {
            sendNow(model);
        }
    }

    public void sendNow(OAConnectorOpenDataModel model) {
        LogUtils.info("OAConnectorOpenDataManager.sendNow,model={}",model);
        //人员登陆失败区分
        if(StringUtils.isNotEmpty(model.getDataTypeId())
                && model.getDataTypeId().equals(DataTypeEnum.EMPLOYEE_LOGIN.getDataType())) {
            //安装失败，无记录
            try {
                if(model.getErrorCode().equals("100")) {
                    List<BizLogOaconnectoropendataDistBo> distBos = bizLogOaconnectoropendataDistManager.findEnterpriseCreateError(ChannelEnum.feishu.name(), DataTypeEnum.ENTERPRISE_CREATE.getDataType(), model.getCorpId());
                    if(CollectionUtils.isNotEmpty(distBos)) {
                        model.setDataTypeId(DataTypeEnum.NEW_EMPLOYEE_LOGIN.getDataType());
                    }
                } else {
                    //安装失败，有记录
                    if(model.getErrorCode().equals("101") || model.getErrorCode().equals("102")) {
                        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().outEa(model.getCorpId()).build());
                        if(CollectionUtils.isNotEmpty(enterpriseBindList)) {
                            Date date = DateUtils.parseDate("2024-02-01", "yyyy-MM-dd");
                            assert date != null;
                            if(enterpriseBindList.get(0).getCreateTime() > date.getTime()) {
                                model.setDataTypeId(DataTypeEnum.NEW_EMPLOYEE_LOGIN.getDataType());
                            }
                        }
                    }
                }
            } finally {
                LogUtils.info("OAConnectorOpenDataManager.sendNow,model.dataTypeId={}", model.getDataTypeId());
            }
        }

        OAConnectorOpenDataLogDTO dto = OAConnectorOpenDataLogDTO.builder()
                .appName(ConfigHelper.getProcessInfo().getName())
                .traceId(TraceUtils.getTraceId())
                .ea(model.getEa())
                .createTime(model.getCreateTime())
                .serverIp(IpUtil.getSiteLocalIp())
                .appId(model.getAppId())
                .channelId(model.getChannelId())
                .corpId(model.getCorpId())
                .dataTypeId(model.getDataTypeId())
                .outUserId(model.getOutUserId())
                .errorCode(model.getErrorCode())
                .errorMsg(model.getErrorMsg())
                .build();

        LogUtils.info("OAConnectorOpenDataManager.sendNow,logDTO={}", dto);
        try {
            BizLogClient.send("biz-log-oaconnectoropendata", Pojo2Protobuf.toMessage(dto, OAConnectorOpenDataLog.class).toByteArray());
        } catch (Exception e) {
            LogUtils.info("OAConnectorOpenDataManager.sendNow,exception={}",e.getMessage());
        }
    }
}
