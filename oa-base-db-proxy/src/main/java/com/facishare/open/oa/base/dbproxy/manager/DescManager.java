package com.facishare.open.oa.base.dbproxy.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.oa.base.dbproxy.configVo.ConstantDb;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oauth.model.enums.AppTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.annotation.SystemAnnotation;
import com.facishare.open.outer.oa.connector.common.api.enums.CRMEmployeeFiledEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldResult;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Set;

/**
 * 系统字段描述管理器
 */
@Slf4j
@Component
public class DescManager {

    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    /**
     * 返回页面列表可以筛选的CRM字段 渠道默认+规则绑定
     */
    public Set<SystemFieldMappingResult.SystemField> getLayoutCrmFilterField(String currentDcId,
            ChannelEnum channelEnum) {

        // 默认CRM姓名+手机号
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(currentDcId);
        Set<SystemFieldMappingResult.SystemField> systemFields = Sets.newHashSet();
        SystemFieldMappingResult.SystemField crmNameFields = new SystemFieldMappingResult.SystemField();
        crmNameFields.setFieldLabel(i18NStringManager.getLangByEa(CRMEmployeeFiledEnum.NAME.getI18nKey().getI18nKey(),null,entityById.getFsEa(),CRMEmployeeFiledEnum.NAME.getDesc()));
        crmNameFields.setFieldApiName(CRMEmployeeFiledEnum.NAME.getCode());
        SystemFieldMappingResult.SystemField phoneFields = new SystemFieldMappingResult.SystemField();
        phoneFields.setFieldLabel(i18NStringManager.getLangByEa(CRMEmployeeFiledEnum.PHONE.getI18nKey().getI18nKey(),null,entityById.getFsEa(),CRMEmployeeFiledEnum.PHONE.getDesc()));
        phoneFields.setFieldApiName(CRMEmployeeFiledEnum.PHONE.getCode());
        systemFields.add(crmNameFields);
        systemFields.add(phoneFields);
        // 添加规则
        // 获取配置的系统字段
        OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager
                .getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, currentDcId);
        SystemFieldMappingResult systemFieldResult = JSONObject.parseObject(entityByDataCenterId.getConfigInfo(),
                SystemFieldMappingResult.class);
        for (SystemFieldMappingResult.ItemFieldMapping itemFieldMapping : systemFieldResult.getItemFieldMappings()) {
            if(itemFieldMapping.getOuterDataText()==null){
                continue;
            }
            SystemFieldMappingResult.SystemField systemField = new SystemFieldMappingResult.SystemField();
            systemField.setFieldLabel(itemFieldMapping.getCrmFieldLabel());
            systemField.setFieldApiName(itemFieldMapping.getCrmFieldApiName());
            systemFields.add(systemField);
        }
        return systemFields;
    }

    /**
     * 人员绑定使用的配置字段映射，用于页面绑定，外部人员字段动态字段展示
     */
    public Result<SystemFieldMappingResult> getOrInitFieldMapping(
            OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            OuterOaConfigInfoTypeEnum outerOaConfigInfoTypeEnum) {
        OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager
                .getEntityByDataCenterId(outerOaConfigInfoTypeEnum, outerOaEnterpriseBindEntity.getId());
        if (ObjectUtils.isNotEmpty(entityByDataCenterId)) {
            SystemFieldMappingResult systemFieldResult = JSONObject.parseObject(entityByDataCenterId.getConfigInfo(),
                    SystemFieldMappingResult.class);
            return Result.newSuccess(systemFieldResult);
        }

        OuterOaConfigInfoEntity outerOaConfigInfoEntity = OuterOaConfigInfoEntity.builder()
                .id(IdGenerator.get())
                .dcId(outerOaEnterpriseBindEntity.getId()).appId(outerOaEnterpriseBindEntity.getAppId())
                .channel(outerOaEnterpriseBindEntity.getChannel()).fsEa(outerOaEnterpriseBindEntity.getFsEa())
                .createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis())
                .outEa(outerOaEnterpriseBindEntity.getOutEa()).type(outerOaConfigInfoTypeEnum).build();

        if (outerOaConfigInfoTypeEnum == OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY) {
            // 使用渠道枚举直接构建映射结果
            SystemFieldMappingResult mappingResult = outerOaEnterpriseBindEntity.getChannel().buildFieldMapping();
            // 保存映射结果到数据库

            outerOaConfigInfoEntity.setConfigInfo(JSONObject.toJSONString(mappingResult));
            //需要判断，自建应用类型，默认以手机号码匹配，其余默认姓名
            String connectInfo = outerOaEnterpriseBindEntity.getConnectInfo();
            BaseConnectorVo baseConnectorVo=JSON.parseObject(connectInfo,BaseConnectorVo.class);
            for (SystemFieldMappingResult.ItemFieldMapping itemFieldMapping : mappingResult.getItemFieldMappings()) {
                if((itemFieldMapping.getCrmFieldApiName().equals(CRMEmployeeFiledEnum.PHONE.getCode())&&baseConnectorVo.getAppType()== OuterOaAppInfoTypeEnum.selfBuild)){
                    itemFieldMapping.setMatchUnique(true);
                    break;
                }
                if((itemFieldMapping.getCrmFieldApiName().equals(CRMEmployeeFiledEnum.NAME.getCode())&&baseConnectorVo.getAppType()== OuterOaAppInfoTypeEnum.isv)){
                    itemFieldMapping.setMatchUnique(true);
                    break;
                }
            }
            outerOaConfigInfoManager.insert(outerOaConfigInfoEntity);
            return Result.newSuccess(mappingResult);
        }
        return Result.newSuccess();
    }

    /**
     * 获取外部系统字段映射
     */
    public Result<Set<SystemFieldResult>> getOuterSystemField(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity,
            ChannelEnum channelEnum) {
        // 查询配置数据库是否已有OUTER_SYSTEM_OBJECT_FIELDS+DCID的数据
        OuterOaConfigInfoEntity configEntity = outerOaConfigInfoManager.getEntityByDataCenterId(
                OuterOaConfigInfoTypeEnum.OUTER_SYSTEM_OBJECT_FIELDS, outerOaEnterpriseBindEntity.getId());
        // 如果数据库中已存在配置，直接返回
        if (configEntity != null && configEntity.getConfigInfo() != null) {
            Set<SystemFieldResult> systemFieldResult = JSON.parseObject(configEntity.getConfigInfo(),
                    new TypeReference<Set<SystemFieldResult>>() {
                    });
            return Result.newSuccess(systemFieldResult);
        }
        return Result.newSuccess();
    }

    /**
     * 获取crm对象的描述
     */
    public Set<SystemFieldResult> getCrmSystemField(Integer tenantId, String objectApiName) {
        HeaderObj headerObj = new HeaderObj(tenantId, ConstantDb.CRM_OPERATOR_USER_ID);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describe = objectDescribeService
                .getDescribe(headerObj, objectApiName);
        Set<SystemFieldResult> systemFieldResults = Sets.newHashSet();
        if (describe.isSuccess()) {
            ControllerGetDescribeResult describeData = describe.getData();
            for (String fieldKey : describeData.getDescribe().getFields().keySet()) {
                SystemFieldResult systemFieldResult = new SystemFieldResult();
                systemFieldResult.setFieldType(describeData.getDescribe().getFields().get(fieldKey).getType());
                systemFieldResult.setFieldLabel(describeData.getDescribe().getFields().get(fieldKey).getLabel());
                systemFieldResult.setFieldApiName(fieldKey);
                systemFieldResults.add(systemFieldResult);
            }
        }
        return systemFieldResults;
    }

}