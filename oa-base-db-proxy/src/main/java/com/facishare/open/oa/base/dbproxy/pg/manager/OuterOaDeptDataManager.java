package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaDeptDataMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDeptDataParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.api.IdGenerator;
import org.apache.commons.collections4.CollectionUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Manager 类 - 外部部门信息
 */
@Component
public class OuterOaDeptDataManager {

    private static final Logger logger = LoggerFactory.getLogger(OuterOaDeptDataManager.class);

    @Resource
    private OuterOaDeptDataMapper outerOaDeptDataMapper;

    public Integer insert(OuterOaDeptDataEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return outerOaDeptDataMapper.insert(entity);
    }

    public Integer updateById(OuterOaDeptDataEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            return 0;
        }
        return outerOaDeptDataMapper.updateById(entity);
    }

    // deleteByDeptId
    public Integer deleteByDeptId(ChannelEnum channel, String outEa, String appId, String outDeptId) {
        LambdaQueryWrapper<OuterOaDeptDataEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OuterOaDeptDataEntity::getOutEa, outEa);
        queryWrapper.eq(OuterOaDeptDataEntity::getChannel, channel);
        queryWrapper.eq(OuterOaDeptDataEntity::getAppId, appId);
        queryWrapper.eq(OuterOaDeptDataEntity::getOutDeptId, outDeptId);
        return outerOaDeptDataMapper.delete(queryWrapper);
    }

    /**
     * 根据参数查询部门信息列表
     * 
     * @param params 查询参数
     * @return 部门信息实体列表
     */
    public List<OuterOaDeptDataEntity> getEntities(OuterOaDeptDataParams params) {
        LambdaQueryWrapper<OuterOaDeptDataEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaDeptDataEntity::getOutEa, params.getOutEa());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaDeptDataEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaDeptDataEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getOutDeptId())) {
            wrapper.eq(OuterOaDeptDataEntity::getOutDeptId, params.getOutDeptId());
        }
        if (StringUtils.isNotEmpty(params.getParentDeptId())) {
            wrapper.eq(OuterOaDeptDataEntity::getParentDeptId, params.getParentDeptId());
        }

        return outerOaDeptDataMapper.selectList(wrapper);
    }

    /**
     * 批量更新外部OA部门信息 根据channel、appId、outEa、outDeptId作为唯一键 不存在则插入，存在则更新其他字段
     * 
     * @param entities 需要更新的实体列表
     * @return 更新成功的记录数
     */
    public Integer batchUpdateOutDeptId(List<OuterOaDeptDataEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        // 过滤出有效的实体
        List<OuterOaDeptDataEntity> validEntities = entities.stream()
                .filter(entity -> !StringUtils.isEmpty(entity.getAppId()) && !StringUtils.isEmpty(entity.getOutEa())
                        && entity.getChannel() != null && !StringUtils.isEmpty(entity.getOutDeptId()))
                .collect(Collectors.toList());

        if (validEntities.isEmpty()) {
            LogUtils.warn("No valid entities found for batch update");
            return 0;
        }

        for (OuterOaDeptDataEntity validEntity : validEntities) {
            if (StringUtils.isEmpty(validEntity.getId())) {
                validEntity.setId(IdGenerator.get());
            }
            if (Objects.isNull(validEntity.getCreateTime())) {
                validEntity.setCreateTime(System.currentTimeMillis());
            }
            validEntity.setUpdateTime(System.currentTimeMillis());
        }

        try {
            return outerOaDeptDataMapper.batchSaveOrUpdate(validEntities);
        } catch (Exception e) {
            LogUtils.error("Failed to execute batch save or update: {}", e.getMessage(), e);
            return 0;
        }
    }

    public Integer upsert(OuterOaDeptDataEntity entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return batchUpdateOutDeptId(Lists.newArrayList(entity));
    }

    /**
     * 查询指定条件下的所有部门ID
     * 
     * @param outEa   外部企业账号
     * @param channel 渠道
     * @param appId   应用ID
     * @return 部门ID列表
     */
    public List<String> queryAllDeptIds(String outEa, ChannelEnum channel, String appId) {
        if (StringUtils.isEmpty(outEa) || channel == null || StringUtils.isEmpty(appId)) {
            logger.warn("Invalid parameters for queryAllDeptIds: outEa={}, channel={}, appId={}", outEa, channel,
                    appId);
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<OuterOaDeptDataEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OuterOaDeptDataEntity::getOutEa, outEa).eq(OuterOaDeptDataEntity::getChannel, channel)
                    .eq(OuterOaDeptDataEntity::getAppId, appId).select(OuterOaDeptDataEntity::getOutDeptId); // 只查询
                                                                                                             // outDeptId
                                                                                                             // 字段

            return outerOaDeptDataMapper.selectList(queryWrapper).stream().map(OuterOaDeptDataEntity::getOutDeptId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to query dept ids: outEa={}, channel={}, appId={}, error={}", outEa, channel, appId,
                    e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询指定条件下的所有部门ID，并删除不在可见范围内的记录
     * 
     * @param outEa          外部企业账号
     * @param channel        渠道
     * @param appId          应用ID
     * @param visibleDeptIds 可见的部门ID集合
     * @return 删除的记录数
     */
    public Integer deleteInvisibleDepts(String outEa, ChannelEnum channel, String appId, Set<String> visibleDeptIds) {
        if (StringUtils.isEmpty(outEa) || channel == null || StringUtils.isEmpty(appId)) {
            logger.warn("Invalid parameters for deleteInvisibleDepts: outEa={}, channel={}, appId={}", outEa, channel,
                    appId);
            return 0;
        }

        try {
            // 1. 查询数据库中现有的所有部门ID
            List<String> existingDeptIds = queryAllDeptIds(outEa, channel, appId);
            Set<String> deptIdsToDelete = new HashSet<>(existingDeptIds);

            // 2. 计算需要删除的部门ID（差集）
            deptIdsToDelete.removeAll(visibleDeptIds);
            if (deptIdsToDelete.isEmpty()) {
                logger.info("No departments need to be deleted for outEa={}, channel={}, appId={}", outEa, channel,
                        appId);
                return 0;
            }

            // 3. 删除不可见的部门记录
            LambdaQueryWrapper<OuterOaDeptDataEntity> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(OuterOaDeptDataEntity::getOutEa, outEa).eq(OuterOaDeptDataEntity::getChannel, channel)
                    .eq(OuterOaDeptDataEntity::getAppId, appId)
                    .in(OuterOaDeptDataEntity::getOutDeptId, deptIdsToDelete);

            int deletedCount = outerOaDeptDataMapper.delete(deleteWrapper);
            logger.info("Deleted {} invisible departments for outEa={}, channel={}, appId={}", deletedCount, outEa,
                    channel, appId);
            return deletedCount;
        } catch (Exception e) {
            logger.error("Failed to delete invisible departments: outEa={}, channel={}, appId={}, error={}", outEa,
                    channel, appId, e.getMessage(), e);
            return 0;
        }
    }

    public OuterOaDeptDataEntity getEntity(ChannelEnum channel, String outEa, String appId, String outDepId) {
        LambdaQueryWrapper<OuterOaDeptDataEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OuterOaDeptDataEntity::getOutEa, outEa).eq(OuterOaDeptDataEntity::getChannel, channel)
                .eq(OuterOaDeptDataEntity::getAppId, appId).eq(OuterOaDeptDataEntity::getOutDeptId, outDepId);
        return outerOaDeptDataMapper.selectOne(queryWrapper);
    }

    /**
     * 根据企业账号、渠道、应用ID和部门ID列表批量查询部门信息
     * 
     * @param outEa      外部企业账号
     * @param channel    渠道
     * @param appId      应用ID
     * @param outDeptIds 部门ID列表
     * @return 部门信息实体列表
     */
    public List<OuterOaDeptDataEntity> batchGetByOutDeptIds(String outEa, ChannelEnum channel, String appId,
            List<String> outDeptIds) {
        if (StringUtils.isEmpty(outEa) || channel == null || StringUtils.isEmpty(appId)
                || CollectionUtils.isEmpty(outDeptIds)) {
            logger.warn("Invalid parameters for batchGetByOutDeptIds: outEa={}, channel={}, appId={}, outDeptIds={}",
                    outEa, channel, appId, outDeptIds);
            return new ArrayList<>();
        }

        try {
            return outerOaDeptDataMapper.batchGetByOutDeptIds(channel, outEa, appId, outDeptIds);
        } catch (Exception e) {
            logger.error("Failed to batch query dept by ids: outEa={}, channel={}, appId={}, outDeptIds={}, error={}",
                    outEa, channel, appId, outDeptIds, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询总数
     *
     * @param params
     * @return
     */
    public Long queryCount(OuterOaDeptDataParams params) {
        LambdaQueryWrapper<OuterOaDeptDataEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            queryWrapper.eq(OuterOaDeptDataEntity::getOutEa, params.getOutEa());
        }
        if (params.getChannel() != null) {
            queryWrapper.eq(OuterOaDeptDataEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            queryWrapper.eq(OuterOaDeptDataEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getOutDeptId())) {
            queryWrapper.eq(OuterOaDeptDataEntity::getOutDeptId, params.getOutDeptId());
        }
        if (StringUtils.isNotEmpty(params.getParentDeptId())) {
            queryWrapper.eq(OuterOaDeptDataEntity::getParentDeptId, params.getParentDeptId());
        }
        return outerOaDeptDataMapper.selectCount(queryWrapper);
    }

    public int batchInsert(List<OuterOaDeptDataEntity> dataEntities) {
        if (CollectionUtils.isEmpty(dataEntities)) {
            return 0;
        }

        // 对数据进行去重，保留最新的记录
        Map<String, OuterOaDeptDataEntity> uniqueMap = new HashMap<>();
        for (OuterOaDeptDataEntity entity : dataEntities) {
            if (StringUtils.isEmpty(entity.getId())) {
                entity.setId(IdGenerator.get());
            }
            if (entity.getCreateTime() == null) {
                entity.setCreateTime(System.currentTimeMillis());
            }
            if (entity.getUpdateTime() == null) {
                entity.setUpdateTime(System.currentTimeMillis());
            }

            // 使用channel、appId、outEa和outDeptId作为唯一键
            String key = String.format("%s_%s_%s_%s", 
                entity.getChannel(), 
                entity.getAppId(),
                entity.getOutEa(),
                entity.getOutDeptId());
            
            OuterOaDeptDataEntity existing = uniqueMap.get(key);
            if (existing == null || (entity.getUpdateTime() != null && 
                (existing.getUpdateTime() == null || entity.getUpdateTime() > existing.getUpdateTime()))) {
                uniqueMap.put(key, entity);
            }
        }

        List<OuterOaDeptDataEntity> deduplicatedEntities = new ArrayList<>(uniqueMap.values());
        LogUtils.info("batchInsert: Original size={}, After deduplication size={}", 
            dataEntities.size(), deduplicatedEntities.size());

        if (deduplicatedEntities.isEmpty()) {
            return 0;
        }

        try {
            return outerOaDeptDataMapper.batchSaveOrUpdate(deduplicatedEntities);
        } catch (Exception e) {
            LogUtils.error("Failed to batch insert dept data: {}", e.getMessage(), e);
            throw e;
        }
    }
}