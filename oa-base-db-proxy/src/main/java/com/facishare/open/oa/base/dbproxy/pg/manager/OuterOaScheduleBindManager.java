package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaScheduleBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaScheduleBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaScheduleBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaScheduleBindStatusEnum;
import com.fxiaoke.api.IdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * Manager 类 - 日程绑定
 */
@Component
public class OuterOaScheduleBindManager {

    @Resource
    private OuterOaScheduleBindMapper outerOaScheduleBindMapper;

    public Integer insert(OuterOaScheduleBindEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return outerOaScheduleBindMapper.insert(entity);
    }

    public Integer updateById(OuterOaScheduleBindEntity entity) {
        return outerOaScheduleBindMapper.updateById(entity);
    }

    /**
     * 根据参数查询日程绑定信息列表
     * 
     * @param params 查询参数
     * @return 日程绑定实体列表
     */
    public List<OuterOaScheduleBindEntity> getEntities(OuterOaScheduleBindParams params) {
        LambdaQueryWrapper<OuterOaScheduleBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getId())) {
            wrapper.eq(OuterOaScheduleBindEntity::getId, params.getId());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaScheduleBindEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaScheduleBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaScheduleBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaScheduleBindEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getFsScheduleId())) {
            wrapper.eq(OuterOaScheduleBindEntity::getFsScheduleId, params.getFsScheduleId());
        }
        if (StringUtils.isNotEmpty(params.getOutScheduleId())) {
            wrapper.eq(OuterOaScheduleBindEntity::getOutScheduleId, params.getOutScheduleId());
        }
        if (params.getStatus() != null) {
            wrapper.eq(OuterOaScheduleBindEntity::getStatus, params.getStatus());
        }

        return outerOaScheduleBindMapper.selectList(wrapper);
    }

    /**
     * 根据crm对象id,找到外部日程id
     */
    public OuterOaScheduleBindEntity queryOutScheduleId(String fsEa, String outEa, String fsScheduleId) {
        LambdaQueryWrapper<OuterOaScheduleBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaScheduleBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaScheduleBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaScheduleBindEntity::getFsScheduleId, fsScheduleId);
        OuterOaScheduleBindEntity entity = outerOaScheduleBindMapper.selectOne(wrapper);
        if (entity != null) {
            return entity;
        }
        return null;
    }

    public List<OuterOaScheduleBindEntity> queryListByOutScheduleId(String fsEa, String outEa, String fsScheduleId) {
        LambdaQueryWrapper<OuterOaScheduleBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaScheduleBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaScheduleBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaScheduleBindEntity::getFsScheduleId, fsScheduleId);
        List<OuterOaScheduleBindEntity> entity = outerOaScheduleBindMapper.selectList(wrapper);
        if (entity != null) {
            return entity;
        }
        return null;
    }

    public Integer deleteByOutUserIds(String fsEa, String outEa, String outScheduleId, List<String> outUsrId) {
        LambdaQueryWrapper<OuterOaScheduleBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaScheduleBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaScheduleBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaScheduleBindEntity::getOutScheduleId, outScheduleId);
        wrapper.in(OuterOaScheduleBindEntity::getOutUserId, outUsrId);
        int count = outerOaScheduleBindMapper.delete(wrapper);
        return count;
    }

    public Integer updateStatusByScheduleId(String fsEa, String outEa, String outScheduleId,
            OuterOaScheduleBindStatusEnum outerOaScheduleBindStatusEnum) {
        LambdaQueryWrapper<OuterOaScheduleBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaScheduleBindEntity::getFsEa, fsEa);
        wrapper.eq(OuterOaScheduleBindEntity::getOutEa, outEa);
        wrapper.eq(OuterOaScheduleBindEntity::getOutScheduleId, outScheduleId);

        OuterOaScheduleBindEntity updateEntity = new OuterOaScheduleBindEntity();
        updateEntity.setUpdateTime(System.currentTimeMillis());
        updateEntity.setStatus(outerOaScheduleBindStatusEnum);

        return outerOaScheduleBindMapper.update(updateEntity, wrapper);
    }

    /**
     * 批量插入或更新日程绑定信息 基于(fs_ea, out_ea, fs_schedule_id)作为唯一索引进行更新
     *
     * @param entities 需要插入或更新的实体列表
     * @return 成功处理的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchUpsert(List<OuterOaScheduleBindEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return 0;
        }

        // 为没有ID的实体生成ID，并设置时间
        long now = System.currentTimeMillis();
        entities.forEach(entity -> {
            if (StringUtils.isEmpty(entity.getId())) {
                entity.setId(IdGenerator.get());
            }
            if (entity.getCreateTime() == null) {
                entity.setCreateTime(now);
            }
            entity.setUpdateTime(now);
        });

        return outerOaScheduleBindMapper.batchUpsert(entities);
    }

}
