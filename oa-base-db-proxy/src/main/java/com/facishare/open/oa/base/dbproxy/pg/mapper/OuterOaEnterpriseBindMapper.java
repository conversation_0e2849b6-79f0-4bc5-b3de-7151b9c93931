package com.facishare.open.oa.base.dbproxy.pg.mapper;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface OuterOaEnterpriseBindMapper extends BaseMapper2<OuterOaEnterpriseBindEntity> {

    /**
     * 批量插入或更新企业绑定信息 当记录存在时更新，不存在时插入 使用fs_ea,out_ea,app_id,channel作为唯一性约束
     * 当记录重复时，保持原有id不变，仅更新其他字段
     *
     * @param list 需要插入或更新的实体列表
     * @return 受影响的记录数
     */
    @Insert("<script>" + "INSERT INTO outer_oa_enterprise_bind ("
            + "id, channel, fs_ea, out_ea, app_id, connect_info, bind_type, bind_status, create_time, update_time"
            + ") VALUES " + "<foreach collection='list' item='item' separator=','>" + "("
            + "#{item.id}, #{item.channel}, #{item.fsEa}, #{item.outEa}, "
            + "#{item.appId}, #{item.connectInfo}, #{item.bindType}, #{item.bindStatus}, "
            + "#{item.createTime}, #{item.updateTime}" + ")" + "</foreach> "
            + "ON CONFLICT (fs_ea, out_ea, app_id, channel) DO UPDATE SET " + "connect_info = EXCLUDED.connect_info, "
            + "bind_type = EXCLUDED.bind_type, " + "bind_status = EXCLUDED.bind_status, "
            + "update_time = EXCLUDED.update_time" + "</script>")
    int batchUpsert(@Param("list") List<OuterOaEnterpriseBindEntity> list);

    @Update("UPDATE outer_oa_enterprise_bind " +
            "SET connect_info = jsonb_set(" +
            "   CASE " +
            "       WHEN connect_info IS NULL OR connect_info::text = '' THEN '{\"repeatIndex\": 1}' " +
            "       WHEN connect_info->>'repeatIndex' IS NULL THEN jsonb_set(connect_info::jsonb, '{repeatIndex}', '1') " +
            "       ELSE jsonb_set(connect_info::jsonb, '{repeatIndex}', ((connect_info->>'repeatIndex')::int + 1)::text::jsonb) " +
            "   END::jsonb" +
            ") " +
            "WHERE channel = #{channel} AND fs_ea = #{fsEa}")
    Integer updateRepeatIndex(@Param("fsEa") String fsEa, @Param("channel") ChannelEnum channel);

    @Update("UPDATE outer_oa_enterprise_bind " +
            "SET connect_info = jsonb_set(" +
            "   CASE " +
            "       WHEN connect_info IS NULL OR connect_info::text = '' THEN '{\"isInit\": 1}' " +
            "       ELSE jsonb_set(connect_info::jsonb, '{isInit}', '1') " +
            "   END::jsonb" +
            ") " +
            "WHERE channel = #{channel} AND fs_ea = #{fsEa}")
    Integer updateInitStatus(@Param("fsEa") String fsEa, @Param("channel") ChannelEnum channel);

    @Delete("DELETE FROM outer_oa_enterprise_bind WHERE fs_ea = #{fsEa} AND app_key = #{appKey} AND channel = #{channel}")
    Integer deleteByBusinessId(@Param("fsEa") String fsEa, @Param("appKey") String appKey, @Param("channel") ChannelEnum channel);

    /**
     * 批量插入或更新企业绑定信息 当记录存在时更新，不存在时插入 使用id作为唯一性约束，适用于已知ID的更新场景 当ID冲突时，更新指定字段
     *
     * @param list 需要插入或更新的实体列表
     * @return 受影响的记录数
     */
    @Insert("<script>" + "INSERT INTO outer_oa_enterprise_bind ("
            + "id, channel, fs_ea, out_ea, app_id, connect_info, bind_type, bind_status, create_time, update_time"
            + ") VALUES " + "<foreach collection='list' item='item' separator=','>" + "("
            + "#{item.id}, #{item.channel}, #{item.fsEa}, #{item.outEa}, "
            + "#{item.appId}, #{item.connectInfo}, #{item.bindType}, #{item.bindStatus}, "
            + "#{item.createTime}, #{item.updateTime}" + ")" + "</foreach> " + "ON CONFLICT (id) DO UPDATE SET "
            + "connect_info = EXCLUDED.connect_info, " + "bind_type = EXCLUDED.bind_type, "
            + "app_id = EXCLUDED.app_id, " + "out_ea = EXCLUDED.out_ea, "
            + "bind_status = EXCLUDED.bind_status, " + "update_time = EXCLUDED.update_time" + "</script>")
    int batchUpsertById(@Param("list") List<OuterOaEnterpriseBindEntity> list);

    /**
     * 根据纷享企业账号和员工ID更新绑定状态
     *
     * @param channelEnum   渠道枚举
     * @param fsEa          纷享企业账号
     * @param fsEmployeeIds 员工ID列表
     * @param statusEnum    绑定状态枚举
     * @param updateTime    更新时间
     * @return 受影响的记录数
     */
    @Update("<script>" +
            "UPDATE outer_oa_enterprise_bind " +
            "SET bind_status = #{statusEnum} " +
            "AND update_time = #{updateTime} " +
            "WHERE channel = #{channelEnum} " +
            "AND fs_ea = #{fsEa} " +
            "AND fs_emp_id IN <foreach collection='fsEmployeeIds' item='fsEmployeeId' separator=','>#{fsEmployeeId}</foreach>" +
            "AND bind_status != #{statusEnum}" +
            "</script>")
    Integer updateStatusBySFsEaAndFsEmpId(@Param("channelEnum") ChannelEnum channelEnum, @Param("fsEa") String fsEa, @Param("fsEmployeeIds") List<String> fsEmployeeIds, @Param("statusEnum") BindStatusEnum statusEnum, @Param("updateTime") long updateTime);

    @Update("${sqlStr}")
    Integer superUpdateSql(@Param("sqlStr") String sqlStr);
}
