package com.facishare.open.oa.base.dbproxy.pg.params;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 外部部门表查询参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OuterOaDeptDataParams {
    /**
     * 外部企业账号
     */
    private String outEa;

    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 外部部门id
     */
    private String outDeptId;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 父部门id
     */
    private String parentDeptId;
}