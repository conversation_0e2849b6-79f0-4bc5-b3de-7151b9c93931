package com.facishare.open.outer.oa.connector.common.api.enums;

import com.facishare.open.outer.oa.connector.common.api.admin.*;
import com.facishare.open.outer.oa.connector.common.api.annotation.SystemAnnotation;
import com.facishare.open.outer.oa.connector.common.api.info.BaseConnectParams;
import com.facishare.open.outer.oa.connector.common.api.info.BaseOuterOaAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.info.FieldInfo;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.object.FeishuEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.object.QywxEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.common.api.params.*;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.common.api.utils.GsonUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 连接器渠道
 */
@Getter
// IgnoreI18nFile
public enum ChannelEnum implements Serializable {
    feishu("飞书", "CONNECTOR_FEISHU", "feishu_data_sync_app", FeiShuConnectorVo.class, null, FeishuEmployeeObject.class, "i18n.key.feishu"),
    qywx("企业微信", "CONNECTOR_QYWX", null, QywxConnectorVo.class, QyweixinAppInfoParams.class, QywxEmployeeObject.class, "i18n.key.qywx"),
    dingding("钉钉", "DING_TALK_DOCK_APP", "ding_talk_dock_app", DingConnectorVo.class, DingtalkAppInfoParams.class, DingTalkEmployeeObject.class, "i18n.key.dingding"),
    aliyun("阿里云 云市场", null, null, null, null, null, "i18n.key.aliyun"),
    huawei("华为云", null, null, HuaweiEnterpriseConnectVo.class, HuaweiAppInfoParams.class, null, "i18n.key.huawei"),
    lark("lark", "CONNECTOR_LARK", "lark_data_sync_app", FeiShuConnectorVo.class, null, FeishuEmployeeObject.class, "i18n.key.lark"),//匹配erpdss设置的channel
    whatsapp("WhatsApp", null, null, null, null, null, "i18n.key.whatsapp"),
    all("所有渠道", null, null, null, null, null, "i18n.key.all");

    private final String enumName;
    private final String erpChannelName;
    private final String moduleCode;
    private final Class<? extends BaseConnectorVo> className;
    private final Class<? extends BaseOuterOaAppInfoParams> appInfoClassName;
    private final Class<?> employeeObjectClass;
    private final String i18nKey;

    ChannelEnum(String enumName, String erpChannelName,String moduleCode, Class<? extends BaseConnectorVo> className, Class<? extends BaseOuterOaAppInfoParams> appInfoClassName, Class<?> employeeObjectClass, String i18nKey) {
        this.enumName = enumName;
        this.erpChannelName = erpChannelName;
        this.moduleCode = moduleCode;
        this.className = className;
        this.appInfoClassName = appInfoClassName;
        this.employeeObjectClass = employeeObjectClass;
        this.i18nKey = i18nKey;
    }

    //有飞书的调用，不去掉
    public <T extends BaseConnectParams> T getConnectParam(String json) {
        if(StringUtils.isEmpty(json)){
            json="{}";
        }

        if (Objects.isNull(className)) {
            throw new IllegalStateException("channel not support: " + name());
        }
        return GsonUtil.fromJson(json,className);
    }

    public <T extends BaseOuterOaAppInfoParams> T getOuterOaAppInfoParam(String json) {
        if(StringUtils.isEmpty(json)){
            json="{}";
        }

        if (Objects.isNull(appInfoClassName)) {
            throw new IllegalStateException("app info not support: " + name());
        }
        return GsonUtil.fromJson(json,appInfoClassName);
    }

    /**
     * 获取默认需要映射的CRM字段列表
     */
    public List<CRMEmployeeFiledEnum> getCrmDefaultMappingFields() {
        // 基础字段：姓名
        List<CRMEmployeeFiledEnum> defaultFields = new ArrayList<>();
//        if(this == feishu){
//            defaultFields.add(CRMEmployeeFiledEnum.EMPLOYEE_NUMBER);
//        }
        // 根据渠道特性添加联系方式字段
        if (this == lark) {
            defaultFields.add(CRMEmployeeFiledEnum.EMAIL);
        }
        defaultFields.add(CRMEmployeeFiledEnum.NAME);
        defaultFields.add(CRMEmployeeFiledEnum.PHONE);
        return defaultFields;
    }

    /**
     * 根据CRM的渠道，获取到对应的外部系统的字段
     */

    public Set<SystemFieldMappingResult.SystemField> getOuterDefaultMappings() {
        List<CRMEmployeeFiledEnum> crmDefaultMappingFields = getCrmDefaultMappingFields();
        // 需要根据CRM的系统字段，获取到对应employeeObjectClass的字段描述

        if (employeeObjectClass == null) {
            return java.util.Collections.emptySet();
        }
        // 存储结果的集合
        Set<SystemFieldMappingResult.SystemField> outerFields = new java.util.LinkedHashSet<>();

        // 遍历CRM默认映射字段
        for (CRMEmployeeFiledEnum crmField : crmDefaultMappingFields) {
            FieldInfo fieldInfo = getFieldInfo(crmField);
            if (fieldInfo != null) {
                // 添加外部系统字段
                SystemFieldMappingResult.SystemField outerField = SystemFieldMappingResult.SystemField
                        .builder().fieldApiName(fieldInfo.getFieldName())
                        .fieldLabel(fieldInfo.getFieldDesc()).text(fieldInfo.getFieldDesc())
                        .build();
                outerFields.add(outerField);
            }
        }
        return outerFields;
    }

    /**
     * 构建系统字段映射结果
     *
     * @return 系统字段映射结果
     */
    public SystemFieldMappingResult buildFieldMapping() {
        SystemFieldMappingResult result = new SystemFieldMappingResult();
        List<SystemFieldMappingResult.ItemFieldMapping> itemFieldMappings = new ArrayList<>();
        //默认渠道初始化字段
        List<CRMEmployeeFiledEnum> crmDefaultMappingFields = getCrmDefaultMappingFields();
        for (int i = 0; i < crmDefaultMappingFields.size(); i++) {
            int index=i+1;
            CRMEmployeeFiledEnum crmEmployeeFiledEnum = getCrmDefaultMappingFields().get(i);
            FieldInfo fieldInfo = getFieldInfo(crmEmployeeFiledEnum);
            if (Objects.nonNull(fieldInfo)) {
                // 创建字段映射项
                String queryText =new StringBuilder().append("text").append(index).toString();
                SystemFieldMappingResult.ItemFieldMapping mapping = SystemFieldMappingResult.ItemFieldMapping
                        .builder().crmFieldApiName(crmEmployeeFiledEnum.getCode())
                        .crmFieldLabel(crmEmployeeFiledEnum.getDesc())
                        .outerOAFieldApiName(fieldInfo.getFieldName())
                        .outerOAFieldLabel(fieldInfo.getFieldDesc()).outerDataText(queryText)
                        .outerI18nKey(fieldInfo.getSystemAnnotation().i18n().getI18nKey())
                        .crmI18nKey(crmEmployeeFiledEnum.getI18nKey().getI18nKey())
                        .build();
                itemFieldMappings.add(mapping);
            }
        }
        // 设置字段映射
        result.setItemFieldMappings(itemFieldMappings);

        return result;
    }

    /**
     * 根据CRM字段获取对应渠道的字段信息
     *
     * @param crmField CRM字段枚举
     * @return 字段信息，如果未找到返回null
     */
    public FieldInfo getFieldInfo(CRMEmployeeFiledEnum crmField) {
        if (employeeObjectClass == null) {
            return null;
        }

        for (Field field : employeeObjectClass.getDeclaredFields()) {
            SystemAnnotation annotation = field.getAnnotation(SystemAnnotation.class);
            if (annotation != null && annotation.value() == crmField) {
                return new FieldInfo(field.getName(), crmField.getDesc(), annotation);
            }
        }
        return null;
    }

    /**
     * 获取当前渠道所有的字段映射信息
     *
     * @return 字段映射信息列表
     */
    public List<FieldInfo> getAllFieldMappings() {
        List<FieldInfo> mappings = new ArrayList<>();
        if (employeeObjectClass == null) {
            return mappings;
        }

        for (Field field : employeeObjectClass.getDeclaredFields()) {
            SystemAnnotation annotation = field.getAnnotation(SystemAnnotation.class);
            if (annotation != null) {
                mappings.add(new FieldInfo(field.getName(), annotation.value().getDesc(), annotation));
            }
        }
        return mappings;
    }

    /**
     * 根据code获取枚举
     */
    public static ChannelEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ChannelEnum type : values()) {
            if (type.getModuleCode()!=null&&type.getModuleCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据erp渠道转换到对应的渠道枚举
     */
    public static ChannelEnum getByErpChannel(String erpChannel) {
        if (erpChannel == null) {
            return null;
        }
        for (ChannelEnum type : values()) {
            if (type.getErpChannelName()!=null&&type.getErpChannelName().equals(erpChannel)) {
                return type;
            }
        }
        return null;
    }
}
