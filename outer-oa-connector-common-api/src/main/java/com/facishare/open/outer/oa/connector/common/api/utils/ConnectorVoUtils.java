package com.facishare.open.outer.oa.connector.common.api.utils;

import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;

/**
 * ConnectorVo工具类
 * 
 * <AUTHOR>
 */
public class ConnectorVoUtils {
    private static final Logger logger = LoggerFactory.getLogger(ConnectorVoUtils.class);

    /**
     * 根据渠道和应用类型，将数据设置到ConnectParams中
     *
     * @param connectParams 连接参数对象
     * @param sourceVo      源ConnectorVo对象
     * @param channel       渠道枚举
     */
    public static void setConnectorVoToConnectParams(Object connectParams, BaseConnectorVo sourceVo,
            ChannelEnum channel) {
        try {
            // 获取对应的ConnectorVo类
            Class<? extends BaseConnectorVo> connectorClass = channel.getClassName();
            if (connectorClass == null) {
                logger.error("cantnot found connector class, channel={}", channel);
                return;
            }

            // 创建新实例并复制属性
            BaseConnectorVo newInstance = connectorClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(sourceVo, newInstance);

            // 获取字段名并通过反射设置值
            String fieldName = ConnectorTypeRegistry.getFieldName(channel);
            Field field = connectParams.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(connectParams, newInstance);

            // 操作完成后恢复访问控制
            field.setAccessible(false);
        } catch (NoSuchFieldException e) {
            logger.error("error field: fieldName={}, error={}", ConnectorTypeRegistry.getFieldName(channel), e.getMessage());
        } catch (IllegalAccessException e) {
            logger.error("error field:: fieldName={}, error={}", ConnectorTypeRegistry.getFieldName(channel), e.getMessage());
        } catch (Exception e) {
            logger.error("error field: vofail: {}", e.getMessage(), e);
        }
    }

    /**
     * 从ConnectParams中获取ConnectorVo实例
     *
     * @param connectParams 连接参数对象
     * @param channel       渠道枚举
     * @return BaseConnectorVo实例
     */
    public static BaseConnectorVo getConnectorVoFromConnectParams(Object connectParams, ChannelEnum channel) {
        try {
            // 获取对应的ConnectorVo类
            Class<? extends BaseConnectorVo> connectorClass = ConnectorTypeRegistry.getConnectorClass(channel);
            if (connectorClass == null) {
                logger.error("未找到对应的connector类, channel={}", channel);
                return null;
            }

            // 通过反射获取字段值
            String fieldName = ConnectorTypeRegistry.getFieldName(channel);
            Field field = connectParams.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(connectParams);

            // 操作完成后恢复访问控制
            field.setAccessible(false);

            if (value == null) {
                return null;
            }

            // 创建新实例并复制属性
            BaseConnectorVo newInstance = connectorClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(value, newInstance);
            return newInstance;
        } catch (NoSuchFieldException e) {
            logger.error("error field: fieldName={}, error={}", ConnectorTypeRegistry.getFieldName(channel), e.getMessage());
        } catch (IllegalAccessException e) {
            logger.error("error field:: fieldName={}, error={}", ConnectorTypeRegistry.getFieldName(channel), e.getMessage());
        } catch (Exception e) {
            logger.error("error field: vofail: {}", e.getMessage(), e);
        }
        return null;
    }
}