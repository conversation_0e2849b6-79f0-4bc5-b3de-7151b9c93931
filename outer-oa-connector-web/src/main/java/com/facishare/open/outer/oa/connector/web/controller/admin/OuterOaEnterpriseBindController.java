package com.facishare.open.outer.oa.connector.web.controller.admin;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.web.model.Result;
import com.facishare.open.outer.oa.connector.web.result.PageResult;
import com.facishare.open.outer.oa.connector.web.service.OuterOaEnterpriseBindService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/admin/enterprise")
// IgnoreI18nFile
public class OuterOaEnterpriseBindController {

    @Autowired
    private OuterOaEnterpriseBindService outerOaEnterpriseBindService;

    @GetMapping("/list")
    public Result<PageResult<OuterOaEnterpriseBindEntity>> list(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String fsEa,
            @RequestParam(required = false) String outerEa,
            @RequestParam(required = false) BindStatusEnum bindStatus) {
        try {
            PageResult<OuterOaEnterpriseBindEntity> result = outerOaEnterpriseBindService.queryList(page, size, fsEa, outerEa, bindStatus);
            return Result.newSuccess(result);
        } catch (Exception e) {
            log.error("查询企业绑定列表失败", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    @GetMapping("/{id}")
    public Result<OuterOaEnterpriseBindEntity> getById(@PathVariable Long id) {
        try {
            OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindService.getById(id);
            if (entity == null) {
                return Result.newError(ResultCodeEnum.OUTER_EMPDATA_NOT_EXISTS);
            }
            return Result.newSuccess(entity);
        } catch (Exception e) {
            log.error("查询企业绑定详情失败, id: {}", id, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    @PostMapping
    public Result<OuterOaEnterpriseBindEntity> add(@RequestBody OuterOaEnterpriseBindEntity entity) {
        try {
            OuterOaEnterpriseBindEntity result = outerOaEnterpriseBindService.save(entity);
            return Result.newSuccess(result);
        } catch (IllegalArgumentException e) {
            log.error("新增企业绑定参数错误", e);
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("新增企业绑定失败", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    @PutMapping("/{id}")
    public Result<OuterOaEnterpriseBindEntity> update(@PathVariable String id, @RequestBody OuterOaEnterpriseBindEntity entity) {
        try {
            entity.setId(id);
            OuterOaEnterpriseBindEntity result = outerOaEnterpriseBindService.save(entity);
            return Result.newSuccess(result);
        } catch (IllegalArgumentException e) {
            log.error("更新企业绑定参数错误", e);
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("更新企业绑定失败", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }

    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable Long id) {
        try {
            outerOaEnterpriseBindService.delete(id);
            return Result.newSuccess();
        } catch (IllegalArgumentException e) {
            log.error("删除企业绑定参数错误", e);
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("删除企业绑定失败", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
    }
} 