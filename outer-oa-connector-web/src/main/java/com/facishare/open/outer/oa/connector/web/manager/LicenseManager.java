package com.facishare.open.outer.oa.connector.web.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.LicenseEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.utils.GsonUtil;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringManager;
import com.facishare.paas.license.arg.JudgeModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.exception.PaasMessage;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.facishare.paas.license.pojo.ModuleFlag;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class LicenseManager {
    @Autowired
    private LicenseClient licenseClient;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Resource
    private EIEAConverter eieaConverter;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private RedisManager redisManager;

    /**
     * 检查企业的License 对于企业微信渠道，特殊处理；对于其他渠道，根据LicenseEnum判断模块权限
     *
     * @param fsEa 纷享企业账号
     * @return 包含企业绑定信息的结果
     */
    public Result<Void> checkLicense(String fsEa,ChannelEnum channelEnum) {

        // 转换EA为tenantId
        Integer tenantId = eieaConverter.enterpriseAccountToId(fsEa);
        String tenantIdStr = String.valueOf(tenantId);
        List<String> moduleCodes =null;
        if(ObjectUtils.isNotEmpty(channelEnum)){
             moduleCodes = Arrays.stream(ChannelEnum.values())
                    .filter(item -> ObjectUtils.isNotEmpty(item.getModuleCode())).map(ChannelEnum::getModuleCode)
                    .collect(Collectors.toList());
        }else {
            moduleCodes=Lists.newArrayList(channelEnum.getModuleCode());
        }
        // 获取所有启用了moduleCode的渠道
        List<OuterOaEnterpriseBindEntity> upsertEntities = Lists.newArrayList();
        if(ChannelEnum.qywx==channelEnum){
            // 企业微信特殊处理
            List<OuterOaEnterpriseBindEntity> entitiesByFsEa = outerOaEnterpriseBindManager
                    .getEntitiesByFsEa(ChannelEnum.qywx, fsEa);
            if (CollectionUtils.isEmpty(entitiesByFsEa)) {
                OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = convertConnectorVo(fsEa, tenantIdStr,
                        ChannelEnum.qywx);
                upsertEntities.add(outerOaEnterpriseBindEntity);
            }
        }
        List<ModuleFlag> moduleFlags = judgeConnectorModule(tenantIdStr, moduleCodes);
        // 返回检查结果
        if (moduleFlags.isEmpty()&&upsertEntities.isEmpty()) {
            return Result.newError(ResultCodeEnum.LICENSE_NO_AVAILABLE);
        }

        for (ModuleFlag moduleFlag : moduleFlags) {
            if (moduleFlag.isFlag()) {
                channelEnum = ChannelEnum.getByCode(moduleFlag.getModuleCode());
                if (channelEnum == null) {
                    continue;
                }
                // 这里只初始化渠道下单，还没初始化的时候
                List<OuterOaEnterpriseBindEntity> channelEnumEntities = outerOaEnterpriseBindManager
                        .getEntitiesByFsEa(channelEnum, fsEa);
                if (CollectionUtils.isNotEmpty(channelEnumEntities)) {
                    continue;
                }
                OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = convertConnectorVo(fsEa, tenantIdStr,
                        channelEnum);
                upsertEntities.add(outerOaEnterpriseBindEntity);

            }
        }
        if (!upsertEntities.isEmpty()) {
            //这里需要redis,避免重复新增
//            redisManager.validRefreshUserDataLock()

            outerOaEnterpriseBindManager.batchUpsert(upsertEntities);
        }
        // 返回企业微信的绑定实体作为默认结果
        return Result.newSuccess();
    }

    private OuterOaEnterpriseBindEntity convertConnectorVo(String fsEa, String tenantIdStr, ChannelEnum channelEnum) {
        // 获取绑定信息
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(channelEnum,
                fsEa, null, null);

        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntity)) {
            outerOaEnterpriseBindEntity = getOuterOaEnterpriseBindEntity(fsEa, tenantIdStr, channelEnum);
        }
        return outerOaEnterpriseBindEntity;
    }



    public OuterOaEnterpriseBindEntity getOuterOaEnterpriseBindEntity(String fsEa, String tenantIdStr, ChannelEnum channelEnum) {
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = null;
        try {
            // 获取对应渠道的VO类
            Class<? extends BaseConnectorVo> voClass = channelEnum.getClassName();
            if (voClass != null) {
                String dataCenterId = IdGenerator.get();
                // 使用反射创建实例
                BaseConnectorVo connectorVo = voClass.getDeclaredConstructor().newInstance();
                connectorVo.setChannel(channelEnum);
                String connectorName = i18NStringManager.getByEi(channelEnum.getI18nKey(), tenantIdStr,
                        channelEnum.getEnumName());
                connectorVo.setConnectorName(channelEnum.getEnumName());
                connectorVo.setConnectorName(connectorName);
                connectorVo.setDataCenterId(dataCenterId);
                // 创建绑定实体
                outerOaEnterpriseBindEntity = OuterOaEnterpriseBindEntity.builder().id(dataCenterId)
                        .channel(channelEnum).fsEa(fsEa).bindStatus(BindStatusEnum.create)
                        .bindType(BindTypeEnum.manual).createTime(System.currentTimeMillis())
                        .updateTime(System.currentTimeMillis()).connectInfo(GsonUtil.toJson(connectorVo)).build();

            }
        } catch (Exception e) {
            log.error("Failed to create connector instance for channel: {}, error: {}", channelEnum, e.getMessage(),
                    e);
        }
        return outerOaEnterpriseBindEntity;
    }

    /**
     * 判断连接器模块License
     * 
     * @param tenantId    租户ID
     * @param moduleCodes 模块代码列表
     * @return 模块权限标志列表
     */
    private List<ModuleFlag> judgeConnectorModule(String tenantId, List<String> moduleCodes) {
        JudgeModuleArg judgeModuleArg = new JudgeModuleArg();
        LicenseContext context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId(tenantId);
        judgeModuleArg.setContext(context);
        judgeModuleArg.setModuleCodes(moduleCodes);
        List<ModuleFlag> moduleFlags = Lists.newArrayList();
        try {
            com.facishare.paas.license.common.Result result = licenseClient.judgeModule(judgeModuleArg);
            log.info("judgeConnectorModule,arg:{},result:{}", judgeModuleArg, result);
            if (result.getErrCode() == PaasMessage.SUCCESS.getCode()) {
                moduleFlags = ((JudgeModulePojo) result.getResult()).getModuleFlags();
            }
        } catch (Exception e) {
            log.error("judgeConnectorModule exception for moduleCode: {}, error: {}", moduleCodes, e.getMessage(), e);
        }
        log.info("judgeConnectorModule,moduleCode={},moduleFlag={}", moduleCodes, moduleFlags);
        return moduleFlags;
    }
}
